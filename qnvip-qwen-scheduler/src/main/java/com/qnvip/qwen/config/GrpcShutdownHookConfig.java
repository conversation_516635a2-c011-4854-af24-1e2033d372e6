package com.qnvip.qwen.config;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import io.grpc.Server;

@Component
public class GrpcShutdownHookConfig implements ApplicationListener<ContextClosedEvent> {
    @Autowired(required = false) // 如果gRPC未启用则不注入
    private Server grpcServer;

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        if (grpcServer != null && !grpcServer.isShutdown()) {
            System.out.println("Shutting down gRPC server...");
            grpcServer.shutdown(); // 优雅关闭
            try {
                grpcServer.awaitTermination(30, TimeUnit.SECONDS); // 等待30秒
                if (!grpcServer.isTerminated()) {
                    grpcServer.shutdownNow(); // 强制关闭
                }
            } catch (InterruptedException e) {
                grpcServer.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
