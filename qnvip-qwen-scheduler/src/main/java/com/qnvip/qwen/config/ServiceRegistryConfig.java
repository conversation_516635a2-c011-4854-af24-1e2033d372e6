// package com.qnvip.qwen.config;
//
// import java.net.URI;
// import java.util.Collections;
// import java.util.Map;
//
// import javax.annotation.Resource;
//
// import org.springframework.boot.actuate.context.ShutdownEndpoint;
// import org.springframework.cloud.client.serviceregistry.Registration;
// import org.springframework.cloud.client.serviceregistry.ServiceRegistry;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class ServiceRegistryConfig {
//
// @Resource
// private ShutdownEndpoint shutdownEndpoint;
//
// @Bean
// public ServiceRegistry<SingleTonRegistration> serviceRegistry() {
// return new ServiceRegistry<SingleTonRegistration>() {
//
// @Override
// public void register(SingleTonRegistration registration) {
//
// }
//
// @Override
// public void deregister(SingleTonRegistration registration) {
//
// }
//
// @Override
// public void close() {
// shutdownEndpoint.shutdown();
// }
//
// @Override
// public void setStatus(SingleTonRegistration registration, String status) {
//
// }
//
// @Override
// public <T> T getStatus(SingleTonRegistration registration) {
// return null;
// }
// };
// }
//
// public class SingleTonRegistration implements Registration {
//
// @Override
// public String getServiceId() {
// return "";
// }
//
// @Override
// public String getHost() {
// return "";
// }
//
// @Override
// public int getPort() {
// return 0;
// }
//
// @Override
// public boolean isSecure() {
// return false;
// }
//
// @Override
// public URI getUri() {
// return null;
// }
//
// @Override
// public Map<String, String> getMetadata() {
// return Collections.emptyMap();
// }
// }
// }
