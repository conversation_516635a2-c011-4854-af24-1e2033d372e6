package com.qnvip.qwen.jobhandler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.qnvip.qwen.bizService.ApiDocSyncBizService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * lichaojie
 */
@Slf4j
@Component
public class ApiSyncJobHandler {

    @Resource
    private ApiDocSyncBizService apiDocSyncBizService;

    /**
     * api知识库同步
     */
    @XxlJob("apiBookAndDataSyncJobHandler")
    public ReturnT<String> apiBookAndDataSyncJobHandler(String s) {
        apiDocSyncBizService.syncBook();
        apiDocSyncBizService.syncBookToc();
        apiDocSyncBizService.syncBookData();
        return ReturnT.SUCCESS;
    }

}
