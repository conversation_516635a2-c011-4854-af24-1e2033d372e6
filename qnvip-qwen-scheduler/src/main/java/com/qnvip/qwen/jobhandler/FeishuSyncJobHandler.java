package com.qnvip.qwen.jobhandler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.qnvip.qwen.bizService.FeishuDocSyncBizService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * lichaojie
 */
@Slf4j
@Component
public class FeishuSyncJobHandler {

    @Resource
    private FeishuDocSyncBizService feiShuDocSyncBizService;

    /**
     * 飞书知识库同步
     */
    @XxlJob("feiShuBookSyncJobHandler")
    public ReturnT<String> feiShuBookSyncJobHandler(String s) {
        feiShuDocSyncBizService.syncBook();
        feiShuDocSyncBizService.syncBookToc();
        return ReturnT.SUCCESS;
    }

    /**
     * 飞书知识库文档同步
     */
    @XxlJob("feiShuBookDocSyncJobHandler")
    public ReturnT<String> feiShuBookDocSyncJobHandler(String s) {
        feiShuDocSyncBizService.syncBookData();
        return ReturnT.SUCCESS;
    }

}
