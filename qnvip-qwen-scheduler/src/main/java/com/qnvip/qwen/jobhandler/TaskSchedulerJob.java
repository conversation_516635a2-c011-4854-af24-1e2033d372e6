package com.qnvip.qwen.jobhandler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.bizService.TaskDomainBizService;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.FileTaskProgressDaoService;
import com.qnvip.qwen.dal.dto.FileTaskProgressJobQueryDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.enums.TaskStatusEnum;
import com.qnvip.qwen.service.FileTaskProgressService;
import com.qnvip.qwen.util.ThreadPoolUtil;
import com.qnvip.qwen.vo.TaskHandlerVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TaskSchedulerJob implements InitializingBean {
    private static final Integer TASK_LIMIT = 100;
    ExecutorService threadPool;
    @Resource
    private TaskDomainBizService taskDomainBizService;
    @Resource
    private FileTaskProgressDaoService fileTaskProgressDaoService;
    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private FileTaskProgressService fileTaskProgressService;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.threadPool = ThreadPoolUtil.createThreadPool(10, 10, 100);
    }

    @XxlJob("scheduleTasks")
    public ReturnT<String> scheduleTasks(String msg) {
        log.info("开始调度任务调度 {}", msg);

        Long bookId = null;
        List<Integer> steps = null;
        boolean nextTaskFlag;
        Long taskId = null;
        if (StringUtils.isNotEmpty(msg)) {
            TaskHandlerVO handlerVO = JSONUtil.toBean(msg, TaskHandlerVO.class);
            bookId = handlerVO.getBookId();
            steps = handlerVO.getSteps();
            nextTaskFlag = handlerVO.isNextTaskFlag();
            taskId = handlerVO.getTaskId();
        } else {
            nextTaskFlag = true;
        }
        FileTaskProgressJobQueryDTO query = new FileTaskProgressJobQueryDTO();
        query.setBookId(bookId);
        query.setStatus(TaskStatusEnum.PENDING.getCode());
        query.setLimit(TASK_LIMIT);
        query.setSetpList(steps);
        query.setId(taskId);
        List<Long> taskIds = fileTaskProgressDaoService.findByStatusAndStepList(query);

        if (CollUtil.isEmpty(taskIds)) {
            log.info("结束调度任务调度 {}", msg);
            return ReturnT.SUCCESS;
        }

        List<Future<?>> futures = new ArrayList<>();
        for (Long id : taskIds) {
            Future<?> future = threadPool.submit(() -> {
                try {
                    taskDomainBizService.processTask(id, nextTaskFlag);
                } catch (Exception e) {
                    log.error("处理任务失败", e);
                }
            });
            futures.add(future);
        }

        // 等待所有任务完成
        try {
            for (Future<?> future : futures) {
                future.get();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("任务执行被中断", e);
            return ReturnT.FAIL;
        } catch (ExecutionException e) {
            log.error("任务执行出错", e.getCause());
        }

        log.info("结束调度任务调度 {}", msg);
        return ReturnT.SUCCESS;
    }

    @XxlJob("taskInitJobHandler")
    public ReturnT<String> taskInitJobHandler(String msg) {
        log.info("开始调度任务调度 {}", msg);
        if (StringUtils.isEmpty(msg)) {
            return ReturnT.SUCCESS;
        }
        List<Long> bookIds = JSONUtil.toList(msg, Long.class);
        for (Long bookId : bookIds) {
            List<FileOriginDO> fileOriginIds = fileOriginDaoService.getListByBookId(bookId);
            List<Long> fileOriginIdList = fileOriginIds.stream().map(FileOriginDO::getId).collect(Collectors.toList());

            fileTaskProgressService.saveTaskStartWithFormat(bookId, fileOriginIdList);
        }

        log.info("结束调度任务调度 {}", msg);
        return ReturnT.SUCCESS;
    }

    @XxlJob("taskInitByFileOriginIdsJobHandler")
    public ReturnT<String> taskInitByFileOriginIdsJobHandler(String msg) {
        log.info("开始调度任务调度 {}", msg);
        if (StringUtils.isEmpty(msg)) {
            return ReturnT.SUCCESS;
        }
        List<Long> fileOriginIds = JSONUtil.toList(msg, Long.class);
        List<FileOriginDO> fileOriginDOS = fileOriginDaoService.listByIds(fileOriginIds);
        if (CollUtil.isEmpty(fileOriginDOS)) {
            return ReturnT.SUCCESS;
        }
        Map<Long, List<FileOriginDO>> bookMap =
            fileOriginDOS.stream().collect(Collectors.groupingBy(FileOriginDO::getBookId));
        for (Map.Entry<Long, List<FileOriginDO>> entry : bookMap.entrySet()) {
            Long bookId = entry.getKey();
            List<FileOriginDO> fileOriginDOList = entry.getValue();
            List<Long> fileOriginIdList =
                fileOriginDOList.stream().map(FileOriginDO::getId).collect(Collectors.toList());
            fileTaskProgressService.saveTaskStartWithFormat(bookId, fileOriginIdList);
        }

        log.info("结束调度任务调度 {}", msg);
        return ReturnT.SUCCESS;
    }

    @XxlJob("qaProcessJobHandler")
    public ReturnT<String> qaProcessJobHandler(String msg) {
        log.info("开始调度任务调度 {}", msg);

        Long bookId = null;
        boolean nextTaskFlag;
        if (StringUtils.isNotEmpty(msg)) {
            TaskHandlerVO handlerVO = JSONUtil.toBean(msg, TaskHandlerVO.class);
            bookId = handlerVO.getBookId();
            nextTaskFlag = handlerVO.isNextTaskFlag();
        } else {
            nextTaskFlag = true;
        }


        List<Integer> steps = Arrays.asList(60);
        FileTaskProgressJobQueryDTO query = new FileTaskProgressJobQueryDTO();
        query.setBookId(bookId);
        query.setStatus(TaskStatusEnum.PENDING.getCode());
        query.setLimit(100);
        query.setSetpList(steps);

        while (true) {
            List<Long> taskIds = fileTaskProgressDaoService.findByStatusAndStepList(query);

            if (CollUtil.isEmpty(taskIds)) {
                break;
            }
            for (Long taskId : taskIds) {
                taskDomainBizService.processTask(taskId, nextTaskFlag);
            }
        }

        log.info("结束调度任务调度 {}", msg);
        return ReturnT.SUCCESS;
    }

}
