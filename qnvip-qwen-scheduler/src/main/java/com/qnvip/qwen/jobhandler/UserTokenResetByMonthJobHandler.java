package com.qnvip.qwen.jobhandler;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qnvip.qwen.service.UserTokenConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UserTokenResetByMonthJobHandler {

    @Resource
    private UserTokenConfigService userTokenConfigService;

    @XxlJob("userTokenResetByMonth")
    public ReturnT<String> userTokenResetByMonth(String msg) {
        log.info("开始调度任务调度 {}", msg);

        try {
            Boolean result = userTokenConfigService.resetAllUsedToken();
            if (result) {
                log.info("重置用户token使用量成功");
            } else {
                log.error("重置用户token使用量失败");
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("重置用户token使用量发生异常", e);
            return ReturnT.FAIL;
        }

        log.info("结束调度任务调度 {}", msg);
        return ReturnT.SUCCESS;
    }
}
