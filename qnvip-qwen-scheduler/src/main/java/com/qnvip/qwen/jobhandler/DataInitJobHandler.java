package com.qnvip.qwen.jobhandler;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.qnvip.qwen.bizService.EsDataInitBizService;
import com.qnvip.qwen.bizService.MilvusDataInitBizService;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.enums.MilvusInitTypeEnum;
import com.qnvip.qwen.service.FileTaskProgressService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月15日 14:38:00
 */
@Slf4j
@Component
public class DataInitJobHandler {

    @Resource
    private EsDataInitBizService esDataInitBizService;
    @Resource
    private MilvusDataInitBizService milvusDataInitBizService;
    @Resource
    private TeamBookDaoService teamBookDaoService;
    @Resource
    private TeamBookDocDaoService teamBookDocDaoService;
    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;
    @Resource
    private FileTaskProgressService fileTaskProgressService;


    /**
     * 数据库数据初始化到ES
     */
    @XxlJob("esInitJobHandler")
    public ReturnT<String> esInitJobHandler(String s) {
        DataInitSearchDTO param = new DataInitSearchDTO();
        if (StringUtils.isNotEmpty(s)) {
            param = JSONUtil.toBean(s, DataInitSearchDTO.class);
        }

        // 初始化文档切块数据
        esDataInitBizService.initBookChunk(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 数据库数据初始化到milvus
     */
    @XxlJob("milvusDataInitJobHandler")
    public ReturnT<String> milvusDataInitJobHandler(String s) {
        if (ObjectUtils.isEmpty(s)) {
            return new ReturnT(ReturnT.FAIL_CODE, "initTypes必传  2切块 3qa_link  4graph 如：{\"initTypes\":[2,3,4]}");
        }
        DataInitSearchDTO param = JSONUtil.toBean(s, DataInitSearchDTO.class);

        // chunk初始化
        if (param.getInitTypes().contains(MilvusInitTypeEnum.CHUNKING.getCode())) {
            milvusDataInitBizService.initBookChunk(param);
        }

        // qa初始化
        if (param.getInitTypes().contains(MilvusInitTypeEnum.QA_LINK.getCode())) {
            milvusDataInitBizService.initBookQaLink(param);
        }
        if (param.getInitTypes().contains(MilvusInitTypeEnum.GRAPH.getCode())) {
            milvusDataInitBizService.initGraphAndRelation(param);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 删除团队数据
     */
    @XxlJob("deleteTeamDataJobHandler")
    public ReturnT<String> deleteTeamDataJobHandler(String s) {
        if (StringUtils.isEmpty(s)) {
            return ReturnT.SUCCESS;
        }
        Long teamId = Long.valueOf(s);

        List<TeamBookDocDO> todoList = new ArrayList<>();

        // 删除知识库
        List<TeamBookDO> deleteBooks = teamBookDaoService.getListByTeamId(teamId);
        if (CollUtil.isNotEmpty(deleteBooks)) {
            List<Long> bookIds = deleteBooks.stream().map(TeamBookDO::getId).collect(Collectors.toList());
            teamBookDaoService.deleteByIds(bookIds);

            // 删除知识库关联的文档
            List<TeamBookDocDO> docList = teamBookDocDaoService.getListByBookIds(bookIds);
            if (CollUtil.isNotEmpty(docList)) {
                todoList.addAll(docList);
            }
        }

        if (CollUtil.isEmpty(todoList)) {
            return ReturnT.SUCCESS;
        }
        List<Long> docIds = todoList.stream().map(TeamBookDocDO::getId).distinct().collect(Collectors.toList());
        List<String> docUids = todoList.stream().map(TeamBookDocDO::getUid).distinct().collect(Collectors.toList());
        List<FileOriginDO> fileOriginDOS = fileOriginDaoService.getListByDocUids(docUids);
        teamBookDocDaoService.deleteByIds(docIds);
        teamBookDocDataDaoService.deleteByDocUIds(docUids);

        if (CollUtil.isNotEmpty(fileOriginDOS)) {
            List<Long> fileIds = fileOriginDOS.stream().map(FileOriginDO::getId).collect(Collectors.toList());
            fileOriginDaoService.deleteByDocUids(docUids);
            // 删除任务
            fileTaskProgressService.deleteByFileOriginIds(fileIds);
        }
        return ReturnT.SUCCESS;
    }
}
