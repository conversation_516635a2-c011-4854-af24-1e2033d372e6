package com.qnvip.qwen.jobhandler;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.qnvip.qwen.bizService.YuQueDocSyncBizService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月27日 10:50:00
 */
@Slf4j
@Component
public class YuQueSyncJobHandler {

    @Resource
    private YuQueDocSyncBizService yuQueDocSyncBizService;

    /**
     * 语雀知识库同步
     */
    @XxlJob("yuQueBookSyncJobHandler")
    public ReturnT<String> yuQueBookSyncJobHandler(String s) {
        yuQueDocSyncBizService.syncBook();
        yuQueDocSyncBizService.syncBookToc();
        return ReturnT.SUCCESS;
    }

    /**
     * 语雀知识库文档同步
     */
    @XxlJob("yuQueBookDocSyncJobHandler")
    public ReturnT<String> yuQueBookDocSyncJobHandler(String s) {
        yuQueDocSyncBizService.syncBookData();
        return ReturnT.SUCCESS;
    }

    /**
     * 语雀删除知识库和文档
     */
    @XxlJob("yuQueDeleteJobHandler")
    public ReturnT<String> yuQueDeleteJobHandler(String s) {
        // 默认10天前的数据
        LocalDateTime time = LocalDateTime.now().minusDays(10);
        if (StringUtils.isNotEmpty(s)) {
            time = LocalDateTime.parse(s, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        yuQueDocSyncBizService.processDelete(time);
        return ReturnT.SUCCESS;
    }
}
