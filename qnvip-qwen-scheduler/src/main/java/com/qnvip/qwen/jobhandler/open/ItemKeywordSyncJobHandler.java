package com.qnvip.qwen.jobhandler.open;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.qnvip.qwen.bizService.ItemKeywordBizService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月26日 15:22:00
 */
@Slf4j
@Component
public class ItemKeywordSyncJobHandler {
    @Resource
    private ItemKeywordBizService itemKeywordBizService;

    @XxlJob("itemKeywordSyncJobHandler")
    public ReturnT<String> itemKeywordSyncJobHandler(String s) {
        itemKeywordBizService.syncKeyword();
        return ReturnT.SUCCESS;
    }
}
