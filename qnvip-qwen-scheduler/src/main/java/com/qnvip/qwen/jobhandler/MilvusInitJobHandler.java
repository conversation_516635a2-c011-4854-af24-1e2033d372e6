package com.qnvip.qwen.jobhandler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.qnvip.qwen.dal.milvus.MilvusChunkDocService;
import com.qnvip.qwen.dal.milvus.MilvusGraphEntityService;
import com.qnvip.qwen.dal.milvus.MilvusGraphRelationService;
import com.qnvip.qwen.dal.milvus.MilvusItemKeywordService;
import com.qnvip.qwen.dal.milvus.MilvusQaLinkService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月27日 10:50:00
 */
@Slf4j
@Component
public class MilvusInitJobHandler {

    @Resource
    private MilvusQaLinkService milvusQaLinkService;
    @Resource
    private MilvusChunkDocService milvusChunkDocService;

    @Resource
    private MilvusItemKeywordService milvusItemKeywordService;

    @Resource
    private MilvusGraphEntityService milvusGraphEntityService;

    @Resource
    private MilvusGraphRelationService milvusGraphRelationService;
    /**
     * milvus 数据库初始化
     */
    @XxlJob("milvusInitJobHandler")
    public ReturnT<String> milvusInitJobHandler(String s) {
        milvusItemKeywordService.createDefaultCollection();
        milvusChunkDocService.createDefaultCollection();
        milvusQaLinkService.createDefaultCollection();
        milvusGraphEntityService.createDefaultCollection();
        milvusGraphRelationService.createDefaultCollection();
        log.info("finish milvusInitJobHandler");
        return ReturnT.SUCCESS;
    }


}
