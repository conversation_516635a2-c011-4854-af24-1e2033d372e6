package com.qnvip.qwen;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import lombok.extern.slf4j.Slf4j;

/**
 * ScaffoldApplication
 *
 * <AUTHOR>
 * @since 2024/1/15
 */
@Slf4j
@MapperScan("com.qnvip.qwen.dal.mapper")
@SpringBootApplication
public class JobAppApplication {
    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        SpringApplication.run(JobAppApplication.class, args);
        log.info("================ JobApplication start success 耗时: {}ms================",
            System.currentTimeMillis() - start);
    }
}
