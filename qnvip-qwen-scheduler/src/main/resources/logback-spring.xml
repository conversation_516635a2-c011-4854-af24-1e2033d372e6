<?xml version="1.0" encoding="UTF-8"?>
<!-- logback 配置 -->
<configuration scan="false">

    <!-- add converter for %tid -->
    <conversionRule conversionWord="tid"
                    converterClass="org.apache.skywalking.apm.toolkit.log.logback.v1.x.LogbackPatternConverter"/>
    <!-- add converter for %sw_ctx -->
    <conversionRule conversionWord="sw_ctx"
                    converterClass="org.apache.skywalking.apm.toolkit.log.logback.v1.x.LogbackSkyWalkingContextPatternConverter"/>

    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- Expose the properties from the file generated by git-commit-id-plugin -->
    <!--    <property resource="git.properties"/>-->

    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSSXXX}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%X{tid}]){yellow} %clr([%X{traceId}]){yellow} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="CONSOLE_LOG_CHARSET" value="${CONSOLE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>
    <property name="FILE_LOG_PATTERN"
              value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSSXXX}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%X{tid}] [%X{traceId}] [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_CHARSET" value="${FILE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>

    <logger name="org.apache.catalina.startup.DigesterFactory" level="ERROR"/>
    <logger name="org.apache.catalina.util.LifecycleBase" level="ERROR"/>
    <logger name="org.apache.coyote.http11.Http11NioProtocol" level="WARN"/>
    <logger name="org.apache.sshd.common.util.SecurityUtils" level="WARN"/>
    <logger name="org.apache.tomcat.util.net.NioSelectorPool" level="WARN"/>
    <logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="ERROR"/>
    <logger name="org.hibernate.validator.internal.util.Version" level="WARN"/>
    <logger name="org.springframework.boot.actuate.endpoint.jmx" level="WARN"/>

    <logger name="org.springframework.context.support" level="warn"/>
    <logger name="org.springframework.test.annotation.ProfileValueUtils" level="warn"/>
    <logger name="com.alibaba" level="warn"/>
    <logger name="org.apache.dubbo" level="warn"/>

    <logger name="org.apache.dubbo.registry.integration.InterfaceCompatibleRegistryProtocol" level="error"/>
    <logger name="org.apache.dubbo.registry.client.ServiceDiscoveryRegistry" level="error"/>
    <logger name="com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder" level="error"/>

    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <springProperty scope="context" name="spring.application.group" source="spring.application.group"
                    defaultValue="qnvip"/>
    <property name="LOG_FILE"
              value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/${spring.application.name:-spring}.log}"/>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- %i为文件按照maxFileSize大小规定轮转后的序号 -->
            <!-- 后缀以".zip"或".gz"结尾，则开启日志文件压缩 -->
            <fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_FILE}.%d{yyyy-MM-dd}.%i.log}
            </fileNamePattern>
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <!-- 单个日志文件最大大小，当文件达到该大小则触发截断（以及压缩）-->
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-100MB}</maxFileSize>
            <!-- 日志文件保留最大时间滚动周期，比如当filaNamePattern中%d以为dd结尾时-->
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-7}</maxHistory>
            <!-- 日志文件保留的总的最大大小-->
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-1GB}</totalSizeCap>
        </rollingPolicy>

        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <fieldNames class="net.logstash.logback.fieldnames.ShortenedFieldNames"/>
            <includeContext>false</includeContext>
            <includeCallerData>true</includeCallerData>
            <customFields>{"module":"${spring.application.name}", "group":"${spring.application.group}"}</customFields>
            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                <!-- 值可以是 full或者short或者int值，表示每个异常最多打印多少个 stackTraceElements 元素 -->
                <rootCauseFirst>true</rootCauseFirst>
            </throwableConverter>
            <provider class="net.logstash.logback.composite.loggingevent.StackHashJsonProvider">
                <fieldName>stackHash</fieldName>
            </provider>
            <provider class="net.logstash.logback.composite.loggingevent.ThrowableClassNameJsonProvider">
                <fieldName>throwableClass</fieldName>
                <useSimpleClassName>false</useSimpleClassName>
            </provider>
            <provider class="net.logstash.logback.composite.loggingevent.ThrowableRootCauseClassNameJsonProvider">
                <fieldName>throwableRootCauseClass</fieldName>
                <useSimpleClassName>false</useSimpleClassName>
            </provider>
            <provider class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.logstash.TraceIdJsonProvider"/>
            <provider
                    class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.logstash.SkyWalkingContextJsonProvider"/>
        </encoder>
    </appender>

    <appender name="ASYNC" class="net.logstash.logback.appender.LoggingEventAsyncDisruptorAppender">
        <appender-ref ref="FILE"/>
    </appender>


    <!-- 指定日志输出的级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>