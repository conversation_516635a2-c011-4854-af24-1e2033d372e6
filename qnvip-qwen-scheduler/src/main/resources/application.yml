spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: '@spring.profiles.active@'
  application:
    name: qnvip-qwen-scheduler
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  data:
    neo4j:
      uri: bolt://***********:7687
      username: neo4j
      password: password

management:
  server:
    port: 16013
  endpoints:
    web:
      exposure:
        include: [ 'health', 'info', 'loggers', 'prometheus','shutdown' ]
  endpoint:
    health:
      show-details: ALWAYS  #显示详细信息
    shutdown:
      enabled: true
  health:
    elasticsearch:
      enabled: false


logging.file.name: ./logs/${spring.application.name}.log
logging:
  level:
    io.milvus.client: WARN  # Milvus客户端日志级别



qiniu:
  accessKey: lyv3sG4CZpJczTvoI86_wIeMV3KpzELYAfdE3xSJ
  secretKey: M7uGkx7NXsjpXfR7Y6hp1yPgOw8Fe1GyboBrTEd_
  publicDomain: https://upload-test.qnvipmall.com/
  publicBucketName: qnvip-qa-pub



