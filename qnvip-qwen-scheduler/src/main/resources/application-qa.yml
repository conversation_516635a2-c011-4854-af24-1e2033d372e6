server:
  port: 6013           # 应用端口
  shutdown: graceful

spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************
    username: hroot
    password: HQnvip@123
    hikari:
      maximum-pool-size: 50            # 最大连接数，按应用实际情况设置
      minimum-idle: 10                 # 最小连接数，按应用实际情况设置
      connection-test-query: SELECT 1
  redis:
    host: redis-test.qnvip.io
    password: TesT2023Qn,,
    port: 6379
    database: 5
    timeout: 20000
    jedis:
      pool: # 连接池最大连接数（使用负值表示没有限制）
        max-active: 200
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
  data:
    neo4j:
      uri: bolt://***********:7687
      username: neo4j
      password: password
# es 密码Hzi4u0EmDNBq
elasticsearch:
  hostAndPort: "***********:9200"
  username: "elastic"
  password: "elastic@2021"

# milvus 密码QPVNiM1ClSvo
milvus:
  ipAddr: ***********
  port: 19530
  embeddingClientUrl: http://*************:8001/get_text_embedding
  username:
  password:

swagger:
  # 是否启用swagger,默认值：false，生产环境禁止开启
  enabled: false
  # 默认值：接口文档
  group-name: 后台页面
  # 接口扫描路径,默认值：com.qnvip
  scan-package: com.qnvip.qwen.controller
  # 默认值：接口文档
  description: app接口文档
  # 默认值：1.0
  version: 1.0

xxl:
  job:
    admin:
      addresses: http://job2.qa.youpinhaoche.com
      username: admin
      password: 123456
    executor:
      appname: qwen-job-executor
      apptitle: ''
      ip:
      port: 17013
      logpath: /logs/xxl-job/jobhandler
      logretentiondays: 3
    accessToken:

chunk:
  length: 500
  overlap: 100
  boost: 2.5
  host: http://***********:8002

rerank:
  host: http://*************:8002
  count: 3
  highLimit: 0.25
  lowLimit: 0.25

llm:
  apis:
    bytepro256:
      url: https://ark.cn-beijing.volces.com/api/v3/chat/completions
      apiKey: 7471f763-54af-4382-9285-ac0994fbe08f
      modelName: ep-20250324174654-5c8b2
    ollamaQwen72b:
      url:
      apiKey:
      modelName:

wechat:
  corpid: wwecdcda1ec54a9e69
  secret: U-Lkq0A5GLpkF1GIcpifBOSaMhYOtUJahxpPNx5vPwU
  redirectUrl: https://chat-web.qnvip.com

fileSystem:
  host: http://***********:9113

open:
  host:
    rent: https://rent-third-api-qa-01.qnvipmall.com/
    merchant: https://stage-third-api-qa-01.qnvipmall.com/
