package com.qnvip.qwen;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.qnvip.qwen.bizService.impl.task.processor.TaskProcessorChunk;
import com.qnvip.qwen.bizService.impl.task.processor.TaskProcessorQA;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.jobhandler.TaskSchedulerJob;
import com.qnvip.qwen.service.FileTaskProgressService;

public class TaskSchedulerJobTest extends BaseTest {

    @Resource
    TaskSchedulerJob taskSchedulerJob;
    @Resource
    private TaskProcessorQA taskProcessorQA;
    @Resource
    private TaskProcessorChunk taskProcessorChunk;
    @Resource
    private FileOriginDaoService fileOriginDaoService;

    @Resource
    private FileTaskProgressService fileTaskProgressService;

    @Test
    public void saveTaskStartWithCleanTest() {
        taskSchedulerJob.scheduleTasks("");
    }

    @Test
    public void testTaskQA() {
        FileOriginDO fileOriginDO = fileOriginDaoService.getById(1427L);
        TaskProgressDomainModelDTO task = new TaskProgressDomainModelDTO();
        task.setFileOriginId(fileOriginDO.getId());
        task.setFileOriginDO(fileOriginDO);
        taskProcessorQA.execute(task);
    }

    @Test
    public void testTaskChunk() {
        FileOriginDO fileOriginDO = fileOriginDaoService.getById(942L);
        TaskProgressDomainModelDTO task = new TaskProgressDomainModelDTO();
        task.setFileOriginId(fileOriginDO.getId());
        task.setFileOriginDO(fileOriginDO);
        taskProcessorChunk.execute(task);
    }

    @Test
    public void testSaveTaskProgress() {
        List<FileOriginDO> fileOriginIds = fileOriginDaoService.getListByBookId(1L);
        List<Long> fileOriginIdList = fileOriginIds.stream().map(FileOriginDO::getId).collect(Collectors.toList());

        fileTaskProgressService.saveTaskStartWithFormat(1L, fileOriginIdList);
    }

    @Test
    public void testDeleteTaskProgress() {
        List<Long> fileOriginIdList = Arrays.asList(698L);
        fileTaskProgressService.deleteByFileOriginIds(fileOriginIdList);
    }

}
