package com.qnvip.qwen;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.alibaba.fastjson.JSON;
import com.qnvip.qwen.bizService.AIChatBizService;
import com.qnvip.qwen.enums.PromptEnum;

public class AIChatServiceTest extends BaseTest {

    @Resource
    private AIChatBizService aiChatBizService;

    @Test
    public void test() {
        for (int i = 0; i < 10; i++) {
            String prompt = PromptEnum.CHUNK_QA.replace("{article}", content);
            String chatModelResponse = aiChatBizService.chatBlockingJsonByLocal(prompt, null, null);
            boolean valid = JSON.isValid(chatModelResponse);
            if (!valid)
                System.out.println(chatModelResponse);
        }
    }

    @Test
    public void testChunkQA() {
        String content = "### 本周工作纪要\n" + "1. 老合同对接合同中心异步签署，进度，测试中 [@刘旭东](undefined/yuexiaxingqian)\n"
            + "2. 新合同流程，0213上线 [@刘旭东](undefined/yuexiaxingqian) [@张志远](undefined/nsnsttn-nxijt)";
        String prompt = PromptEnum.CHUNK_QA.replace("{article}", content);
        String chatModelResponse = aiChatBizService.chatBlockingJsonByLocal(prompt, PromptEnum.CHUNK_QA_FORMAT, null);
        System.out.println(chatModelResponse);
    }

    public static String content = "### 本周工作纪要\n" + "1. 老合同对接合同中心异步签署，进度，测试中 [@刘旭东](undefined/yuexiaxingqian)\n"
        + "2. 新合同流程，0213上线 [@刘旭东](undefined/yuexiaxingqian) [@张志远](undefined/nsnsttn-nxijt)";

}
