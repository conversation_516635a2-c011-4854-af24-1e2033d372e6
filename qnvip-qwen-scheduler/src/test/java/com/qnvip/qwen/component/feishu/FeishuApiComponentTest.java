package com.qnvip.qwen.component.feishu;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.qnvip.qwen.BaseTest;
import com.qnvip.qwen.component.feishu.response.ExportTaskDownloadResponse;
import com.qnvip.qwen.component.feishu.response.SpaceListResponse;
import com.qnvip.qwen.component.feishu.response.SpaceNodeListResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 飞书API组件单元测试
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2024/03/21
 */
@Slf4j
class FeishuApiComponentTest extends BaseTest {

    @Resource
    private FeishuApiComponent feishuApiComponent;

    @Test
    void testListSpace() {
        List<FeishuApiComponent.AppSecret> list = new ArrayList<>();
        FeishuApiComponent.AppSecret appSecret = new FeishuApiComponent.AppSecret();
        appSecret.setTeamId(7L);
        appSecret.setAppId("cli_a89002092f5b900d");
        appSecret.setAppSecret("A8DpPJZp2e8vzA8EmbPHifkUXtVRHlFG");
        list.add(appSecret);
        feishuApiComponent.init(list);

        // 准备测试数据
        Integer pageSize = 10;

        // 执行测试
        SpaceListResponse response = feishuApiComponent.listSpace(7L, pageSize);

        // 验证结果
        assertNotNull(response);
        log.info("获取空间列表响应: {}", JSON.toJSONString(response, JSONWriter.Feature.PrettyFormat));
    }

    @Test
    void testListSpaceNode() {
        // 准备测试数据
        String spaceId = "7503452944176906241";
        String parentNodeToken = "FbZkw6KotivHMdkxrWiccZ3fncX";
        Integer pageSize = 10;

        // 执行测试
        SpaceNodeListResponse response = feishuApiComponent.listSpaceNode(6L, spaceId, parentNodeToken, pageSize);

        // 验证结果
        assertNotNull(response);
        log.info("获取空间节点列表响应: {}", JSON.toJSONString(response, JSONWriter.Feature.PrettyFormat));
    }

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
    }

    @Test
    void testDownloadExportTask() {

        // 准备测试数据
        String fileToken = "AMgmdj895o5W84xiZXac3zEgnbb";
        String fileType = "docx";
        // 执行测试
        ExportTaskDownloadResponse response = feishuApiComponent.waitForCreateExportTask(6L, fileToken, fileType);

        // 验证结果
        assertNotNull(response);

        // 保存文件到本地
        try {
            // 创建下载目录
            Path downloadDir = Paths.get("C:\\Users\\<USER>\\Desktop\\downloads");
            log.info("下载地址: {}", downloadDir);
            if (!Files.exists(downloadDir)) {
                Files.createDirectories(downloadDir);
            }

            try (FileOutputStream outputStream = new FileOutputStream(
                downloadDir.resolve(response.getFileName() + "." + response.getFileExtension()).toFile())) {
                outputStream.write(response.getFileData());
            }

        } catch (IOException e) {
            log.error("保存文件失败", e);
        }
    }
}