server:
  port: 6012           # 应用端口
  shutdown: graceful

spring:
  cloud:
    nacos:
      discovery:
        ip: *************
  datasource:
    url: **************************************************************************************************************************************************************************
    username: ailab
    password: kK7%bi+3Ct^x
    hikari:
      maximum-pool-size: 50            # 最大连接数，按应用实际情况设置
      minimum-idle: 10                 # 最小连接数，按应用实际情况设置
      connection-test-query: SELECT 1
  redis:
    host: r-bp1ead7cf9cfb1c4.redis.rds.aliyuncs.com
    password: 5eq3ePGeyXtuWB
    port: 6379
    database: 10
    timeout: 20000
    jedis:
      pool: # 连接池最大连接数（使用负值表示没有限制）
        max-active: 200
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
  data:
    neo4j:
      uri: bolt://*************:7687
      username: neo4j
      password: password

# es 密码Hzi4u0EmDNBq
elasticsearch:
  hostAndPort: "es-cn-3ic46skwm001eax3y.elasticsearch.aliyuncs.com:9200"
  username: "elastic"
  password: "jM5&40P2+E"

# milvus 密码QPVNiM1ClSvo
milvus:
  ipAddr: c-566c09b510d1d7b6-internal.milvus.aliyuncs.com
  port: 19530
  embeddingClientUrl: http://*************:8001/get_text_embedding
  username: "root"
  password: "eU1-8dsvZ6"

embeddingClientUrl: http://*************:8000/get_text_embedding


swagger:
  # 是否启用swagger,默认值：false，生产环境禁止开启
  enabled: false
  # 默认值：接口文档
  group-name: 后台页面
  # 接口扫描路径,默认值：com.qnvip
  scan-package: com.qnvip.qwen.controller
  # 默认值：接口文档
  description: app接口文档
  # 默认值：1.0
  version: 1.0


chunk:
  length: 512
  overlap: 128
  boost: 2.5
  host: http://*************:8002

rerank:
  host: http://*************:8002
  count: 3
  highLimit: 0.25
  lowLimit: 0.25

llm:
  apis:
    bytepro256:
      url: https://ark.cn-beijing.volces.com/api/v3/chat/completions
      apiKey: 7471f763-54af-4382-9285-ac0994fbe08f
      modelName: ep-20250324174654-5c8b2
    ollamaQwen72b:
      url:
      apiKey:
      modelName:
  evaluate:
    evaluate-tags:
      # 正向标签
      - tagKey: ACCURATE_CONTENT
        tagValue: 内容准确
        tagGroup: up
      - tagKey: EASY_TO_UNDERSTAND
        tagValue: 易于理解
        tagGroup: up
      - tagKey: COMPLETE_CONTENT
        tagValue: 内容完善
        tagGroup: up
      - tagKey: OTHER_UP
        tagValue: 其他
        tagGroup: up
      # 负向标签
      - tagKey: FALSE_INFORMATION
        tagValue: 虚假信息
        tagGroup: down
      - tagKey: UNHELPFUL
        tagValue: 没有帮助
        tagGroup: down
      - tagKey: PRIVACY_RELATED
        tagValue: 隐私相关
        tagGroup: down
      - tagKey: OTHER_DOWN
        tagValue: 其他
        tagGroup: down




security:
  oauth2:
    client:
      client-id: qnvip-qwen
      client-secret: 8sfyGU8Q
      response-type: code
      url: https://oauth2.youpinhaoche.com
      redirect-uri: https://chat-web.qnvip.com
    resource:
      token-info-uri: https://oauth2.youpinhaoche.com/api/oauth/check_token

wechat:
  corpid: wwecdcda1ec54a9e69
  secret: U-Lkq0A5GLpkF1GIcpifBOSaMhYOtUJahxpPNx5vPwU
  redirectUrl: https://chat-web.qnvip.com