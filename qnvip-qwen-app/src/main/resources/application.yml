spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: '@spring.profiles.active@'
  application:
    name: qnvip-qwen-app
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB


management:
  server:
    port: 16012
  endpoints:
    web:
      exposure:
        include: [ 'health', 'info', 'loggers', 'prometheus' ]
  endpoint:
    health:
      show-details: ALWAYS  #显示详细信息
    shutdown:
      enabled: true
  health:
    elasticsearch:
      enabled: false


logging.file.name: ./logs/${spring.application.name}.log


itemKeyWordEmbeddingClientUrl: http://*************:8000/get_text_embedding