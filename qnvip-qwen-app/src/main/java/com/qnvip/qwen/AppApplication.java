package com.qnvip.qwen;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import lombok.extern.slf4j.Slf4j;

/**
 * ScaffoldApplication
 *
 * <AUTHOR>
 * @since 2024/1/15
 */
@Slf4j
@MapperScan("com.qnvip.qwen.dal.mapper")
@ComponentScan({"com.qnvip.oauth2.security", "com.qnvip.qwen"})
@SpringBootApplication
public class AppApplication {
    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        SpringApplication.run(AppApplication.class, args);
        log.info("================ AppApplication start success 耗时: {}ms================",
            System.currentTimeMillis() - start);
    }
}
