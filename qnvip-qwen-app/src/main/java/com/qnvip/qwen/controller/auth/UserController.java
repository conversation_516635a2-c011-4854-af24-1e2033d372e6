package com.qnvip.qwen.controller.auth;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.controller.BaseController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月14日 17:17:00
 */
@Api(tags = "获取用户信息")
@RestController
@RequestMapping(value = "/admin/user")
public class UserController extends BaseController {

    @PostMapping("getUserId")
    public ResultVO getUserId() {
        return ResultVO.of(super.getLoginUserId());
    }

    @ApiOperation("登录状态检查")
    @PostMapping("isLogin")
    public ResultVO<Boolean> isLogin() {
        return ResultVO.of(true);
    }

}
