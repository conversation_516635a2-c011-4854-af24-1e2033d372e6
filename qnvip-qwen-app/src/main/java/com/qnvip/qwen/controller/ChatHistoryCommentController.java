package com.qnvip.qwen.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.config.LlmEvaluateConfig;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDetailDTO;
import com.qnvip.qwen.service.ChatHistoryCommentService;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.vo.request.ChatHistoryCommentReq;
import com.qnvip.qwen.vo.response.ChatHistoryCommentResp;
import com.qnvip.qwen.vo.response.LLMEvaluateResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 聊天记录评价表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@Api(tags = "聊天记录评价表")
@RestController
@RequestMapping(value = "/api/chatHistoryComment")
public class ChatHistoryCommentController extends BaseController {

    @Resource
    private ChatHistoryCommentService chatHistoryCommentService;

    @Resource
    private LlmEvaluateConfig llmEvaluateConfig;


    // @ApiOperation(value = "获取会话的所有点赞评价")
    // @PostMapping("list")
    // public ResultVO<List<ChatHistoryCommentResp>> list(@RequestBody ChatHistoryCommentQueryDTO record) {
    // record.setUserId(getLoginUserId());
    // List<ChatHistoryCommentResp> list = chatHistoryCommentService.list(record);
    // for (ChatHistoryCommentResp item : list) {
    // convertTagModel(item);
    // }
    // return ResultVO.of(list);
    // }

    @ApiOperation("评价标签列表")
    @GetMapping("evaluateTags")
    public ResultVO<Map<String, List<LLMEvaluateResp>>> evaluateTags() {
        List<LLMEvaluateResp> evaluateTags = llmEvaluateConfig.getEvaluateTags();
        Map<String, List<LLMEvaluateResp>> collect =
            evaluateTags.stream().collect(Collectors.groupingBy(LLMEvaluateResp::getTagGroup));
        return ResultVO.of(collect);
    }

    @ApiOperation("点赞评价")
    @PostMapping("like")
    public ResultVO<Boolean> save(@RequestBody ChatHistoryCommentReq record) {
        record.setUserId(getLoginUserId());
        return ResultVO.of(chatHistoryCommentService.save(record));
    }

    @ApiOperation("点赞评价详情")
    @PostMapping("detail")
    public ResultVO<ChatHistoryCommentResp> detail(@RequestBody ChatHistoryCommentQueryDetailDTO record) {
        record.setUserId(getLoginUserId());
        ChatHistoryCommentResp data = chatHistoryCommentService.detailByMessageId(record);
        convertTagModel(data);
        if (data == null) {
            return ResultVO.of(data);
        }

        if (!data.getLikeFlag().equals(record.getLikeFlag())) {
            data.setContent(null);
            data.setEvaluateTags(new ArrayList<>());
        }

        return ResultVO.of(data);
    }

    private void convertTagModel(ChatHistoryCommentResp item) {
        if (item == null) {
            return;
        }
        if (ObjectUtils.isEmpty(item.getEvaluateTag())) {
            item.setEvaluateTags(Lists.newArrayList());
            return;
        }
        Map<String, LLMEvaluateResp> stringLLMEvaluateRespMap = llmEvaluateConfig.mapByKeyMap();
        item.setEvaluateTags(Arrays.stream(item.getEvaluateTag().split(",")).map(stringLLMEvaluateRespMap::get)
            .collect(Collectors.toList()));
    }

}