package com.qnvip.qwen.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.service.UserCacheService;
import com.qnvip.qwen.vo.request.UserCacheReq;
import com.qnvip.qwen.vo.response.UserCacheResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户缓存表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@Api(tags = "用户缓存表")
@RestController
@RequestMapping(value = "/api/userCache")
public class UserCacheController extends BaseController {

    @Resource
    private UserCacheService userCacheService;

    @ApiOperation("用户缓存表详情")
    @GetMapping("detail")
    public ResultVO<UserCacheResp> detail() {
        return ResultVO.of(userCacheService.detail(getLoginUserId()));
    }

    @ApiOperation("用户缓存表新")
    @PostMapping("save")
    public ResultVO<Boolean> save(@RequestBody UserCacheReq record) {
        record.setId(getLoginUserId());

        return ResultVO.of(userCacheService.save(record));
    }

}