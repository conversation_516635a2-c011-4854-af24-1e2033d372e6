package com.qnvip.qwen.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.service.PromptRoleCategoryService;
import com.qnvip.qwen.vo.request.PromptRoleCategoryReq;
import com.qnvip.qwen.vo.response.PromptRoleCategoryResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色提示词分类表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
@Api(tags = "角色提示词分类表")
@RestController
@RequestMapping(value = "/api/promptRoleCategory")
public class PromptRoleCategoryController {

    @Resource
    private PromptRoleCategoryService promptRoleCategoryService;

    @ApiOperation(value = "角色提示词分类表列表")
    @PostMapping("list")
    public ResultVO<List<PromptRoleCategoryResp>> list(@RequestBody PromptRoleCategoryReq record) {
        return ResultVO.of(promptRoleCategoryService.list(record));
    }


}