package com.qnvip.qwen.controller.auth;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.qwen.bizService.Oauth2BizService;
import com.qnvip.qwen.bizService.WechatBizService;
import com.qnvip.qwen.dal.dto.wechat.WechatUserDTO;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月27日 16:45:00
 */
@RestController
@RequestMapping("/auth/wechat")
public class WeChatAuthController {

    @Resource
    private WechatBizService wechatBizService;
    @Resource
    private Oauth2BizService oauth2Service;

    @Value("${wechat.redirectUrl}")
    private String redirectUrl;

    @GetMapping("login")
    public void login(@RequestParam("code") String code, HttpServletResponse response) throws IOException {
        WechatUserDTO wechatUserDTO = wechatBizService.getUserInfo(code);
        String mobile = wechatUserDTO.getMobile();
        String token = oauth2Service.getOauth2Token(mobile);
        // 重定向到指定地址
        response.sendRedirect(redirectUrl + "?fromBy=wechat&token=" + token);
    }

}
