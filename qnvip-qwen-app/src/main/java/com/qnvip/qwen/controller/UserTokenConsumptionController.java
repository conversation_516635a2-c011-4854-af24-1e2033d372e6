package com.qnvip.qwen.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.service.UserTokenConfigService;
import com.qnvip.qwen.service.UserTokenConsumptionService;
import com.qnvip.qwen.vo.request.UserTokenConsumptionReq;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户token消耗表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
@Api(tags = "用户token消耗表")
@RestController
@RequestMapping(value = "/api/userTokenConsumption")
public class UserTokenConsumptionController extends BaseController {

    @Resource
    private UserTokenConsumptionService userTokenConsumptionService;

    @Resource
    private UserTokenConfigService userTokenConfigService;

    @ApiOperation("可以使用token 前端 每次对话前调用")
    @PostMapping("hasToken")
    public ResultVO<Boolean> hasToken() {
        boolean data = userTokenConfigService.hasToken(getLoginUserId());
        return data ? ResultVO.of(data) : ResultVO.error("个人token不足，联系管理员");
    }

    @ApiOperation("用户token消耗表记录")
    @PostMapping("save")
    public ResultVO<Boolean> save(@RequestBody UserTokenConsumptionReq record) {
        record.setUserId(getLoginUserId());
        record.setTotalConsumedToken(record.getInputConsumedToken() + record.getOutputConsumedToken());
        return ResultVO.of(userTokenConsumptionService.save(record, false));
    }

    @ApiOperation("用户token消耗表记录-系统")
    @PostMapping("save/system")
    public ResultVO<Boolean> saveSystem(@RequestBody UserTokenConsumptionReq record) {
        record.setUserId(getLoginUserId());

        return ResultVO.of(userTokenConsumptionService.saveSystem(record, true));
    }

}