package com.qnvip.qwen.controller.open;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.util.AssertUtils;
import com.qnvip.qwen.bizService.ItemKeywordBizService;
import com.qnvip.qwen.controller.BaseController;
import com.qnvip.qwen.dal.dto.open.ItemKeywordQueryDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月20日 16:46:00
 */
@Slf4j
@Api(tags = "商品名称搜索")
@RestController
@RequestMapping(value = "/open/itemKeyword")
public class ItemNameSearchController extends BaseController {

    @Resource
    private ItemKeywordBizService itemKeywordBizService;

    @PostMapping("search")
    @ApiOperation("搜索关键词")
    public ResultVO search(@RequestBody ItemKeywordQueryDTO queryDTO) {
        String searchContent = queryDTO.getSearchContent();
        Long platformId = queryDTO.getPlatformId();

        AssertUtils.checkNotBlank(searchContent, "searchContent参数不能为空");
        AssertUtils.checkNotNull(platformId, "platformId参数不能为空");
        return itemKeywordBizService.search(searchContent, platformId);
    }
}
