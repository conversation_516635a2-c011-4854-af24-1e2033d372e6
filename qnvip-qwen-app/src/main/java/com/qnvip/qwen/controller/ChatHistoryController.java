package com.qnvip.qwen.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.qwen.service.ChatHistoryService;
import com.qnvip.qwen.vo.request.ChatHistoryLoadChatHistoryListReq;
import com.qnvip.qwen.vo.request.ChatHistoryLoadChatHistoryReq;
import com.qnvip.qwen.vo.request.ChatHistoryReq;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 对话记录表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@Api(tags = "对话记录表")
@RestController
@RequestMapping(value = "/api/chatHistory")
public class ChatHistoryController extends BaseController {

    @Resource
    private ChatHistoryService chatHistoryService;

    @ApiOperation("保存回复")
    @PostMapping("saveChat")
    public boolean saveChat(@RequestBody ChatHistoryReq record) {
        record.setUserId(getLoginUserId());
        chatHistoryService.saveChat(record);
        return true;
    }

    @ApiOperation("加载对话记录块")
    @PostMapping("loadChatHistory")
    public String loadChatHistory(@RequestBody ChatHistoryLoadChatHistoryReq record) {
        record.setUserId(getLoginUserId());
        return chatHistoryService.loadChatHistory(record);
    }

    @ApiOperation("加载对话记录list")
    @PostMapping("loadChatHistoryList")
    public List<String> loadChatHistoryList(@RequestBody ChatHistoryLoadChatHistoryListReq record) {
        record.setUserId(getLoginUserId());
        return chatHistoryService.loadChatHistory(record);
    }

}