package com.qnvip.qwen.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.dal.dto.ContentDTO;
import com.qnvip.qwen.service.FileOriginService;
import com.qnvip.qwen.vo.response.TeamBookDocSummaryResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色提示词模板表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@Api(tags = "文章")
@RestController
@RequestMapping(value = "/api/doc")
public class DocController extends BaseController {

    @Resource
    private FileOriginService fileOriginService;

    @ApiOperation(value = "链接匹配文章")
    @PostMapping("urlMatchDoc")
    public ResultVO<List<TeamBookDocSummaryResp>> urlMatchDoc(@RequestBody ContentDTO record) {
        List<TeamBookDocSummaryResp> resp = fileOriginService.urlMatchDoc(record.getContent());
        return ResultVO.of(resp);
    }

}