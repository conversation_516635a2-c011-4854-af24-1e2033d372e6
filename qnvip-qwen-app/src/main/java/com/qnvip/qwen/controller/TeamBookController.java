package com.qnvip.qwen.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.dal.dto.TeamBookQueryDTO;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.enums.HintConstants;
import com.qnvip.qwen.service.PermRoleResourceService;
import com.qnvip.qwen.service.TeamBookService;
import com.qnvip.qwen.vo.response.TeamResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色提示词模板表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@Api(tags = "知识库")
@RestController
@RequestMapping(value = "/api/teamBook")
public class TeamBookController extends BaseController {

    @Resource
    private TeamBookService teamBookService;

    @Resource
    private PermRoleResourceService permRoleResourceService;

    @ApiOperation(value = "知识库列表")
    @PostMapping("list")
    public ResultVO<List<TeamResp>> list(@RequestBody TeamBookQueryDTO record) {
        List<Long> userKnowledgeBaseIds = permRoleResourceService.getUserKnowledgeBaseIds(getLoginUserId());
        if (ObjectUtils.isEmpty(userKnowledgeBaseIds)) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(), HintConstants.NO_BOOK_PERMISSION);
        }
        record.setIds(userKnowledgeBaseIds);
        return ResultVO.of(teamBookService.list(record));
    }

}