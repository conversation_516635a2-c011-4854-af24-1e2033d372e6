package com.qnvip.qwen.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.util.GzipBase64Util;

import io.swagger.annotations.Api;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月14日 13:50:00
 */
@Api(tags = "获取用户信息")
@RestController
@RequestMapping(value = "/dify/chat")
public class DifyChatController extends BaseController {
    public static final String CHAT_URL_INTERNAL = "https://ai.qnvip.com/chatbot/DdWslXaNieB2fD0O";
    public static final String CHAT_URL_THIRD = "https://ai.qnvip.com/chatbot/Qi2cwR35uBsjxiXj";

    @PostMapping("list")
    public ResultVO list(@RequestHeader("Authorization") String authorization) throws IOException {
        super.getLoginUserId();
        String token = GzipBase64Util.compressAndEncode(authorization);
        Map<String, String> chatList = new HashMap<>();
        chatList.put("internal", CHAT_URL_INTERNAL + "?token=" + token);
        chatList.put("third", CHAT_URL_THIRD + "?token=" + token);
        return ResultVO.of(chatList);
    }
}
