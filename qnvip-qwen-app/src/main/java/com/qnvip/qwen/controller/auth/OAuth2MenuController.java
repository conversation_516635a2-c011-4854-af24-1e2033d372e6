package com.qnvip.qwen.controller.auth;

import java.util.ArrayList;
import java.util.List;

import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.qnvip.common.base.ResultVO;
import com.qnvip.oauth2.security.config.SecurityContext;
import com.qnvip.oauth2.security.constants.OAuth2Constants;
import com.qnvip.qwen.controller.BaseController;
import com.qnvip.qwen.dal.dto.AdminRouterDTO;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;

/**
 * create by gw on 2022/5/19
 */
@RestController
@RequestMapping("/auth/menu/")
@Api(tags = "后台登录")
public class OAuth2MenuController extends BaseController {

    @GetMapping("router")
    public ResultVO<List<AdminRouterDTO>> menuTree(Authentication authentication) {
        if (authentication == null) {
            return ResultVO.error(104, "用户未登录");
        }
        // 从redis里取菜单资源
        String routerNamespace =
            OAuth2Constants.routerNamespace(SecurityContext.getLoginUserId(), oAuth2ClientProperties.getClientId());
        String menus = stringRedisTemplate.opsForValue().get(routerNamespace);
        if (StringUtils.isBlank(menus)) {
            return ResultVO.of(new ArrayList<>());
        }
        return ResultVO.of(JSONUtil.toList(menus, AdminRouterDTO.class));
    }
}
