package com.qnvip.qwen.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.bizService.DynamicsNavigationBizService;
import com.qnvip.qwen.dal.dto.ContentDTO;
import com.qnvip.qwen.dal.dto.FilterOptionsDTO;
import com.qnvip.qwen.dal.entity.LlmSupportModelDO;
import com.qnvip.qwen.enums.HintConstants;
import com.qnvip.qwen.service.DifyAppApikeyService;
import com.qnvip.qwen.service.FileOriginService;
import com.qnvip.qwen.service.LlmSupportModelService;
import com.qnvip.qwen.service.PermRoleResourceService;
import com.qnvip.qwen.service.PromptRoleTemplateService;
import com.qnvip.qwen.service.UserTokenConfigService;
import com.qnvip.qwen.vo.request.DifyUserQuestionReq;
import com.qnvip.qwen.vo.request.MultiRouteRecallConfig;
import com.qnvip.qwen.vo.response.LLMUrlTokenResp;
import com.qnvip.qwen.vo.response.TeamBookDocSummaryResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "dify控制器")
@RestController
@RequestMapping(value = "/api/dify")
public class DifyController extends BaseController {

    @Resource
    private DynamicsNavigationBizService dynamicsNavigationBizService;

    @Resource
    private PermRoleResourceService permRoleResourceService;

    @Resource
    private UserTokenConfigService userTokenConfigService;

    @Resource
    private LlmSupportModelService llmSupportModelService;

    @Resource
    private DifyAppApikeyService difyAppApikeyService;

    @Resource
    private PromptRoleTemplateService promptRoleTemplateService;

    @Resource
    private FileOriginService fileOriginService;

    public MultiRouteRecallConfig defaultNoPermitConfig() {
        MultiRouteRecallConfig req = new MultiRouteRecallConfig();

        req.setChunkFullText(new FilterOptionsDTO(50, 0.01f));
        req.setQaVector(new FilterOptionsDTO(20, 0.5f));
        req.setGraphVectorFilter(new FilterOptionsDTO(50, 0.1f));
        req.setGraphChunkIdFilter(new FilterOptionsDTO(50, 0.1f));
        req.setRerankChunk(new FilterOptionsDTO(10, 0.6f));
        req.setDocTopN(3);
        req.setTkweight(new BigDecimal("0.3"));
        req.setVtweight(new BigDecimal("0.7"));
        return req;
    }

    @ApiOperation("知识库权限检查")
    @GetMapping("bookPermitCheck")
    public String bookPermitCheck() {
        List<Long> userKnowledgeBaseIds = permRoleResourceService.getUserKnowledgeBaseIds(getLoginUserId());
        if (ObjectUtils.isEmpty(userKnowledgeBaseIds)) {
            return HintConstants.NO_BOOK_PERMISSION;
        }
        return "";

    }

    @ApiOperation("深度检索")
    @PostMapping("deepSearch")
    public String deepSearch(@RequestBody DifyUserQuestionReq record) {
        record.setUserId(getLoginUserId());
        if (record.getUserPermit() == null) {
            record.setUserPermit(deepSearchDefaultConfig());
        }
        if (record.getNoPermit() == null) {
            record.setNoPermit(defaultNoPermitConfig());
        }

        List<Long> userKnowledgeBaseIds = permRoleResourceService.getUserKnowledgeBaseIds(record.getUserId());
        if (ObjectUtils.isEmpty(userKnowledgeBaseIds)) {
            return HintConstants.NO_BOOK_PERMISSION;
        }

        if (!ObjectUtils.isEmpty(record.getBookIdsStr())) {
            Set<Long> pickBookIds =
                Arrays.stream(record.getBookIdsStr().split(",")).map(Long::valueOf).collect(Collectors.toSet());
            userKnowledgeBaseIds.retainAll(pickBookIds);
        }

        if (ObjectUtils.isEmpty(userKnowledgeBaseIds)) {
            return "";
        }

        record.setUserBookIs(userKnowledgeBaseIds);

        if (!ObjectUtils.isEmpty(record.getUserInput())) {
            if (record.getUserInputs() == null) {
                record.setUserInputs(new ArrayList<>());
            }
            record.getUserInputs().add(record.getUserInput());
        }

        processBeforeSearch(record);

        String callback;
        if (record.getRecallAnalysis()) {
            callback = dynamicsNavigationBizService.navigationDebug(record);
        } else {
            callback = dynamicsNavigationBizService.navigation(record);
        }

        if (ObjectUtils.isEmpty(callback)) {
            return "";
        }
        return callback;
    }

    private void processBeforeSearch(DifyUserQuestionReq record) {
        if (!ObjectUtils.isEmpty(record.getUserInput())){
            record.setUserInput(record.getUserInput().replace("我们公司","我们公司青年优品"));
        }

        if (!ObjectUtils.isEmpty(record.getUserInputs())){
            record.setUserInputs(record.getUserInputs().stream().map(e->e.replace("我们公司","青年优品")).collect(Collectors.toList()));
        }
    }

    public MultiRouteRecallConfig deepSearchDefaultConfig() {
        MultiRouteRecallConfig req = new MultiRouteRecallConfig();
        req.setChunkFullText(new FilterOptionsDTO(150, 0.01f));
        req.setQaVector(new FilterOptionsDTO(50, 0.1f));
        req.setGraphVectorFilter(new FilterOptionsDTO(50, 0.1f));
        req.setGraphChunkIdFilter(new FilterOptionsDTO(50, 0.1f));
        req.setRerankChunk(new FilterOptionsDTO(30, 0.3f));
        req.setDocTopN(30);
        req.setTkweight(new BigDecimal("0.3"));
        req.setVtweight(new BigDecimal("0.7"));
        return req;
    }

    @ApiOperation("可以使用token")
    @PostMapping("hasToken")
    public boolean hasToken() {
        return userTokenConfigService.hasToken(getLoginUserId());
    }

    @ApiOperation("模型列表")
    @GetMapping("modelList")
    public ResultVO<List<LlmSupportModelDO>> modelList() {
        return ResultVO.of(llmSupportModelService.list());
    }

    @ApiOperation(value = "链接匹配文章")
    @PostMapping("promptUrlMatchDoc")
    public String promptUrlMatchDoc(@RequestBody ContentDTO record) {
        List<TeamBookDocSummaryResp> resp = fileOriginService.urlMatchDoc(record.getContent());
        if (ObjectUtils.isEmpty(resp)) {
            return "";
        }

        return promptRoleTemplateService.contentReplace(record.getContent(), resp);
    }

    @ApiOperation("根据模型筛选地址和鉴权")
    @GetMapping("modelUrlToken")
    public ResultVO<LLMUrlTokenResp> modelUrlToken(@RequestParam(value = "modelId", defaultValue = "1") Integer modelId,
        @RequestParam(value = "type", required = false, defaultValue = "chat") String type) {
        LLMUrlTokenResp byModelId = difyAppApikeyService.getByModelId(modelId, type);
        byModelId.setUrl(byModelId.getUrl() + "chat-messages");
        return ResultVO.of(byModelId);
    }

    @ApiOperation("dify加载角色提示词")
    @GetMapping("prompt")
    public String detail(@RequestParam(value = "roleId", required = false) Long roleId) {
        if (roleId == null || roleId <= 0) {
            return "你是一个有用的助手";
        }
        return promptRoleTemplateService.detail(roleId).getContent();
    }

}
