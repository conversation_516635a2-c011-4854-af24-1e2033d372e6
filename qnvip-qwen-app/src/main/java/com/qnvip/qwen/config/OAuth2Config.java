package com.qnvip.qwen.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;

import com.qnvip.oauth2.security.config.ResourceServerConfig;

@Configuration
@EnableResourceServer
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
public class OAuth2Config extends ResourceServerConfig {

    private final String[] backIgnoreUrl = {"/swagger-ui.html", "/webjars/**", "/swagger-resources/**", "/v2/api-docs",
        "/doc.html", "/favicon.ico", "/auth/**", "/api/**", "/open/**", "/devops/**",
        "/WW_verify_APfRaZD8yP92WvN7.txt"};

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
            // 不需要登录即可访问
            .antMatchers(backIgnoreUrl).permitAll()
            // 只要登录就能访问
            .antMatchers("/auth/logout").authenticated().and().authorizeRequests()
            // 接口权限暂时注释不判断,只要登录就能访问
            .anyRequest().access("@rbacInterceptor.hasPermission(request, authentication)")
            // .anyRequest().authenticated()
            .and()
            // 支持跨域
            .cors().and().csrf().disable();

    }

}
