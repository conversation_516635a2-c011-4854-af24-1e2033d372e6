package com.qnvip.qwen.config;

import org.springframework.cloud.client.serviceregistry.Registration;
import org.springframework.cloud.client.serviceregistry.ServiceRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class SpringBootSmartOfflineConfig {

    @Bean
    public ServiceRegistry serviceRegistry() {
        return new ServiceRegistry() {
            @Override
            public void register(Registration registration) {

            }

            @Override
            public void deregister(Registration registration) {

            }

            @Override
            public void close() {
                log.info("收到服务优雅下线请求 但是实际不下线，只变更health接口响应码500,以便Nginx侧下线，防止收到请求");
            }

            @Override
            public void setStatus(Registration registration, String status) {

            }

            @Override
            public Object getStatus(Registration registration) {
                return null;
            }
        };
    }
}
