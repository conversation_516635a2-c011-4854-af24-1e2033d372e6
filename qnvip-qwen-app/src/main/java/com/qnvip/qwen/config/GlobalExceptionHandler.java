package com.qnvip.qwen.config;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;


/**
 * GlobalExceptionHandler
 *
 * <AUTHOR>
 * @since 2024/1/16
 */
@Slf4j
@ResponseStatus(HttpStatus.OK)
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Random RANDOM = new Random();

    /**
     * 业务定义的异常
     */
    @ExceptionHandler(value = {BusinessException.class})
    public ResultVO<Object> baseExceptionHandler(HttpServletRequest request, BusinessException e) {
        log.warn("BusinessException出现了,message: {}, requestURL: {}", e.getMessage(), request.getRequestURL(), e);
        return ResultVO.error(e.getErrorCode(), e.getErrorMsg());
    }

    @ExceptionHandler(value = Exception.class)
    public ResultVO<Object> exceptionHandler(HttpServletRequest request, Exception e) {
        String errorMsg = Optional.ofNullable(e.getMessage()).orElse("");
        log.error("系统异常, message: {}, requestURL: {}", e.getMessage(), request.getRequestURL(), e);
        return ResultVO.error(getReturnValue());
    }

    public String getReturnValue() {

        List<String> returnList = new ArrayList<>();
        returnList.add("网络延迟，请稍后再试");
        returnList.add("当前浏览人数较多，请稍后再试");
        returnList.add("努力加载中，请稍后");
        returnList.add("您的操作太频繁了，请稍后再试");
        // 随机获取
        return returnList.get(RANDOM.nextInt(returnList.size()));
    }
}
