package com.qnvip.qwen.config;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.qnvip.qwen.vo.response.LLMEvaluateResp;

import lombok.Data;

@Configuration
@ConfigurationProperties(prefix = "llm.evaluate")
@Data
public class LlmEvaluateConfig {
    private List<LLMEvaluateResp> evaluateTags;

    public Map<String, LLMEvaluateResp> mapByKeyMap() {
        return evaluateTags.stream().collect(Collectors.toMap(LLMEvaluateResp::getTagKey, v -> v));
    }
}
