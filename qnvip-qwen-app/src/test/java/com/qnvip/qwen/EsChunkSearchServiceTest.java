package com.qnvip.qwen;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.qnvip.qwen.dal.es.EsChunkSearchService;

public class EsChunkSearchServiceTest extends BaseTest {

    @Resource
    private EsChunkSearchService esChunkSearchService;

    @Test
    public void search() {
        // EsChunkSearchDTO searchDTO = new EsChunkSearchDTO();
        // searchDTO.setTeamIds(Arrays.asList(1L));
        // searchDTO.setUserInput("供货商后台增加快递单号筛选");
        // searchDTO.setFileOriginIds(null);
        // searchDTO.setBookIds(Arrays.asList(6L));
        // List<DocChunkESDO> list = esChunkSearchService.searchRelevantDocuments(searchDTO);
        // System.out.println(JSONUtil.toJsonStr(list));
    }

}
