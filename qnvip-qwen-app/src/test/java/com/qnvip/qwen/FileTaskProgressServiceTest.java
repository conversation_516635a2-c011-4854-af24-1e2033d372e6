package com.qnvip.qwen;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.qnvip.qwen.service.FileTaskProgressService;

public class FileTaskProgressServiceTest extends BaseTest {

    @Resource
    FileTaskProgressService fileTaskProgressService;

    @Test
    public void saveTaskStartWithCleanTest() {
        // fileTaskProgressService.saveTaskStartWithClean(1L);
    }

    @Test
    public void saveTaskStartWithFormatTest() {
        fileTaskProgressService.saveTaskStartWithFormat(null, 1L, null);
    }
}
