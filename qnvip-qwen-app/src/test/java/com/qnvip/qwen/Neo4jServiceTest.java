package com.qnvip.qwen;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.qnvip.qwen.dal.dto.graph.GraphEntity;
import com.qnvip.qwen.dal.dto.graph.GraphRelationship;
import com.qnvip.qwen.dal.graph.Neo4jService;

public class Neo4jServiceTest extends BaseTest {

    @Resource
    private Neo4jService neo4jService; // 注入Neo4j服务类，用于执行Cypher查询

    /**
     * 测试保存实体到Neo4j
     */
    @Test
    public void saveEntityTest() {
        // 1. 准备测试数据
        List<GraphEntity> entities = createTestEntities();

        // 2. 执行保存操作
        neo4jService.saveEntity(entities);

    }

    @Test
    public void saveRelationshipTest() {
        // 1. 准备测试数据 - 基于提供的SQL数据
        List<GraphRelationship> relationships = createTestRelationships();

        // 2. 执行保存操作
        neo4jService.saveRelationship(relationships);

        // 3. 验证保存结果（可选）
        System.out.println("关系保存完成，共保存 " + relationships.size() + " 条关系");
    }


    /**
     * 创建测试实体数据
     */
    private List<GraphEntity> createTestEntities() {
        List<GraphEntity> entities = new ArrayList<>();

        // 创建测试实体 - 基于数据库数据
        GraphEntity entity1 = new GraphEntity();
        entity1.setCategory("entity");
        entity1.setEntityName("产品部");
        entity1.setEntityType("组织");
        entity1.setBookId(Arrays.asList(66L));
        entity1.setFileOriginId(Arrays.asList(12447L));
        entity1.setFileChunkUid(Arrays.asList("20250728203650614382691960275176"));
        entity1.setGraphScore(85);
        entity1.setVdbScore(0.92f);
        entities.add(entity1);

        return entities;
    }

    /**
     * 创建测试关系数据 - 基于提供的SQL数据 INSERT INTO `qnvip_qwen`.`qw_file_graph_relation` (`id`, `version`, `create_time`,
     * `update_time`, `is_deleted`, `chunk_uid`, `file_origin_id`, `book_id`, `source`, `target`, `keyword`, `strength`,
     * `description`) VALUES (99494, 1, '2025-07-28 21:05:28.2400', '2025-07-28 21:05:28.2400', 0,
     * '20250728203650614382691960275176', 12447, 66, '产研中心', '产品部', '产品部是产研中心下属部门。', 10, '产品部是产研中心下属部门。');
     */
    private List<GraphRelationship> createTestRelationships() {
        List<GraphRelationship> relationships = new ArrayList<>();

        // 创建测试关系 - 基于提供的SQL数据
        GraphRelationship relationship1 = new GraphRelationship();
        relationship1.setCategory("relationship");
        relationship1.setSourceEntity("产研中心");
        relationship1.setTargetEntity("产品部");
        relationship1.setBookId(Arrays.asList(66L));
        relationship1.setFileOriginId(Arrays.asList(12447L));
        relationship1.setFileChunkUid(Arrays.asList("20250728203650614382691960275176"));
        relationship1.setRelationshipKeyword(Arrays.asList("产品部是产研中心下属部门。"));
        relationship1.setRelationshipStrength(10);
        relationships.add(relationship1);

        // 可以添加更多测试关系数据
        GraphRelationship relationship2 = new GraphRelationship();
        relationship2.setCategory("relationship");
        relationship2.setSourceEntity("产品部");
        relationship2.setTargetEntity("产研中心");
        relationship2.setBookId(Arrays.asList(66L));
        relationship2.setFileOriginId(Arrays.asList(12447L));
        relationship2.setFileChunkUid(Arrays.asList("20250728203650614382691960275176"));
        relationship2.setRelationshipKeyword(Arrays.asList("隶属关系", "组织架构"));
        relationship2.setRelationshipStrength(8);
        relationships.add(relationship2);

        return relationships;
    }



}