package com.qnvip.qwen;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.qnvip.qwen.bizService.WechatBizService;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberTreeDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 微信业务服务测试
 * @createTime 2025年01月27日 10:00:00
 */
@Slf4j
public class WechatBizServiceTest extends BaseTest {

    @Resource
    private WechatBizService wechatBizService;

    @Test
    public void testGetDepartmentList() {
        List<WechatDepartmentMemberTreeDTO> departmentMemberTree = wechatBizService.getDepartmentMemberTree();
        String s = wechatBizService.formatAsProperties(departmentMemberTree);
        System.out.println(s);


    }


}