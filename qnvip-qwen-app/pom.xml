<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qnvip</groupId>
        <artifactId>qnvip-qwen</artifactId>
        <version>${project.version}</version>
    </parent>

    <artifactId>qnvip-qwen-app</artifactId>

    <properties>
        <oauth2-core-nacos.version>1.0-SNAPSHOT</oauth2-core-nacos.version>
        <spring-cloud-starter-oauth2.version>2.1.0.RELEASE</spring-cloud-starter-oauth2.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-qwen-biz</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 基础组件 -->
        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-web-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-actuator-starter</artifactId>
        </dependency>

        <!-- actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>


        <!-- skywalking -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>

        <!-- logstash -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>


        <!-- archUnit for junit5 -->
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5</artifactId>
            <scope>test</scope>
        </dependency>

        <!--oauth2 start-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-oauth2</artifactId>
            <version>${spring-cloud-starter-oauth2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>oauth2-core-nacos</artifactId>
            <version>${oauth2-core-nacos.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.qnvip</groupId>
                    <artifactId>commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-neo4j</artifactId>
        </dependency>
        <!--oauth2 end-->

        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.4.11</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.24.0</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>qnvip-qwen-app</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
