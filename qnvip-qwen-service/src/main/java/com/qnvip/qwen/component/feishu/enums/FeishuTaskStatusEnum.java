package com.qnvip.qwen.component.feishu.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum FeishuTaskStatusEnum {

    // 成功
    SUCCESS(0, "成功"),
    // 失败
    FAILURE(1, "失败"),
    SHEET_IMAGES_EXCEED_LIMIT(6000, "图片超出限制"),
    // 处理中
    PROCESSING(2, "处理中");

    private final int code;
    private final String desc;

    public static FeishuTaskStatusEnum getByCode(int code) {
        for (FeishuTaskStatusEnum value : FeishuTaskStatusEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}