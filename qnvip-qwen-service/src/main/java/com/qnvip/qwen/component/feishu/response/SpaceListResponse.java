package com.qnvip.qwen.component.feishu.response;

import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

/**
 * 飞书空间列表响应模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2024/03/21
 */
@Data
public class SpaceListResponse {

    /**
     * 是否还有更多数据
     */
    @JSONField(name = "has_more")
    private Boolean hasMore;

    /**
     * 空间列表
     */
    @JSONField(name = "items")
    private List<SpaceItem> items;

    /**
     * 分页标记，用于获取下一页数据
     */
    @JSONField(name = "page_token")
    private String pageToken;

    @Data
    public static class SpaceItem {
        /**
         * 空间描述
         */
        @JSONField(name = "description")
        private String description;

        /**
         * 空间名称
         */
        @JSONField(name = "name")
        private String name;

        /**
         * 空间分享设置 可选值： - closed：关闭分享 - open：开启分享
         */
        @JSONField(name = "open_sharing")
        private String openSharing;

        /**
         * 空间ID
         */
        @JSONField(name = "space_id")
        private String spaceId;

        /**
         * 空间类型 可选值： - team：团队空间 - personal：个人空间
         */
        @JSONField(name = "space_type")
        private String spaceType;

        /**
         * 空间可见性 可选值： - public：公开 - private：私有
         */
        @JSONField(name = "visibility")
        private String visibility;
    }
}