package com.qnvip.qwen.component.yuque.response;

import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

/**
 * 语雀知识库
 */
@Data
public class YuQueBookListResp {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "type")
    private String type;

    /**
     * 知识库的唯一标识，通常为 slug
     */
    @JSONField(name = "slug")
    private String slug;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "user_id")
    private Long userId;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "creator_id")
    private Long creatorId;

    @JSONField(name = "public")
    private Integer publicValue;

    @JSONField(name = "items_count")
    private Integer itemsCount;

    @JSONField(name = "likes_count")
    private Integer likesCount;

    @JSONField(name = "watches_count")
    private Integer watchesCount;

    @JSONField(name = "content_updated_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime contentUpdatedAt;

    @JSONField(name = "created_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime createdAt;

    @JSONField(name = "updated_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime updatedAt;

    @JSONField(name = "namespace")
    private String namespace;
}
