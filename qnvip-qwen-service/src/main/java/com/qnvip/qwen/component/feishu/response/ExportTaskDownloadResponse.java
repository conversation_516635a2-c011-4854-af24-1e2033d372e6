package com.qnvip.qwen.component.feishu.response;

import lombok.Data;

/**
 * 飞书导出任务下载响应模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2024/03/21
 */
@Data
public class ExportTaskDownloadResponse {

    /**
     * 文件流
     */
    private byte[] fileData;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;


    /**
     * 文件类型
     */
    private String type;

    /**
     * 导出文件token
     */
    private String fileToken;

    /**
     * 导出文件扩展名
     */
    private String fileExtension;
}