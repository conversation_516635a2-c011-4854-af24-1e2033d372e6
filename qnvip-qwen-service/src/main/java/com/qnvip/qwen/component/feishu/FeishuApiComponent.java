package com.qnvip.qwen.component.feishu;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.drive.v1.model.CreateExportTaskReq;
import com.lark.oapi.service.drive.v1.model.CreateExportTaskResp;
import com.lark.oapi.service.drive.v1.model.DownloadExportTaskReq;
import com.lark.oapi.service.drive.v1.model.DownloadExportTaskResp;
import com.lark.oapi.service.drive.v1.model.ExportTask;
import com.lark.oapi.service.drive.v1.model.GetExportTaskReq;
import com.lark.oapi.service.drive.v1.model.GetExportTaskResp;
import com.lark.oapi.service.wiki.v2.model.ListSpaceNodeReq;
import com.lark.oapi.service.wiki.v2.model.ListSpaceNodeResp;
import com.lark.oapi.service.wiki.v2.model.ListSpaceReq;
import com.lark.oapi.service.wiki.v2.model.ListSpaceResp;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.component.feishu.enums.FeishuObjectTypeEnum;
import com.qnvip.qwen.component.feishu.enums.FeishuTaskStatusEnum;
import com.qnvip.qwen.component.feishu.response.ExportTaskDownloadResponse;
import com.qnvip.qwen.component.feishu.response.ExportTaskResponse;
import com.qnvip.qwen.component.feishu.response.ExportTaskStatusResponse;
import com.qnvip.qwen.component.feishu.response.SpaceListResponse;
import com.qnvip.qwen.component.feishu.response.SpaceNodeListResponse;
import com.qnvip.qwen.enums.FileExtEnum;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 飞书API组件 接口文档地址 https://open.feishu.cn/document/server-docs/docs/wiki-v2/space/list
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Slf4j
@Component
@Data
public class FeishuApiComponent {

    private static final String URL_TEMPLATE = "https://%s.feishu.cn/wiki/%s";

    private static final Long MAX_FILE_SIZE = 512L * 1024 * 1024;
    private Map<Long, AppSecret> teamMap = new ConcurrentHashMap<>();

    public void init(List<AppSecret> appSecrets) {
        for (AppSecret appSecret : appSecrets) {
            teamMap.put(appSecret.getTeamId(), appSecret);
        }
    }

    /**
     * 获取空间列表 https://open.feishu.cn/document/server-docs/docs/wiki-v2/space/list
     *
     * @param pageSize 每页数量
     * @return 空间列表响应
     */
    public SpaceListResponse listSpace(Long teamId, Integer pageSize) {
        try {
            // 创建请求对象
            ListSpaceReq req = ListSpaceReq.newBuilder().pageSize(pageSize).lang("zh").build();

            // 发起请求
            ListSpaceResp resp = getClient(teamId).wiki().v2().space().list(req);

            // 处理服务端错误
            if (!resp.success()) {
                log.error("获取空间列表失败: code={}, msg={}, reqId={}, resp={}", resp.getCode(), resp.getMsg(),
                    resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
                return null;
            }

            // 转换响应数据
            String jsonStr = Jsons.DEFAULT.toJson(resp.getData());
            SpaceListResponse spaceListResponse = JSON.parseObject(jsonStr, SpaceListResponse.class);
            for (SpaceListResponse.SpaceItem item : spaceListResponse.getItems()) {
                item.setName(item.getName().replace("/", "_"));
            }
            return spaceListResponse;
        } catch (Exception e) {
            log.error("获取空间列表异常", e);
            return null;
        }
    }

    /**
     * 获取空间节点列表
     *
     * https://open.feishu.cn/document/server-docs/docs/wiki-v2/space-node/list?appId=cli_a8a85dff32a1900e
     *
     * @param spaceId 空间ID
     * @param parentNodeToken 父节点token，不传则获取根节点
     * @param pageSize 每页数量
     * @return 空间节点列表响应
     */
    public SpaceNodeListResponse listSpaceNode(Long teamId, String spaceId, String parentNodeToken, Integer pageSize) {
        try {

            // 创建请求对象
            ListSpaceNodeReq.Builder reqBuilder = ListSpaceNodeReq.newBuilder().spaceId(spaceId).pageSize(pageSize);

            if (parentNodeToken != null && !parentNodeToken.isEmpty()) {
                reqBuilder.parentNodeToken(parentNodeToken);
            }

            // 发起请求
            ListSpaceNodeResp resp = getClient(teamId).wiki().v2().spaceNode().list(reqBuilder.build());

            // 处理服务端错误
            if (!resp.success()) {
                log.error("获取空间节点列表失败: code={}, msg={}, reqId={}, resp={}", resp.getCode(), resp.getMsg(),
                    resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
                return null;
            }

            // 转换响应数据
            String jsonStr = Jsons.DEFAULT.toJson(resp.getData());
            SpaceNodeListResponse response = JSON.parseObject(jsonStr, SpaceNodeListResponse.class);

            for (SpaceNodeListResponse.NodeItem item : response.getItems()) {
                item.setTitle(item.getTitle().replace("/", "_"));
            }
            return response;
        } catch (Exception e) {
            log.error("获取空间节点列表异常", e);
            return null;
        }
    }

    private Client getClient(Long teamId) {
        AppSecret appSecret = teamMap.get(teamId);
        return Client.newBuilder(appSecret.getAppId(), appSecret.getAppSecret()).build();
    }

    /**
     * 创建导出任务 https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/create
     *
     * @param fileToken 文件token
     * @param fileType 文件类型，如：sheet、doc等
     * @return 导出任务响应
     */
    private ExportTaskResponse createExportTask(Long teamId, String fileToken, String fileType) {
        String fileExtension = "";

        if (fileType.equals(FeishuObjectTypeEnum.DOC.getCode())
            || fileType.equals(FeishuObjectTypeEnum.DOCX.getCode())) {
            fileExtension = FileExtEnum.DOCX.getDesc();
        } else if (fileType.equals(FeishuObjectTypeEnum.SHEET.getCode())
            || fileType.equals(FeishuObjectTypeEnum.BITABLE.getCode())) {
            fileExtension = FileExtEnum.XLSX.getDesc();
        } else {
            throw FrameworkException.instance("飞书转markdown 不支持的格式:" + fileType + " " + "fileToken:" + fileToken);
        }

        try {
            // 构建client
            Client client = getClient(teamId);

            // 创建导出任务对象
            ExportTask.Builder exportTaskBuilder =
                ExportTask.newBuilder().token(fileToken).type(fileType).fileExtension(fileExtension);

            // 创建请求对象
            CreateExportTaskReq req = CreateExportTaskReq.newBuilder().exportTask(exportTaskBuilder.build()).build();

            // 发起请求
            CreateExportTaskResp resp = client.drive().v1().exportTask().create(req);

            // 处理服务端错误
            if (!resp.success()) {
                log.error("创建导出任务失败: code={}, msg={}, reqId={}, resp={}", resp.getCode(), resp.getMsg(),
                    resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
                return null;
            }

            // 转换响应数据
            String jsonStr = Jsons.DEFAULT.toJson(resp.getData());
            return JSON.parseObject(jsonStr, ExportTaskResponse.class);
        } catch (Exception e) {
            log.error("创建导出任务异常", e);
            return null;
        }
    }

    /**
     * 创建和等待文件下载
     * 
     * @param teamId
     * @param fileToken
     * @param fileType
     * @return
     */
    public ExportTaskDownloadResponse waitForCreateExportTask(Long teamId, String fileToken, String fileType) {
        ExportTaskResponse task = createExportTask(teamId, fileToken, fileType);
        log.info("提交导出任务成功，等待查询taskId{}", task.getTicket());
        return waitForResult(teamId, task.getTicket(), fileToken);
    }

    private ExportTaskDownloadResponse waitForResult(Long teamId, String ticket, String fileToken) {
        ExportTaskStatusResponse exportTask = getExportTask(teamId, ticket, fileToken);

        while (exportTask.getResult().getJobStatus() == FeishuTaskStatusEnum.PROCESSING.getCode()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            exportTask = getExportTask(teamId, ticket, fileToken);
        }

        if (exportTask.getResult().getJobStatus() == FeishuTaskStatusEnum.FAILURE.getCode()
            || exportTask.getResult().getJobStatus() == FeishuTaskStatusEnum.SHEET_IMAGES_EXCEED_LIMIT.getCode()) {
            throw FrameworkException
                .instance("飞书导出文件失败失败,fileToken:" + fileToken + " err:" + exportTask.getResult().getJobErrorMsg());
        }

        if (exportTask.getResult().getFileSize() > MAX_FILE_SIZE) {
            throw FrameworkException
                .instance("文件太大，请重新选择,fileToken:" + fileToken + " size: " + exportTask.getResult().getFileSize());
        }

        return downloadExportTask(teamId, exportTask.getResult());
    }

    /**
     * 获取导出任务状态 https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/get
     *
     * @param ticket 导出任务ID
     * @param fileToken
     * @return 导出任务状态响应
     */
    private ExportTaskStatusResponse getExportTask(Long teamId, String ticket, String fileToken) {
        try {
            // 构建client
            Client client = getClient(teamId);

            // 创建请求对象
            GetExportTaskReq req = GetExportTaskReq.newBuilder().ticket(ticket).token(fileToken).build();

            // 发起请求
            GetExportTaskResp resp = client.drive().v1().exportTask().get(req);

            // 处理服务端错误
            if (!resp.success()) {
                log.error("获取导出任务状态失败: code={}, msg={}, reqId={}, resp={}", resp.getCode(), resp.getMsg(),
                    resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
                return null;
            }

            // 转换响应数据
            String jsonStr = Jsons.DEFAULT.toJson(resp.getData());
            ExportTaskStatusResponse exportTaskStatusResponse =
                JSON.parseObject(jsonStr, ExportTaskStatusResponse.class);
            exportTaskStatusResponse.getResult()
                .setFileName(exportTaskStatusResponse.getResult().getFileName().replace("/", "_"));
            return exportTaskStatusResponse;
        } catch (Exception e) {
            log.error("获取导出任务状态异常", e);
            return null;
        }
    }

    /**
     * 下载导出任务文件 https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/download
     *
     * @param taskResult 文件token
     * @return 导出任务下载响应
     */
    private ExportTaskDownloadResponse downloadExportTask(Long teamId,
        ExportTaskStatusResponse.ExportTaskResult taskResult) {
        try {
            // 构建client
            Client client = getClient(teamId);

            // 创建请求对象
            DownloadExportTaskReq req = DownloadExportTaskReq.newBuilder().fileToken(taskResult.getFileToken()).build();

            DownloadExportTaskResp resp = null;
            try {
                resp = client.drive().v1().exportTask().download(req);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }

            // 处理服务端错误
            if (!resp.success()) {
                log.error("下载导出任务文件失败: code={}, msg={}, reqId={}, resp={}", resp.getCode(), resp.getMsg(),
                    resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
                return null;
            }

            // 创建响应对象
            ExportTaskDownloadResponse response = new ExportTaskDownloadResponse();
            response.setFileData(resp.getRawResponse().getBody());
            response.setFileName(taskResult.getFileName());
            response.setFileSize(taskResult.getFileSize());
            response.setType(taskResult.getType());
            response.setFileToken(taskResult.getFileToken());
            response.setFileExtension(taskResult.getFileExtension());
            return response;
        } catch (Exception e) {
            log.error("下载导出任务文件异常", e);
            return null;
        }
    }

    public String getDocUrl(String teamMark, String bookMark) {
        return String.format(URL_TEMPLATE, teamMark, bookMark);
    }

    // {"appId":"cli_a8a85dff32a1900e","appSecret":"Pl1dXtqxumXRcs0yoQVf9b5kf3vgUbFM"}
    @Data
    public static class AppSecret {
        private Long teamId;
        private String appId;
        private String appSecret;
    }
}
