package com.qnvip.qwen.component.yuque;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.component.yuque.enums.YuQueGroupTokenEnum;
import com.qnvip.qwen.component.yuque.response.YuQueBookListResp;
import com.qnvip.qwen.component.yuque.response.YuQueBookTocResp;
import com.qnvip.qwen.component.yuque.response.YuQueDockDetailResp;
import com.qnvip.qwen.component.yuque.response.YuQueDockListResp;
import com.qnvip.qwen.util.Maps;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 语雀接口文档地址 https://www.yuque.com/yuque/developer/gyht993a76zg54mv
 */
@Slf4j
@Component
public class YuQueApiComponent {

    private static final String API_DOMAIN = "https://pokyqd.yuque.com";

    /**
     * 获取团队下的知识库
     */
    private static final String API_GROUP_LOGIN_REPOS =
        "/api/v2/groups/{groupLogin}/repos?offset={offset}&limit={limit}";

    /**
     * 获取库中的文章列表
     */
    private static final String API_REPOS_DOCS =
        "/api/v2/repos/{groupLogin}/{repoSlug}/docs?offset={offset}&limit={limit}";

    /**
     * 获取知识库目录
     */
    private static final String API_REPOS_TOC = "/api/v2/repos/{groupLogin}/{bookSlug}/toc";

    /**
     * 获取文章详情接口
     */
    private static final String API_REPOS_DOCS_DETAIL = "/api/v2/repos/{groupLogin}/{bookSlug}/docs/{docSlug}";


    /**
     * 获取文章列表接口
     */
    private static final String API_REPOS_DOCS_List =
        "/api/v2/repos/{groupLogin}/{bookSlug}/docs?offset={offset}&limit={limit}";

    /**
     * 公开性 (0:私密, 1:公开, 2:企业内公开)
     */
    private static final int PRIVACY_DATA = 0;



    /**
     * 获取语雀团队下的所有知识库
     * 
     * @param group 团队空间名字比如 青年优品技术部 pokyqd
     * @return
     */
    public List<YuQueBookListResp> getBookList(String group, Integer offset, Integer limit) {

        String api = API_GROUP_LOGIN_REPOS.replace("{groupLogin}", group);
        api = appendPaging(api, offset, limit);

        log.info("语雀获取团队知识库列表接口调用:{}", api);
        HttpRequest request = HttpUtil.createGet(API_DOMAIN + api);
        addTokenHeader(request, group);

        String body = request.execute().body();
        log.info("语雀获取团队知识库列表接口返回:{}", body);
        if (StringUtils.isEmpty(body)) {
            throw FrameworkException.instance("语雀获取团队知识库列表接口调用失败,返回结果为空");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        Integer status = jsonObject.getInteger("status");
        if (status != null && status == 404) {
            return null;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        List<YuQueBookListResp> javaList = data.toJavaList(YuQueBookListResp.class);
        return javaList.stream().filter(e -> e.getPublicValue() != PRIVACY_DATA).collect(Collectors.toList());
    }

    /**
     * 获取库中的目录
     *
     * @param group 团队
     * @param bookSlug 知识库唯一标识
     * @return
     */
    public List<YuQueBookTocResp> getBookTocList(String group, String bookSlug) {

        String api = API_REPOS_TOC.replace("{groupLogin}", group).replace("{bookSlug}", bookSlug);

        log.info("语雀获取知识库目录接口调用:{}", api);
        HttpRequest request = HttpUtil.createGet(API_DOMAIN + api);
        addTokenHeader(request, group);

        String body = request.execute().body();
        // log.info("语雀获取知识库目录接口返回:{}", body);
        if (StringUtils.isEmpty(body)) {
            throw FrameworkException.instance("语雀获取知识库目录调用失败,返回结果为空");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        Integer status = jsonObject.getInteger("status");
        if (status != null && status == 404) {
            return null;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        return data.toJavaList(YuQueBookTocResp.class);
    }

    /**
     * 获取库中的文章列表
     *
     * @return
     */
    public List<YuQueDockListResp> getDocList(String group, String bookSlug, Integer offset, Integer limit) {

        String api = API_REPOS_DOCS_List.replace("{groupLogin}", group).replace("{bookSlug}", bookSlug);
        api = appendPaging(api, offset, limit);
        log.info("语雀获取知识库文章列表接口调用:{}", api);
        HttpRequest request = HttpUtil.createGet(API_DOMAIN + api);
        addTokenHeader(request, group);

        String body = request.execute().body();
        // log.info("语雀获取知识库文章列表接口返回:{}", body);
        if (StringUtils.isEmpty(body)) {
            throw FrameworkException.instance("语雀获取知识库文章详情列表调用失败,返回结果为空");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        Integer status = jsonObject.getInteger("status");
        if (status != null && status == 404) {
            return null;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        return data.toJavaList(YuQueDockListResp.class);
    }

    /**
     * 获取库中的文章详情
     *
     * @return
     */
    public YuQueDockDetailResp getDocDetail(String group, String bookSlug, String docSlug) {

        String api = API_REPOS_DOCS_DETAIL.replace("{groupLogin}", group).replace("{bookSlug}", bookSlug).replace("{docSlug}", docSlug);

        log.info("语雀获取知识库文章详情接口调用:{}", api);
        HttpRequest request = HttpUtil.createGet(API_DOMAIN + api);
        addTokenHeader(request, group);

        String body = request.execute().body();
        // log.info("语雀获取知识库文章详情接口返回:{}", body);
        if (StringUtils.isEmpty(body)) {
            throw FrameworkException.instance("语雀获取知识库文章详情接口调用失败,返回结果为空");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        Integer status = jsonObject.getInteger("status");
        if (status != null && status == 404) {
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        return data.toJavaObject(YuQueDockDetailResp.class);
    }


    /**
     * 添加请求头
     * @param httpRequest
     * @param group
     */
    private void addTokenHeader(HttpRequest httpRequest, String group) {
        String token = YuQueGroupTokenEnum.getTokenByGroup(group);
        httpRequest.addHeaders(Maps.of("X-Auth-Token", token));
    }



    /**
     * 添加分页参数
     *
     * @param url
     * @param offset
     * @param limit
     * @return
     */
    public String appendPaging(String url, Integer offset, Integer limit) {
        offset = offset == null ? 0 : offset;
        limit = limit == null ? 100 : limit;

        return url.replace("{offset}", String.valueOf(offset)).replace("{limit}", String.valueOf(limit));
    }

    public String getDocUrl(String group, String repoSlug, String docUrl) {
        return API_DOMAIN + "/" + group + "/" + repoSlug + "/" + docUrl;
    }

}
