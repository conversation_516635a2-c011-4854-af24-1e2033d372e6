package com.qnvip.qwen.component.feishu.response;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

/**
 * 飞书API基础响应模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2024/03/21
 */
@Data
public class BaseResponse<T> {

    @JSONField(name = "code")
    private Integer code;

    @JSONField(name = "msg")
    private String msg;

    @JSONField(name = "data")
    private T data;
}
