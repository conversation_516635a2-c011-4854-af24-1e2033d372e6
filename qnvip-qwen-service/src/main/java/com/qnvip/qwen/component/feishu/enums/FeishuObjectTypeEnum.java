package com.qnvip.qwen.component.feishu.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum FeishuObjectTypeEnum {

    // 文档
    DOC("doc", "文档"), DOCX("docx", "文档"),
    // 电子表格
    SHEET("sheet", "电子表格"),
    // 思维导图
    MINDNOTE("mindnote", "思维导图"),
    // 多维表格
    BITABLE("bitable", "多维表格"),
    // 文件
    FILE("file", "文件"),
    // 文件夹
    FOLDER("folder", "文件夹");

    private final String code;
    private final String desc;

    public static FeishuObjectTypeEnum getByCode(String code) {
        for (FeishuObjectTypeEnum value : FeishuObjectTypeEnum.values()) {
            if (code.equals(value.code)) {
                return value;
            }
        }
        return null;
    }

}