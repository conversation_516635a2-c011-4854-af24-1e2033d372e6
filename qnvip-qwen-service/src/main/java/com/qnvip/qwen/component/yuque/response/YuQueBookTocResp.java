package com.qnvip.qwen.component.yuque.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月26日 10:42:00
 */
@Data
public class YuQueBookTocResp {
    @J<PERSON>NField(name = "uuid")
    private String uuid;

    @JSO<PERSON>ield(name = "type")
    private String type;

    @JSONField(name = "title")
    private String title;

    @J<PERSON><PERSON>ield(name = "url")
    private String url;

    @JSONField(name = "slug")
    private String slug;

    @JSONField(name = "id")
    private Integer id;

    @J<PERSON><PERSON>ield(name = "doc_id")
    private Integer docId;

    @J<PERSON>NField(name = "level")
    private Integer level;

    @JSONField(name = "depth")
    private Integer depth;

    @J<PERSON>NField(name = "open_window")
    private Integer openWindow;

    @JSONField(name = "visible")
    private Integer visible;

    @J<PERSON>NField(name = "prev_uuid")
    private String prevUuid;

    @JSONField(name = "sibling_uuid")
    private String siblingUuid;

    @J<PERSON><PERSON>ield(name = "child_uuid")
    private String childUuid;

    @J<PERSON><PERSON>ield(name = "parent_uuid")
    private String parentUuid;

    @JSONField(name = "_serializer")
    private String serializer;

    List<YuQueBookTocResp> childList;
    
}
