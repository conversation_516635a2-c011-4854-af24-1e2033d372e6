package com.qnvip.qwen.component.feishu.response;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.Data;

/**
 * 飞书空间节点列表响应模型
 *
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2024/03/21
 */
@Data
public class SpaceNodeListResponse {

    /**
     * 是否还有更多数据
     */
    @JSONField(name = "has_more")
    private Boolean hasMore;

    /**
     * 节点列表
     */
    @JSONField(name = "items")
    private List<NodeItem> items;

    /**
     * 分页标记，用于获取下一页数据
     */
    @JSONField(name = "page_token")
    private String pageToken;

    @Data
    public static class NodeItem {
        /**
         * 节点创建者
         */
        @JSONField(name = "creator")
        private String creator;

        /**
         * 是否有子节点
         */
        @JSONField(name = "has_child")
        private Boolean hasChild;

        /**
         * 节点创建时间
         */
        @JSONField(name = "node_create_time")
        private String nodeCreateTime;

        /**
         * 节点token
         */
        @JSONField(name = "node_token")
        private String nodeToken;

        /**
         * 节点类型 可选值： - origin：原始节点 - shortcut：快捷方式
         */
        @JSONField(name = "node_type")
        private String nodeType;

        /**
         * 对象创建时间
         */
        @JSONField(name = "obj_create_time")
        private String objCreateTime;
        /**
         * 对象编辑时间
         */
        @JSONField(name = "obj_edit_time")
        private String objEditTime;
        /**
         * 对象token
         */
        @JSONField(name = "obj_token")
        private String objToken;
        /**
         * 对象类型 可选值： - doc：文档 - sheet：电子表格 - mindnote：思维导图 - bitable：多维表格 - file：文件 - folder：文件夹
         */
        @JSONField(name = "obj_type")
        private String objType;
        /**
         * 原始节点token
         */
        @JSONField(name = "origin_node_token")
        private String originNodeToken;
        /**
         * 原始空间ID
         */
        @JSONField(name = "origin_space_id")
        private String originSpaceId;
        /**
         * 节点所有者
         */
        @JSONField(name = "owner")
        private String owner;
        /**
         * 父节点token
         */
        @JSONField(name = "parent_node_token")
        private String parentNodeToken;
        /**
         * 空间ID
         */
        @JSONField(name = "space_id")
        private String spaceId;
        /**
         * 节点标题
         */
        @JSONField(name = "title")
        private String title;

        public LocalDateTime getObjCreateTimeFmt() {
            if (objCreateTime == null) {
                return null;
            }
            return LocalDateTimeUtil.of(Long.parseLong(objEditTime) * 1000, ZoneOffset.ofHours(8));
        }

        public LocalDateTime getObjEditTimeFmt() {
            if (objEditTime == null) {
                return null;
            }
            return LocalDateTimeUtil.of(Long.parseLong(objEditTime) * 1000, ZoneOffset.ofHours(8));
        }
    }
}