package com.qnvip.qwen.component.yuque.enums;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月25日 16:50:00
 */
@RequiredArgsConstructor
@Getter
public enum YuQueGroupTokenEnum {

    //技术部
    TECHNICAL("pokyqd", "P7z781M1jlkCoRMs7NzjtaAuqlT9cpHDUBr99lVk"),
    //产品部
    PRODUCT("tbli8d", "fyXrBj02ODnm8C7PHCZt1FfrjbAK2WiGBDIpWnDG"),
    // 大数据
    DATA("uu5e85", "t6U3UZOdJgi6y4rORtK0VvWs9fXzR13nxzYK85gx"),
    // 金融
    FINANCE("mddqxg", "87v6q9XXeWCcz0KhKiM9Tiok2klRQhI4DxO21P6F"),
    // 风控
    ALCHEMIST("mqtzyc", "qyjSfca3JKWRgwRtj1h3WGSVJaC0pIx3sBgvuPAg"),

    ;

    private final String group;
    private final String token;

    public static String getTokenByGroup(String group) {
        for (YuQueGroupTokenEnum values : YuQueGroupTokenEnum.values()) {
            if (Objects.equals(values.group, group)) {
                return values.getToken();
            }
        }
        return StringUtils.EMPTY;
    }
}
