package com.qnvip.qwen.component.feishu.response;

import java.util.Map;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

/**
 * 飞书导出任务状态响应模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2024/03/21
 */
@Data
public class ExportTaskStatusResponse {

    /**
     * 导出任务结果
     */
    @JSONField(name = "result")
    private ExportTaskResult result;

    @Data
    public static class ExportTaskResult {
        /**
         * 额外信息
         */
        @JSONField(name = "extra")
        private Map<String, Object> extra;

        /**
         * 导出文件扩展名
         */
        @JSONField(name = "file_extension")
        private String fileExtension;

        /**
         * 导出文件名
         */
        @JSONField(name = "file_name")
        private String fileName;

        /**
         * 导出文件大小（字节）
         */
        @JSONField(name = "file_size")
        private Long fileSize;

        /**
         * 导出文件token
         */
        @JSONField(name = "file_token")
        private String fileToken;

        /**
         * 任务错误信息
         */
        @JSONField(name = "job_error_msg")
        private String jobErrorMsg;

        /**
         * 任务状态 可选值： - 0：成功 - 1：失败 - 2：处理中
         */
        @JSONField(name = "job_status")
        private Integer jobStatus;

        /**
         * 文件类型 可选值： - doc：文档 - sheet：电子表格 - mindnote：思维导图 - bitable：多维表格
         */
        @JSONField(name = "type")
        private String type;
    }
}