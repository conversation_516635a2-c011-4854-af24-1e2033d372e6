package com.qnvip.qwen.component.yuque.response;

import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

/**
 * 语雀文档
 */
@Data
public class YuQueDockListResp {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "slug")
    private String slug;

    @JSONField(name = "title")
    private String title;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "cover")
    private String cover;

    @JSONField(name = "user_id")
    private Long userId;

    @JSONField(name = "book_id")
    private Long bookId;

    @JSONField(name = "last_editor_id")
    private Long lastEditorId;

    @JSONField(name = "public")
    private Integer publicValue;

    @JSONField(name = "status")
    private Integer status;

    @JSONField(name = "likes_count")
    private Integer likesCount;

    @JSONField(name = "read_count")
    private Integer readCount;

    @JSONField(name = "comments_count")
    private Integer commentsCount;

    @JSONField(name = "word_count")
    private Integer wordCount;

    @JSONField(name = "created_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime createdAt;

    @JSONField(name = "updated_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime updatedAt;

    @JSONField(name = "content_updated_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime contentUpdatedAt;

    @JSONField(name = "published_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime publishedAt;

    @JSONField(name = "first_published_at", format = "yyyy-MM-dd'T'HH:mm:ss.SSSX")
    private LocalDateTime firstPublishedAt;
}
