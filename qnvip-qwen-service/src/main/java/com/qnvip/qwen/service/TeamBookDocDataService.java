package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamBookDocDataQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.vo.request.TeamBookDocDataReq;
import com.qnvip.qwen.vo.response.TeamBookDocDataResp;

/**
 * 文档表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
public interface TeamBookDocDataService extends BaseService<TeamBookDocDataDO> {

    /**
     * 文档表分页
     *
     * @param query
     * @return
     */
    Pageable<TeamBookDocDataResp> page(TeamBookDocDataQueryDTO query);

    /**
     * 保存文档表
     *
     * @param record
     * @return
     */
    Boolean save(TeamBookDocDataReq record);

    /**
     * 获取文档表详情
     *
     * @param id
     * @return
     */
    TeamBookDocDataResp detail(Long id);

    /**
     * 删除文档表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 根据文档uid获取文档表记录
     * 
     * @param docUid
     * @return
     */
    TeamBookDocDataDO getByDocUid(String docUid);
}
