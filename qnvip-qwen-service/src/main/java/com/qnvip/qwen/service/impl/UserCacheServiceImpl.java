package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.UserCacheDaoService;
import com.qnvip.qwen.dal.dto.UserCacheDTO;
import com.qnvip.qwen.dal.dto.UserCacheQueryDTO;
import com.qnvip.qwen.dal.entity.UserCacheDO;
import com.qnvip.qwen.dal.mapper.UserCacheMapper;
import com.qnvip.qwen.service.UserCacheService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.UserCacheReq;
import com.qnvip.qwen.vo.response.UserCacheResp;

/**
 * 用户缓存表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@Service
public class UserCacheServiceImpl extends BaseServiceImpl<UserCacheMapper, UserCacheDO> implements UserCacheService {

    @Resource
    private UserCacheMapper userCacheMapper;

    @Resource
    private UserCacheDaoService userCacheDaoService;

    @Override
    public Pageable<UserCacheResp> page(UserCacheQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<UserCacheDTO> page = lambdaQuery().setEntity(CopierUtil.copy(query, UserCacheDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, UserCacheResp.class)));
    }

    @Override
    public List<UserCacheResp> list(UserCacheReq query) {
        UserCacheDO filter = CopierUtil.copy(query, UserCacheDO.class);
        List<UserCacheDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        return CopierUtil.copyList(list, UserCacheResp.class);
    }

    @Override
    public Boolean save(UserCacheReq record) {
        UserCacheDO copy = CopierUtil.copy(record, UserCacheDO.class);
        if (!ObjectUtils.isEmpty(record.getBookIds())) {
            copy.setBookIdsStr(record.getBookIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }

        return saveOrUpdate(copy);
    }

    @Override
    public UserCacheResp detail(Long id) {
        UserCacheDO record = get(UserCacheDO::getId, id);
        if (record == null) {
            UserCacheResp userCacheResp = new UserCacheResp();
            userCacheResp.setId(id);
            userCacheResp.setModelId(1L);
            userCacheResp.setUseBookFlag(1);
            userCacheResp.setUseSearchFlag(0);
            userCacheResp.setUseDeepSearchFlag(0);
            userCacheResp.setBookIds(new ArrayList<>());
            return userCacheResp;
        }

        UserCacheResp copy = CopierUtil.copy(record, UserCacheResp.class);
        if (!ObjectUtils.isEmpty(record.getBookIdsStr())) {
            copy.setBookIds(
                Arrays.stream(record.getBookIdsStr().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        } else {
            copy.setBookIds(new ArrayList<>());
        }
        return copy;
    }

    @Override
    public Boolean delete(Long id) {
        return remove(UserCacheDO::getId, id);
    }
}