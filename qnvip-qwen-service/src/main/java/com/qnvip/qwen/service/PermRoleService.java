package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.PermRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleDO;
import com.qnvip.qwen.vo.request.PermRoleReq;
import com.qnvip.qwen.vo.response.AdminAndRoleAssignResp;
import com.qnvip.qwen.vo.response.PermRoleDetailResp;
import com.qnvip.qwen.vo.response.PermRoleResp;
import com.qnvip.qwen.vo.response.TeamResp;

/**
 * 角色表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public interface PermRoleService extends BaseService<PermRoleDO> {

    /**
     * 角色表分页
     *
     * @param query
     * @return
     */
    Pageable<PermRoleResp> page(PermRoleQueryDTO query);

    /**
     * 保存角色表
     *
     * @param record
     * @return
     */
    Boolean save(PermRoleReq record);

    /**
     * 获取角色表详情
     *
     * @param id
     * @return
     */
    PermRoleResp detail(Long id);

    /**
     * 删除角色表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 获取角色详情，包含关联的知识库信息
     *
     * @param id 角色ID
     * @return 角色详情
     */
    PermRoleDetailResp getDetail(Long id);

    /**
     * 获取已分配的用户的列表
     *
     * @param id
     * @param adminAndRoleAssignResps
     * @return
     */
    List<AdminAndRoleAssignResp> putIsPuckedRole(Long id, List<AdminAndRoleAssignResp> adminAndRoleAssignResps);

    /**
     * 根据角色获取用户id
     */
    List<Long> getUserIdsByRoleId(Long roleId);

    /**
     * 获取团队的知识库
     * 
     * @return
     */
    List<TeamResp> getTeamBooks();

    /**
     * 角色列表
     * 
     * @param req
     * @return
     */
    List<PermRoleResp> list(PermRoleReq req);
}
