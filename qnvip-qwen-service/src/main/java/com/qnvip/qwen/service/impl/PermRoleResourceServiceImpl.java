package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.PermRoleResourceDaoService;
import com.qnvip.qwen.dal.dto.PermRoleResourceDTO;
import com.qnvip.qwen.dal.dto.PermRoleResourceQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleResourceDO;
import com.qnvip.qwen.dal.entity.PermUserRoleDO;
import com.qnvip.qwen.dal.mapper.PermRoleResourceMapper;
import com.qnvip.qwen.enums.ResourceTypeEnum;
import com.qnvip.qwen.service.PermRoleResourceService;
import com.qnvip.qwen.service.PermRoleService;
import com.qnvip.qwen.service.PermUserRoleService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.PermRoleReq;
import com.qnvip.qwen.vo.request.PermRoleResourceReq;
import com.qnvip.qwen.vo.request.RoleAssignBooksReq;
import com.qnvip.qwen.vo.response.PermRoleResourceResp;

/**
 * 角色资源表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Service
public class PermRoleResourceServiceImpl extends BaseServiceImpl<PermRoleResourceMapper, PermRoleResourceDO>
    implements PermRoleResourceService {

    @Resource
    private PermRoleResourceMapper permRoleResourceMapper;

    @Resource
    private PermRoleResourceDaoService permRoleResourceDaoService;

    @Resource
    private PermRoleService permRoleService;

    @Resource
    private PermUserRoleService permUserRoleService;

    @Override
    public Pageable<PermRoleResourceResp> page(PermRoleResourceQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<PermRoleResourceDTO> page = permRoleResourceDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, PermRoleResourceResp.class)));
    }

    @Override
    public Boolean save(PermRoleResourceReq record) {
        return save(CopierUtil.copy(record, PermRoleResourceDO.class));
    }

    @Override
    public PermRoleResourceResp detail(Long id) {
        PermRoleResourceDO record = get(PermRoleResourceDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, PermRoleResourceResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(PermRoleResourceDO::getId, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignBooks(RoleAssignBooksReq req) {
        if (req.getId() == null) {
            // 1. 新增角色
            PermRoleReq roleReq = new PermRoleReq();
            roleReq.setRoleName(req.getRoleName());
            permRoleService.save(roleReq);
            req.setId(roleReq.getId());
        }

        // 2. 删除该角色已有的知识库资源关系
        Long roleId = req.getId();
        remove(new LambdaQueryWrapper<PermRoleResourceDO>().eq(PermRoleResourceDO::getRoleId, roleId)
            .eq(PermRoleResourceDO::getResourceType, ResourceTypeEnum.KNOWLEDGE_BASE.getCode())); // 1表示知识库资源类型

        // 3. 创建新的角色-知识库关系
        List<PermRoleResourceDO> roleResources = req.getBookIds().stream().map(bookId -> {
            PermRoleResourceDO roleResource = new PermRoleResourceDO();
            roleResource.setRoleId(roleId);
            roleResource.setResourceId(bookId);
            roleResource.setResourceType(1); // 1表示知识库资源类型
            return roleResource;
        }).collect(Collectors.toList());

        // 4. 批量保存新的角色-知识库关系
        return saveBatch(roleResources);
    }


    @Override
    public List<Long> getUserKnowledgeBaseIds(Long userId) {
        // 1. 获取用户的所有角色ID
        List<PermUserRoleDO> userRoles =
            permUserRoleService.list(new LambdaQueryWrapper<PermUserRoleDO>().eq(PermUserRoleDO::getUserId, userId));

        if (userRoles.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> roleIds = userRoles.stream().map(PermUserRoleDO::getRoleId).collect(Collectors.toList());

        // 2. 获取这些角色对应的知识库资源
        List<PermRoleResourceDO> roleResources = list(new LambdaQueryWrapper<PermRoleResourceDO>()
            .in(PermRoleResourceDO::getRoleId, roleIds).eq(PermRoleResourceDO::getResourceType, 1) // 1表示知识库资源类型
        );

        // 3. 提取知识库ID列表
        return roleResources.stream().map(PermRoleResourceDO::getResourceId).distinct().collect(Collectors.toList());
    }
}