package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.UserTokenConfigQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConfigDO;
import com.qnvip.qwen.vo.request.UserTokenConfigReq;
import com.qnvip.qwen.vo.response.AdminAndTokenInfoResp;
import com.qnvip.qwen.vo.response.UserTokenConfigResp;

/**
 * 用户token用量配置表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
public interface UserTokenConfigService extends BaseService<UserTokenConfigDO> {

    /**
     * 用户token用量配置表分页
     *
     * @param query
     * @return
     */
    Pageable<UserTokenConfigResp> page(UserTokenConfigQueryDTO query);

    /**
     * 保存用户token用量配置表
     *
     * @param record
     * @return
     */
    Boolean save(UserTokenConfigReq record);

    /**
     * 获取用户token用量配置表详情
     *
     * @param id
     * @return
     */
    UserTokenConfigResp detail(Long id);

    /**
     * 删除用户token用量配置表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 放入token使用信息
     * 
     * @param objects
     */
    void putTokenInfo(List<AdminAndTokenInfoResp> objects);

    /**
     * 重置所有用户的已使用token为0
     * 
     * @return 是否成功
     */
    Boolean resetAllUsedToken();

    /**
     * 更新用户Token配置
     *
     * @param userId 用户ID
     * @param permissionAuthorizedToken 授权Token数量
     * @return 是否成功
     */
    Boolean updateUserTokenConfig(Long userId, Long permissionAuthorizedToken);

    /**
     * 增加已使用Token
     * 
     * @param userId
     * @param totalConsumedToken
     */
    void increaseUsedToken(Long userId, Long totalConsumedToken);

    /**
     * token是否有剩余
     * 
     * @param loginUserId
     * @return
     */
    boolean hasToken(Long loginUserId);
}
