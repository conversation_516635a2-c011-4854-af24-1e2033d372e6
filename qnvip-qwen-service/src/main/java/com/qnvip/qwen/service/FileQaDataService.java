package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileQaDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileQaDataDO;
import com.qnvip.qwen.vo.request.FileQaDataReq;
import com.qnvip.qwen.vo.response.FileQaDataResp;

/**
 * 文件问答数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
public interface FileQaDataService extends BaseService<FileQaDataDO> {

    /**
     * 文件问答数据表分页
     *
     * @param query
     * @return
     */
    Pageable<FileQaDataResp> page(FileQaDataQueryDTO query);

    /**
     * 保存文件问答数据表
     *
     * @param record
     * @return
     */
    Boolean save(FileQaDataReq record);

    /**
     * 获取文件问答数据表详情
     *
     * @param id
     * @return
     */
    FileQaDataResp detail(Long id);

    /**
     * 删除文件问答数据表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
