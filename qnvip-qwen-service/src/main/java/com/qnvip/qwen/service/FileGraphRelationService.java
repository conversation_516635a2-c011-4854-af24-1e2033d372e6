package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileGraphRelationQueryDTO;
import com.qnvip.qwen.dal.entity.FileGraphRelationDO;
import com.qnvip.qwen.vo.request.FileGraphRelationReq;
import com.qnvip.qwen.vo.response.FileGraphRelationResp;

/**
 * 文件图数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
public interface FileGraphRelationService extends BaseService<FileGraphRelationDO> {

    /**
     * 文件图数据表分页
     *
     * @param query
     * @return
     */
    Pageable<FileGraphRelationResp> page(FileGraphRelationQueryDTO query);

    /**
     * 文件图数据表列表
     *
     * @param query
     * @return
     */
    List<FileGraphRelationResp> list(FileGraphRelationReq query);

    /**
     * 保存文件图数据表
     *
     * @param record
     * @return
     */
    Boolean save(FileGraphRelationReq record);

    /**
     * 获取文件图数据表详情
     *
     * @param id
     * @return
     */
    FileGraphRelationResp detail(Long id);

    /**
     * 删除文件图数据表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    List<FileGraphRelationDO> pageLoadRelation(List<Long> fileOriginIds);
}
