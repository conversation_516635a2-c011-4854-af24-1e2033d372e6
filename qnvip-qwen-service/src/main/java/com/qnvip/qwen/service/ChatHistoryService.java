package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.ChatHistoryQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDO;
import com.qnvip.qwen.vo.request.ChatHistoryLoadChatHistoryListReq;
import com.qnvip.qwen.vo.request.ChatHistoryLoadChatHistoryReq;
import com.qnvip.qwen.vo.request.ChatHistoryReq;
import com.qnvip.qwen.vo.request.DifyMutilRecallReq;
import com.qnvip.qwen.vo.response.ChatHistoryResp;

/**
 * 对话记录表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
public interface ChatHistoryService extends BaseService<ChatHistoryDO> {

    /**
     * 对话记录表分页
     *
     * @param query
     * @return
     */
    Pageable<ChatHistoryResp> page(ChatHistoryQueryDTO query);

    /**
     * 保存对话记录表
     *
     * @param record
     * @return
     */
    Boolean save(ChatHistoryReq record);

    /**
     * 获取对话记录表详情
     *
     * @param id
     * @return
     */
    ChatHistoryResp detail(Long id);

    /**
     * 删除对话记录表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 保存问题
     * 
     * @param chatHistoryReq
     * @return
     */
    Long saveChat(ChatHistoryReq chatHistoryReq);

    /**
     * 加载聊天记录
     * 
     * @param record
     * @return
     */
    String loadChatHistory(ChatHistoryLoadChatHistoryReq record);

    /**
     * 加载聊天记录list
     *
     * @return
     */
    List<String> loadChatHistory(ChatHistoryLoadChatHistoryListReq req);

    /**
     * 加载之前的问题id
     * 
     * @param record
     * @return
     */
    Long loadBeforeQuestionIdByWorkflowRunId(DifyMutilRecallReq record);
}
