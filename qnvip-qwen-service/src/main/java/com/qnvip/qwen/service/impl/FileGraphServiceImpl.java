package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.qnvip.qwen.dal.dao.FileGraphEntityDaoService;
import com.qnvip.qwen.dal.dao.FileGraphRelationDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileGraphExtractDataDTO;
import com.qnvip.qwen.dal.entity.FileGraphEntityDO;
import com.qnvip.qwen.dal.entity.FileGraphRelationDO;
import com.qnvip.qwen.dal.graph.Neo4jService;
import com.qnvip.qwen.enums.CategoryEnum;
import com.qnvip.qwen.service.FileGraphEntityService;
import com.qnvip.qwen.service.FileGraphRelationService;
import com.qnvip.qwen.service.FileGraphService;
import com.qnvip.qwen.util.rag.GraphUtil;

import cn.hutool.core.collection.CollUtil;

/**
 * 文件图数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 16:59
 */
@Service
public class FileGraphServiceImpl implements FileGraphService {

    @Resource
    private FileGraphEntityDaoService fileGraphEntityDaoService;
    @Resource
    private FileGraphRelationDaoService fileGraphRelationDaoService;

    @Resource
    private FileGraphEntityService fileGraphEntityService;
    @Resource
    private FileGraphRelationService fileGraphRelationService;
    @Resource
    private Neo4jService neo4jService;

    private static List<FileGraphExtractDataDTO>
        getFileGraphExtractDataDTOSFromRela(List<FileGraphRelationDO> updRelation) {
        List<FileGraphExtractDataDTO> graphs = new ArrayList<>();
        for (FileGraphRelationDO updRela : updRelation) {
            FileGraphExtractDataDTO data = new FileGraphExtractDataDTO();
            data.setBookId(updRela.getBookId());
            data.setFileOriginId(updRela.getFileOriginId());
            data.setChunkUid(updRela.getChunkUid());
            data.setType(CategoryEnum.RELATIONSHIP.getRealDesc());
            data.setRelStrength(updRela.getStrength());
            data.setSrc(updRela.getSource());
            data.setTgt(updRela.getTarget());
            data.setRelKeys(updRela.getKeyword());
            graphs.add(data);
        }
        return graphs;
    }

    private static List<FileGraphExtractDataDTO> convertEntityToGraph(List<FileGraphEntityDO> updEntities) {
        List<FileGraphExtractDataDTO> graphs = new ArrayList<>();
        for (FileGraphEntityDO updEntity : updEntities) {
            FileGraphExtractDataDTO data = new FileGraphExtractDataDTO();
            data.setBookId(updEntity.getBookId());
            data.setFileOriginId(updEntity.getFileOriginId());
            data.setChunkUid(updEntity.getChunkUid());
            data.setType(CategoryEnum.ENTITY.getRealDesc());

            data.setEntName(updEntity.getName());
            data.setEntType(updEntity.getType());
            graphs.add(data);
        }
        return graphs;
    }

    @Override
    public void saveBatchToDb(List<FileGraphExtractDataDTO> graphs) {
        List<FileGraphEntityDO> entityDTOS = new ArrayList<>();
        List<FileGraphRelationDO> relationDTOS = new ArrayList<>();
        for (FileGraphExtractDataDTO graph : graphs) {

            if (CategoryEnum.ENTITY.getDesc().contains(graph.getType())) {
                FileGraphEntityDO entity = new FileGraphEntityDO();
                entity.setBookId(graph.getBookId());
                entity.setFileOriginId(graph.getFileOriginId());
                entity.setChunkUid(graph.getChunkUid());
                entity.setName(graph.getEntName());
                entity.setType(graph.getEntType());
                entity.setDescription(graph.getEntDesc());
                entityDTOS.add(entity);

            } else if (CategoryEnum.RELATIONSHIP.getDesc().contains(graph.getType())) {
                FileGraphRelationDO relation = new FileGraphRelationDO();
                relation.setBookId(graph.getBookId());
                relation.setFileOriginId(graph.getFileOriginId());
                relation.setChunkUid(graph.getChunkUid());
                relation.setSource(graph.getSrc());
                relation.setTarget(graph.getTgt());
                relation.setKeyword(graph.getRelKeys());
                relation.setStrength(graph.getRelStrength());
                relation.setDescription(graph.getRelDesc());
                relation.setLinkSortSourceTarget(GraphUtil.generateEdgeKey(relation.getSource(), relation.getTarget()));
                relationDTOS.add(relation);
            }
        }

        // 非空判断再插入
        if (CollUtil.isNotEmpty(entityDTOS)) {
            fileGraphEntityService.saveBatch(entityDTOS);
        }
        if (CollUtil.isNotEmpty(relationDTOS)) {
            fileGraphRelationService.saveBatch(relationDTOS);
        }
    }

    @Override
    public void deleteAndRefreshByFileId(Long fileId) {
        List<FileGraphEntityDO> entities = fileGraphEntityDaoService.listByFileId(fileId);

        // 影响的节点
        List<String> entityNames = entities.stream().map(FileGraphEntityDO::getName).collect(Collectors.toList());

        fileGraphEntityDaoService.deleteByFileOriginId(fileId);
        fileGraphRelationDaoService.deleteByFileOriginId(fileId);

        List<FileGraphRelationDO> updRelation =
            fileGraphRelationDaoService.listSimpleBySourceNameOrTargetName(entityNames);

        neo4jService.deleteByFileOriginId(fileId);

        saveBatchToGraphByRelation(getFileGraphExtractDataDTOSFromRela(updRelation));
    }

    @Override
    public List<FileGraphExtractDataDTO> pageLoadEntity(List<Long> param) {
        List<FileGraphEntityDO> entityDOS = fileGraphEntityService.pageLoadEntity(param);

        return convertEntityToGraph(entityDOS);

    }

    @Override
    public List<FileGraphExtractDataDTO> pageLoadRelation(List<Long> fileOriginIds) {
        List<FileGraphRelationDO> relationDOS = fileGraphRelationService.pageLoadRelation(fileOriginIds);

        return getFileGraphExtractDataDTOSFromRela(relationDOS);
    }

    @Override
    public void saveBatchToGraphByRelation(List<FileGraphExtractDataDTO> graphs) {
        List<FileGraphExtractDataDTO> relations =
            graphs.stream().filter(graph -> CategoryEnum.RELATIONSHIP.getDesc().contains(graph.getType()))
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(relations)) {
            return;
        }

        // 获取关系需要的所有实体
        Set<String> sourceEntities =
            relations.stream().map(FileGraphExtractDataDTO::getSrc).collect(Collectors.toSet());
        Set<String> targetEntities =
            relations.stream().map(FileGraphExtractDataDTO::getTgt).collect(Collectors.toSet());
        sourceEntities.addAll(targetEntities);

        // 获取一个由于保存关系
        List<FileGraphEntityDO> updEntities = fileGraphEntityService.groupListByName(sourceEntities);

        // 实体转保存模型
        List<FileGraphExtractDataDTO> entities = convertEntityToGraph(updEntities);;

        neo4jService.saveBatch(entities);
        neo4jService.saveBatch(relations);
    }

    @Override
    public void deleteGraphAll() {
        neo4jService.deleteAll();
    }

    @Override
    public List<Long> getDistinctOriginId(DataInitSearchDTO param) {
        return fileGraphRelationDaoService.getDistinctOriginId(param);
    }

}