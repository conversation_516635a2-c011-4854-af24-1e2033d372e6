package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.DifyAppApikeyDaoService;
import com.qnvip.qwen.dal.dto.DifyAppApikeyDTO;
import com.qnvip.qwen.dal.dto.DifyAppApikeyQueryDTO;
import com.qnvip.qwen.dal.entity.DifyAppApikeyDO;
import com.qnvip.qwen.dal.mapper.DifyAppApikeyMapper;
import com.qnvip.qwen.service.DifyAppApikeyService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.DifyWorkflowTypeEnum;
import com.qnvip.qwen.vo.request.DifyAppApikeyReq;
import com.qnvip.qwen.vo.response.DifyAppApikeyResp;
import com.qnvip.qwen.vo.response.LLMUrlTokenResp;

/**
 * dify应用的apikey表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
@Service
public class DifyAppApikeyServiceImpl extends BaseServiceImpl<DifyAppApikeyMapper, DifyAppApikeyDO>
    implements DifyAppApikeyService {

    @Resource
    private DifyAppApikeyMapper difyAppApikeyMapper;

    @Resource
    private DifyAppApikeyDaoService difyAppApikeyDaoService;

    @Override
    public Pageable<DifyAppApikeyResp> page(DifyAppApikeyQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<DifyAppApikeyDTO> page = lambdaQuery().setEntity(CopierUtil.copy(query, DifyAppApikeyDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, DifyAppApikeyResp.class)));
    }

    @Override
    public List<DifyAppApikeyResp> list(DifyAppApikeyReq query) {
        DifyAppApikeyDO filter = CopierUtil.copy(query, DifyAppApikeyDO.class);
        List<DifyAppApikeyDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        return CopierUtil.copyList(list, DifyAppApikeyResp.class);
    }

    @Override
    public Boolean save(DifyAppApikeyReq record) {
        return saveOrUpdate(CopierUtil.copy(record, DifyAppApikeyDO.class));
    }

    @Override
    public DifyAppApikeyResp detail(Long id) {
        DifyAppApikeyDO record = get(DifyAppApikeyDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, DifyAppApikeyResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(DifyAppApikeyDO::getId, id);
    }

    @Override
    public LLMUrlTokenResp getByModelId(Integer modelId, String type) {
        if (type.equals(DifyWorkflowTypeEnum.CHAT.getDesc())) {
            DifyAppApikeyDO one = lambdaQuery().eq(DifyAppApikeyDO::getModelId, modelId)
                .eq(DifyAppApikeyDO::getType, type).last("limit 1").one();
            return CopierUtil.copy(one, LLMUrlTokenResp.class);
        } else {
            DifyAppApikeyDO one = lambdaQuery().eq(DifyAppApikeyDO::getType, type).last("limit 1").one();
            return CopierUtil.copy(one, LLMUrlTokenResp.class);
        }
    }
}