package com.qnvip.qwen.service.open;

import java.util.List;

import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.entity.open.ItemKeywordDO;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月22日 16:18:00
 */
public interface ItemKeywordService extends BaseService<ItemKeywordDO> {

    /**
     * 根据ID列表获取关键词列表
     *
     * @param ids ID集合
     * @return 关键词列表
     */
    List<ItemKeywordDO> getListByIds(List<Long> ids);

    /**
     * 根据平台ID获取关键词列表
     *
     * @param platformId 平台ID
     * @return 关键词列表
     */
    List<ItemKeywordDO> getByPlatformId(Long platformId);
}
