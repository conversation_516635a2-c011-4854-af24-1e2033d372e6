package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dto.FileChunkDTO;
import com.qnvip.qwen.dal.dto.FileChunkQueryDTO;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.dal.mapper.FileChunkMapper;
import com.qnvip.qwen.service.FileChunkService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileChunkReq;
import com.qnvip.qwen.vo.response.FileChunkResp;


/**
 * 文件切块表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
@Service
public class FileChunkServiceImpl extends BaseServiceImpl<FileChunkMapper, FileChunkDO> implements FileChunkService {

    @Resource
    private FileChunkMapper fileChunkMapper;

    @Resource
    private FileChunkDaoService fileChunkDaoService;

    @Override
    public Pageable<FileChunkResp> page(FileChunkQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileChunkDTO> page = fileChunkDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileChunkResp.class)));
    }

    @Override
    public Boolean save(FileChunkReq record) {
        return save(CopierUtil.copy(record, FileChunkDO.class));
    }

    @Override
    public FileChunkResp detail(Long id) {
        FileChunkDO record = get(FileChunkDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileChunkResp.class);
    }


    @Override
    public Boolean delete(Long id) {
        return remove(FileChunkDO::getId, id);
    }
}