package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qnvip.qwen.dal.dto.FileGraphExtractDataDTO;
import com.qnvip.qwen.dal.dto.RagGraphEntityDTO;
import com.qnvip.qwen.dal.dto.RagGraphRelationDTO;
import com.qnvip.qwen.dal.milvus.MilvusGraphEntityService;
import com.qnvip.qwen.dal.milvus.MilvusGraphRelationService;
import com.qnvip.qwen.enums.CategoryEnum;
import com.qnvip.qwen.service.GraphMilvusService;

import cn.hutool.core.collection.CollUtil;

@Service
public class GraphMilvusServiceImpl implements GraphMilvusService {

    @Resource
    private MilvusGraphEntityService milvusGraphEntityService;

    @Resource
    private MilvusGraphRelationService milvusGraphRelationService;

    @Override
    public void saveBatchToMilvus(List<FileGraphExtractDataDTO> graphs) {

        List<RagGraphEntityDTO> entityDTOS = new ArrayList<>();
        List<RagGraphRelationDTO> relationDTOS = new ArrayList<>();
        for (FileGraphExtractDataDTO graph : graphs) {

            if (CategoryEnum.ENTITY.getDesc().contains(graph.getType())) {
                RagGraphEntityDTO entity = new RagGraphEntityDTO();
                entity.setBookId(graph.getBookId());
                entity.setFileOriginId(graph.getFileOriginId());
                entity.setChunkUid(graph.getChunkUid());
                entity.setEntityName(graph.getEntName());
                entity.setEntityType(graph.getEntType());
                entity.setDescription(graph.getEntDesc());
                entityDTOS.add(entity);

            } else if (CategoryEnum.RELATIONSHIP.getDesc().contains(graph.getType())) {

                RagGraphRelationDTO relation = new RagGraphRelationDTO();
                relation.setBookId(graph.getBookId());
                relation.setFileOriginId(graph.getFileOriginId());
                relation.setChunkUid(graph.getChunkUid());
                relation.setSrcId(graph.getSrc());
                relation.setTgtId(graph.getTgt());
                relation.setKeywords(graph.getRelKeys());
                relation.setDescription(graph.getRelDesc());
                relationDTOS.add(relation);
            }
        }

        // 非空判断再插入
        if (CollUtil.isNotEmpty(entityDTOS)) {
            milvusGraphEntityService.insert(entityDTOS);
        }
        if (CollUtil.isNotEmpty(relationDTOS)) {
            milvusGraphRelationService.insert(relationDTOS);
        }
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        milvusGraphEntityService.deleteByFileOriginIds(fileOriginIds);
        milvusGraphRelationService.deleteByFileOriginIds(fileOriginIds);
    }

}
