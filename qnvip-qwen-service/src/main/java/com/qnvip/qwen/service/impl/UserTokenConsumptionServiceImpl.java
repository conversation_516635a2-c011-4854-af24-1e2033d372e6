package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.UserTokenConsumptionDaoService;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionDTO;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConsumptionDO;
import com.qnvip.qwen.dal.mapper.UserTokenConsumptionMapper;
import com.qnvip.qwen.service.UserTokenConfigService;
import com.qnvip.qwen.service.UserTokenConsumptionService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.TokenCalculator;
import com.qnvip.qwen.vo.request.UserTokenConsumptionReq;
import com.qnvip.qwen.vo.response.UserTokenConsumptionResp;

/**
 * 用户token消耗表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
@Service
public class UserTokenConsumptionServiceImpl extends BaseServiceImpl<UserTokenConsumptionMapper, UserTokenConsumptionDO>
    implements UserTokenConsumptionService {

    @Resource
    private UserTokenConsumptionMapper userTokenConsumptionMapper;

    @Resource
    private UserTokenConsumptionDaoService userTokenConsumptionDaoService;

    @Resource
    private UserTokenConfigService userTokenConfigService;

    @Override
    public Pageable<UserTokenConsumptionResp> page(UserTokenConsumptionQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<UserTokenConsumptionDTO> page =
            userTokenConsumptionDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils
            .convertFrom(page.convert(item -> CopierUtil.copy(item, UserTokenConsumptionResp.class)));
    }

    @Override
    public Boolean save(UserTokenConsumptionReq record, boolean isSystemRecord) {
        UserTokenConsumptionDO data = CopierUtil.copy(record, UserTokenConsumptionDO.class);
        data.setUserInputPrefix(
            record.getUserInput().length() > 255 ? record.getUserInput().substring(0, 255) : record.getUserInput());
        if (!isSystemRecord) {
            userTokenConfigService.increaseUsedToken(record.getUserId(), record.getTotalConsumedToken());
        }
        return save(data);
    }

    @Override
    public UserTokenConsumptionResp detail(Long id) {
        UserTokenConsumptionDO record = get(UserTokenConsumptionDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, UserTokenConsumptionResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(UserTokenConsumptionDO::getId, id);
    }

    @Override
    public Boolean saveSystem(UserTokenConsumptionReq record, boolean isSystemRecord) {
        record.setInputConsumedToken(TokenCalculator.calculateMixedTokens(record.getLlmInput()));
        record.setOutputConsumedToken(TokenCalculator.calculateMixedTokens(record.getLlmOutput()));
        record.setTotalConsumedToken(record.getInputConsumedToken() + record.getOutputConsumedToken());
        return save(record, isSystemRecord);
    }
}