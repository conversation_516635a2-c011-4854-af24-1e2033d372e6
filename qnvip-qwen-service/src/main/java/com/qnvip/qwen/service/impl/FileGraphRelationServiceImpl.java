package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileGraphRelationDaoService;
import com.qnvip.qwen.dal.dto.FileGraphRelationDTO;
import com.qnvip.qwen.dal.dto.FileGraphRelationQueryDTO;
import com.qnvip.qwen.dal.entity.FileGraphRelationDO;
import com.qnvip.qwen.dal.mapper.FileGraphRelationMapper;
import com.qnvip.qwen.service.FileGraphRelationService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileGraphRelationReq;
import com.qnvip.qwen.vo.response.FileGraphRelationResp;

/**
 * 文件图数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
@Service
public class FileGraphRelationServiceImpl extends BaseServiceImpl<FileGraphRelationMapper, FileGraphRelationDO>
    implements FileGraphRelationService {

    @Resource
    private FileGraphRelationMapper fileGraphRelationMapper;

    @Resource
    private FileGraphRelationDaoService fileGraphRelationDaoService;

    @Override
    public Pageable<FileGraphRelationResp> page(FileGraphRelationQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileGraphRelationDTO> page = lambdaQuery().setEntity(CopierUtil.copy(query, FileGraphRelationDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileGraphRelationResp.class)));
    }

    @Override
    public List<FileGraphRelationResp> list(FileGraphRelationReq query) {
        FileGraphRelationDO filter = CopierUtil.copy(query, FileGraphRelationDO.class);
        List<FileGraphRelationDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        return CopierUtil.copyList(list, FileGraphRelationResp.class);
    }

    @Override
    public Boolean save(FileGraphRelationReq record) {
        return saveOrUpdate(CopierUtil.copy(record, FileGraphRelationDO.class));
    }

    @Override
    public FileGraphRelationResp detail(Long id) {
        FileGraphRelationDO record = get(FileGraphRelationDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileGraphRelationResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(FileGraphRelationDO::getId, id);
    }

    @Override
    public List<FileGraphRelationDO> pageLoadRelation(List<Long> fileOriginIds) {
        if (ObjectUtils.isEmpty(fileOriginIds)) {
            return new ArrayList<>();
        }

        return lambdaQuery()
            .in(!ObjectUtils.isEmpty(fileOriginIds), FileGraphRelationDO::getFileOriginId, fileOriginIds).list();
    }

}