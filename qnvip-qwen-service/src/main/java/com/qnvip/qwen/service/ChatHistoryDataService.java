package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.ChatHistoryDataQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDataDO;
import com.qnvip.qwen.vo.request.ChatHistoryDataReq;
import com.qnvip.qwen.vo.response.ChatHistoryChatContextResp;
import com.qnvip.qwen.vo.response.ChatHistoryDataResp;

/**
 * 对话记录表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
public interface ChatHistoryDataService extends BaseService<ChatHistoryDataDO> {

    /**
     * 对话记录表分页
     *
     * @param query
     * @return
     */
    Pageable<ChatHistoryDataResp> page(ChatHistoryDataQueryDTO query);

    /**
     * 保存对话记录表
     *
     * @param record
     * @return
     */
    Boolean save(ChatHistoryDataReq record);

    /**
     * 获取对话记录表详情
     *
     * @param id
     * @return
     */
    ChatHistoryDataResp detail(Long id);

    /**
     * 删除对话记录表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    void setData(List<ChatHistoryChatContextResp> chatHistoryChatContextResps);
}
