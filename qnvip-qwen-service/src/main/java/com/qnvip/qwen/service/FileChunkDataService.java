package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileChunkDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.vo.request.FileChunkDataReq;
import com.qnvip.qwen.vo.response.FileChunkDataResp;

/**
 * 文件切块数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
public interface FileChunkDataService extends BaseService<FileChunkDataDO> {

    /**
     * 文件切块数据表分页
     *
     * @param query
     * @return
     */
    Pageable<FileChunkDataResp> page(FileChunkDataQueryDTO query);

    /**
     * 保存文件切块数据表
     *
     * @param record
     * @return
     */
    Boolean save(FileChunkDataReq record);

    /**
     * 获取文件切块数据表详情
     *
     * @param id
     * @return
     */
    FileChunkDataResp detail(Long id);

    /**
     * 删除文件切块数据表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
