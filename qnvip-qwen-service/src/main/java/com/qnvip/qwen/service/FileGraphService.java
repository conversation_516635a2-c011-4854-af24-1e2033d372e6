package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileGraphExtractDataDTO;

/**
 * 文件图数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 16:59
 */
public interface FileGraphService {

    void saveBatchToDb(List<FileGraphExtractDataDTO> graphs);

    void deleteAndRefreshByFileId(Long fileId);

    List<FileGraphExtractDataDTO> pageLoadEntity(List<Long> param);

    List<FileGraphExtractDataDTO> pageLoadRelation(List<Long> fileOriginIds);

    /**
     * ！！！图谱唯一保存方法，存关系，同时找出关系对应的节点一起保存！！！ 禁止此方法外的 neo4jService.saveBatch(graphs);调用 否则会放入至少5倍的无连接的节点不能形成关系
     * 
     * @param graphs
     */
    void saveBatchToGraphByRelation(List<FileGraphExtractDataDTO> graphs);

    void deleteGraphAll();

    List<Long> getDistinctOriginId(DataInitSearchDTO param);
}
