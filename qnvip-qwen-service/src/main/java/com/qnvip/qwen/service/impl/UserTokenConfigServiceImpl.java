package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.UserTokenConfigDaoService;
import com.qnvip.qwen.dal.dto.UserTokenConfigDTO;
import com.qnvip.qwen.dal.dto.UserTokenConfigQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConfigDO;
import com.qnvip.qwen.dal.mapper.UserTokenConfigMapper;
import com.qnvip.qwen.service.UserTokenConfigService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.UserTokenConfigReq;
import com.qnvip.qwen.vo.response.AdminAndTokenInfoResp;
import com.qnvip.qwen.vo.response.UserTokenConfigResp;

/**
 * 用户token用量配置表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
@Service
public class UserTokenConfigServiceImpl extends BaseServiceImpl<UserTokenConfigMapper, UserTokenConfigDO>
    implements UserTokenConfigService {

    @Resource
    private UserTokenConfigMapper userTokenConfigMapper;

    @Resource
    private UserTokenConfigDaoService userTokenConfigDaoService;

    @Value("${user.token.limit:1000000}") // 注入配置值，默认值为1000000
    private Long permissionAuthorizedToken;

    @Override
    public Pageable<UserTokenConfigResp> page(UserTokenConfigQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<UserTokenConfigDTO> page = userTokenConfigDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, UserTokenConfigResp.class)));
    }

    @Override
    public Boolean save(UserTokenConfigReq record) {
        return save(CopierUtil.copy(record, UserTokenConfigDO.class));
    }

    @Override
    public UserTokenConfigResp detail(Long id) {
        UserTokenConfigDO record = get(UserTokenConfigDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, UserTokenConfigResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(UserTokenConfigDO::getId, id);
    }

    /**
     * 将管理员和令牌信息对象列表保存到数据库。
     *
     * @param resps 管理员和令牌信息对象列表
     */
    @Override
    public void putTokenInfo(List<AdminAndTokenInfoResp> resps) {
        if (ObjectUtils.isEmpty(resps)) {
            return;
        }

        // 提取所有对象的ID
        List<Integer> ids = resps.stream().map(AdminAndTokenInfoResp::getId).collect(Collectors.toList());

        // 获取现有的 UserTokenConfigDO 记录并转换为 Map
        Map<Long, UserTokenConfigDO> idMap = getUserTokenConfigMap(ids);

        // 将 idMap 复制到一个 final 变量中，确保 Lambda 表达式可以引用
        final Map<Long, UserTokenConfigDO> finalIdMap = new HashMap<>(idMap);

        // 处理新记录
        List<Integer> newIds =
            ids.stream().filter(id -> !finalIdMap.containsKey(id.longValue())).collect(Collectors.toList());
        if (!newIds.isEmpty()) {
            List<UserTokenConfigDO> newUserTokenConfigDOS =
                newIds.stream().map(this::createNewUserTokenConfigDO).collect(Collectors.toList());
            saveBatch(newUserTokenConfigDOS);

            // 重新加载记录并更新 Map
            idMap = getUserTokenConfigMap(ids);
        }

        // 更新现有记录
        updateAdminAndTokenInfoResp(resps, idMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserTokenConfig(Long userId, Long permissionAuthorizedToken) {
        if (userId == null || permissionAuthorizedToken == null || permissionAuthorizedToken <= 0) {
            return false;
        }

        // 查询是否存在配置
        UserTokenConfigDO config = get(UserTokenConfigDO::getId, userId);
        if (config == null) {
            // 不存在则创建
            config = new UserTokenConfigDO();
            config.setId(userId);
            config.setPermissionAuthorizedToken(permissionAuthorizedToken);
            return save(config);
        } else {
            // 存在则更新
            return lambdaUpdate().eq(UserTokenConfigDO::getId, userId)
                .set(UserTokenConfigDO::getPermissionAuthorizedToken, permissionAuthorizedToken).update();
        }
    }

    @Override
    public void increaseUsedToken(Long userId, Long totalConsumedToken) {
        userTokenConfigMapper.increaseUsedToken(userId, totalConsumedToken);
    }

    @Override
    public boolean hasToken(Long loginUserId) {
        UserTokenConfigDO before = getById(loginUserId);
        if (before == null) {
            before = new UserTokenConfigDO();
            before.setId(loginUserId);
            before.setUsedToken(0L);
            before.setPermissionAuthorizedToken(permissionAuthorizedToken);
            save(before);
        }
        return before.getUsedToken() < before.getPermissionAuthorizedToken();
    }

    /**
     * 根据给定的用户ID列表，获取对应的用户令牌配置映射
     *
     * @param ids 用户ID列表
     * @return 用户令牌配置映射，键为用户ID，值为对应的用户令牌配置对象
     */
    private Map<Long, UserTokenConfigDO> getUserTokenConfigMap(List<Integer> ids) {
        List<UserTokenConfigDO> userTokenConfigDOS = listByIds(ids);
        return userTokenConfigDOS.stream().collect(Collectors.toMap(UserTokenConfigDO::getId, user -> user));
    }

    private UserTokenConfigDO createNewUserTokenConfigDO(Integer id) {
        UserTokenConfigDO newUserTokenConfigDO = new UserTokenConfigDO();
        newUserTokenConfigDO.setId(id.longValue());
        newUserTokenConfigDO.setPermissionAuthorizedToken(permissionAuthorizedToken);
        newUserTokenConfigDO.setUsedToken(0L);
        return newUserTokenConfigDO;
    }

    private void updateAdminAndTokenInfoResp(List<AdminAndTokenInfoResp> objects, Map<Long, UserTokenConfigDO> idMap) {
        objects.forEach(object -> {
            UserTokenConfigDO userTokenConfigDO = idMap.get(object.getId().longValue());
            if (userTokenConfigDO != null) {
                object.setPermissionAuthorizedToken(userTokenConfigDO.getPermissionAuthorizedToken());
                object.setUsedToken(userTokenConfigDO.getUsedToken());
            }
        });
    }

    @Override
    public Boolean resetAllUsedToken() {
        // 使用LambdaUpdateWrapper更新所有记录的usedToken为0
        return userTokenConfigMapper.resetAllUsedToken();
    }

}