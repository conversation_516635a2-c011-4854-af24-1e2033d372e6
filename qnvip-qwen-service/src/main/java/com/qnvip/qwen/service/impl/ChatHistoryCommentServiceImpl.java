package com.qnvip.qwen.service.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.ChatHistoryCommentDaoService;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDetailDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryCommentDO;
import com.qnvip.qwen.dal.mapper.ChatHistoryCommentMapper;
import com.qnvip.qwen.enums.LikeStatusEnum;
import com.qnvip.qwen.service.ChatHistoryCommentService;
import com.qnvip.qwen.service.DifyAppApikeyService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.ChatHistoryCommentReq;
import com.qnvip.qwen.vo.response.ChatHistoryCommentResp;
import com.qnvip.qwen.vo.response.LLMUrlTokenResp;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 聊天记录评论表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@Service
@Slf4j
public class ChatHistoryCommentServiceImpl extends BaseServiceImpl<ChatHistoryCommentMapper, ChatHistoryCommentDO>
    implements ChatHistoryCommentService {

    @Resource
    private ChatHistoryCommentMapper chatHistoryCommentMapper;

    @Resource
    private ChatHistoryCommentDaoService chatHistoryCommentDaoService;

    @Resource
    private DifyAppApikeyService difyAppApikeyService;
    @Override
    public List<ChatHistoryCommentResp> list(ChatHistoryCommentQueryDTO query) {
        if (query == null) {
            return Collections.emptyList();
        }

        List<ChatHistoryCommentDO> comments = lambdaQuery().eq(ChatHistoryCommentDO::getUserId, query.getUserId())
            .eq(ChatHistoryCommentDO::getConversationId, query.getConversationId()).list();

        return CopierUtil.copyList(comments, ChatHistoryCommentResp.class);
    }

    @Override
    public Boolean save(ChatHistoryCommentReq record) {

        LLMUrlTokenResp chat = difyAppApikeyService.getByModelId(record.getModelId(), "chat");

        ChatHistoryCommentDO one = lambdaQuery().eq(ChatHistoryCommentDO::getUserId, record.getUserId())
            .eq(ChatHistoryCommentDO::getConversationId, record.getConversationId())
            .eq(ChatHistoryCommentDO::getMessageId, record.getMessageId()).one();

        if (one == null) {
            one = new ChatHistoryCommentDO();
            one.setUserId(record.getUserId());
            one.setConversationId(record.getConversationId());
            one.setMessageId(record.getMessageId());
            one.setLikeFlag(record.getLikeFlag());
            one.setEvaluateTag(String.join(",", record.getEvaluateTags()));
            one.setContent(record.getContent());
        } else {
            one.setEvaluateTag(String.join(",", record.getEvaluateTags()));
            one.setContent(record.getContent());
            one.setLikeFlag(record.getLikeFlag());
        }

        boolean b = saveOrUpdate(one);
        // dify like
        difyLike(chat, record);
        return b;
    }

    private void difyLike(LLMUrlTokenResp chat, ChatHistoryCommentReq req) {
        String url = chat.getUrl() + "messages/" + req.getMessageId() + "/feedbacks";

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", chat.getApiKey());
        header.put("Content-Type", "application/json");
        Map<String, String> params = new HashMap<>();
        params.put("rating", LikeStatusEnum.getByCode(req.getLikeFlag()).getDesc());
        params.put("user", String.valueOf(req.getUserId()));
        params.put("content", req.getContent());
        String body = HttpUtil.createPost(url).header("Authorization", chat.getApiKey())
            .body(JSON.toJSONString(params), ContentType.JSON.getValue()).execute().body();

        log.info("difyLike result {}", body);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(ChatHistoryCommentDO::getId, id);
    }

    @Override
    public ChatHistoryCommentResp detailByMessageId(ChatHistoryCommentQueryDetailDTO query) {
        if (ObjectUtils.isEmpty(query.getMessageId())) {
            return null;
        }

        ChatHistoryCommentDO comments = lambdaQuery().eq(ChatHistoryCommentDO::getUserId, query.getUserId())
            .eq(ChatHistoryCommentDO::getConversationId, query.getConversationId())
            .eq(ChatHistoryCommentDO::getMessageId, query.getMessageId())
            .one();
        if (comments == null) {
            return null;
        }

        return CopierUtil.copy(comments, ChatHistoryCommentResp.class);
    }

}