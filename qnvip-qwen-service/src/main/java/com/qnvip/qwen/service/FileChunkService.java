package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileChunkQueryDTO;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.vo.request.FileChunkReq;
import com.qnvip.qwen.vo.response.FileChunkResp;

/**
 * 文件切块表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
public interface FileChunkService extends BaseService<FileChunkDO> {

    /**
     * 文件切块表分页
     *
     * @param query
     * @return
     */
    Pageable<FileChunkResp> page(FileChunkQueryDTO query);

    /**
     * 保存文件切块表
     *
     * @param record
     * @return
     */
    Boolean save(FileChunkReq record);

    /**
     * 获取文件切块表详情
     *
     * @param id
     * @return
     */
    FileChunkResp detail(Long id);

    /**
     * 删除文件切块表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
