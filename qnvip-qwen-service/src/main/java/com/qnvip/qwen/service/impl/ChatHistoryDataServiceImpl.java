package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.ChatHistoryDataDaoService;
import com.qnvip.qwen.dal.dto.ChatHistoryDataDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryDataQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDataDO;
import com.qnvip.qwen.dal.mapper.ChatHistoryDataMapper;
import com.qnvip.qwen.service.ChatHistoryDataService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.ChatHistoryDataReq;
import com.qnvip.qwen.vo.response.ChatHistoryChatContextResp;
import com.qnvip.qwen.vo.response.ChatHistoryDataResp;

/**
 * 对话记录表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Service
public class ChatHistoryDataServiceImpl extends BaseServiceImpl<ChatHistoryDataMapper, ChatHistoryDataDO>
    implements ChatHistoryDataService {

    @Resource
    private ChatHistoryDataMapper chatHistoryDataMapper;

    @Resource
    private ChatHistoryDataDaoService chatHistoryDataDaoService;

    @Override
    public Pageable<ChatHistoryDataResp> page(ChatHistoryDataQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<ChatHistoryDataDTO> page = chatHistoryDataDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, ChatHistoryDataResp.class)));
    }

    @Override
    public Boolean save(ChatHistoryDataReq record) {
        return save(CopierUtil.copy(record, ChatHistoryDataDO.class));
    }

    @Override
    public ChatHistoryDataResp detail(Long id) {
        ChatHistoryDataDO record = get(ChatHistoryDataDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, ChatHistoryDataResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(ChatHistoryDataDO::getId, id);
    }

    @Override
    public void setData(List<ChatHistoryChatContextResp> resps) {
        if (CollectionUtils.isEmpty(resps)) {
            return;
        }
        List<Long> ids = resps.stream().map(ChatHistoryChatContextResp::getId).collect(Collectors.toList());
        List<ChatHistoryDataDO> chatHistoryDataDOS = listByIds(ids);
        Map<Long, String> idDataMap =
            chatHistoryDataDOS.stream().collect(Collectors.toMap(ChatHistoryDataDO::getId, ChatHistoryDataDO::getData));
        resps.forEach(e -> e.setData(idDataMap.getOrDefault(e.getId(), "")));
    }

}