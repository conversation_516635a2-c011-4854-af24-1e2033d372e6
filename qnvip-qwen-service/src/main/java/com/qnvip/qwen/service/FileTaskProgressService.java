package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;

/**
 * 文件任务进度表服务接口
 *
 * 该接口定义了与文件任务进度相关的服务方法，继承自BaseService。
 *
 * <AUTHOR>
 * @description 由qnvip提供支持
 * @date 2025/03/26 09:06
 */
public interface FileTaskProgressService extends BaseService<FileTaskProgressDO> {

    /**
     * 添加任务，从格式化开始
     * <p>
     * 该方法用于创建一个新的文件任务，并从格式化阶段开始。
     *
     * @param bookId
     * @param fileOriginId 原始文件的唯一标识符
     */
    void saveTaskStartWithFormat(Long bookId, Long fileOriginId);

    /**
     * 添加任务，从格式化开始
     * 
     * 该方法用于创建一个新的文件任务，并从格式化阶段开始。
     * 
     * @param teamId 团队id
     * @param bookId 库id
     * @param docUid 文档唯一标识
     */

    void saveTaskStartWithFormat(Long teamId, Long bookId, String docUid);

    /**
     * 批量 - 添加任务，从格式化开始
     * <p>
     * 该方法用于创建一个新的文件任务，并从格式化阶段开始。
     *
     * @param bookId
     * @param fileOriginId 原始文件的唯一标识符
     */
    void saveTaskStartWithFormat(Long bookId, List<Long> fileOriginId);

    /**
     * 文档删除时调用删除文档对应的任务
     * 
     * @param fileOriginId
     */
    void deleteByFileOriginId(Long fileOriginId);

    /**
     * 删除文档
     *
     * @param fileOriginIds 原始文件的唯一标识符
     */
    void deleteByFileOriginIds(List<Long> fileOriginIds);

    void deleteAllByFileOriginIds(List<Long> fileOriginIds);
}