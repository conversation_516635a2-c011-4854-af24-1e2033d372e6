package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileSummaryDaoService;
import com.qnvip.qwen.dal.dto.FileSummaryDTO;
import com.qnvip.qwen.dal.dto.FileSummaryQueryDTO;
import com.qnvip.qwen.dal.entity.FileSummaryDO;
import com.qnvip.qwen.dal.mapper.FileSummaryMapper;
import com.qnvip.qwen.service.FileSummaryService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileSummaryReq;
import com.qnvip.qwen.vo.response.FileSummaryResp;

/**
 * 文件概括表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Service
public class FileSummaryServiceImpl extends BaseServiceImpl<FileSummaryMapper, FileSummaryDO>
    implements FileSummaryService {

    @Resource
    private FileSummaryMapper fileSummaryMapper;

    @Resource
    private FileSummaryDaoService fileSummaryDaoService;

    @Override
    public Pageable<FileSummaryResp> page(FileSummaryQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileSummaryDTO> page = fileSummaryDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileSummaryResp.class)));
    }

    @Override
    public Boolean save(FileSummaryReq record) {
        return save(CopierUtil.copy(record, FileSummaryDO.class));
    }

    @Override
    public FileSummaryResp detail(Long id) {
        FileSummaryDO record = get(FileSummaryDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileSummaryResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(FileSummaryDO::getId, id);
    }

    @Override
    public void saveDeleteBefore(Long fileOriginId, String summary) {
        fileSummaryMapper.deleteByOriginId(fileOriginId);
        save(FileSummaryDO.builder().originId(fileOriginId).data(summary).build());
    }
}