package com.qnvip.qwen.service;

import java.util.Map;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamQueryDTO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.vo.request.TeamReq;
import com.qnvip.qwen.vo.response.TeamResp;

/**
 * 团队表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
public interface TeamService extends BaseService<TeamDO> {

    /**
     * 团队表分页
     *
     * @param query
     * @return
     */
    Pageable<TeamResp> page(TeamQueryDTO query);

    /**
     * 保存团队表
     *
     * @param record
     * @return
     */
    Boolean save(TeamReq record);

    /**
     * 获取团队表详情
     *
     * @param id
     * @return
     */
    TeamResp detail(Long id);

    /**
     * 删除团队表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 获取团队名字
     * 
     * @return
     */
    Map<Long, String> loadTeamNameMap();
}
