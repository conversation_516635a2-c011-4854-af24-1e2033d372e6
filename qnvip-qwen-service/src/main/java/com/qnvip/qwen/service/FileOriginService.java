package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileOriginQueryDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.util.ExcelJsonToMarkdownTableUtil;
import com.qnvip.qwen.vo.request.FileOriginReq;
import com.qnvip.qwen.vo.response.FileOriginResp;
import com.qnvip.qwen.vo.response.TeamBookDocSummaryResp;

/**
 * 原始文件表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
public interface FileOriginService extends BaseService<FileOriginDO> {

    /**
     * 原始文件表分页
     *
     * @param query
     * @return
     */
    Pageable<FileOriginResp> page(FileOriginQueryDTO query);

    /**
     * 保存原始文件表
     *
     * @param record
     * @return
     */
    Boolean save(FileOriginReq record);

    /**
     * 获取原始文件表详情
     *
     * @param id
     * @return
     */
    FileOriginResp detail(Long id);


    /**
     * 删除原始文件表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    void updateToc(Long fileOriginId, List<String> tocs);

    /**
     * 创建markdown格式的
     * 
     * @param origin
     * @param md
     */
    void createMarkdownTableSheet(FileOriginDO origin, List<ExcelJsonToMarkdownTableUtil.SheetData> md);

    /**
     * 获取库id
     * 
     * @param needFileOriginIds
     * @return
     */
    List<Long> listBookIdsByIds(List<Long> needFileOriginIds);

    /**
     * urlMatchDoc
     * 
     * @param content
     * @return
     */
    List<TeamBookDocSummaryResp> urlMatchDoc(String content);
}
