package com.qnvip.qwen.service.example;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.qnvip.qwen.dal.dto.SimpleTeamBookDTO;
import com.qnvip.qwen.dal.dto.TeamBookQueryDTO;
import com.qnvip.qwen.service.TeamBookService;
import com.qnvip.qwen.vo.response.TeamResp;

import lombok.extern.slf4j.Slf4j;

/**
 * TeamBookService 使用示例
 * 
 * <AUTHOR>
 * @since 2025-06-26
 */
@Slf4j
@Component
public class TeamBookServiceExample {

    @Resource
    private TeamBookService teamBookService;

    /**
     * 示例1：获取所有团队及其知识库
     */
    public void getAllTeamsWithBooks() {
        log.info("=== 获取所有团队及其知识库 ===");

        TeamBookQueryDTO filter = new TeamBookQueryDTO();
        List<TeamResp> teams = teamBookService.list(filter);

        for (TeamResp team : teams) {
            log.info("团队: {} (ID: {})", team.getName(), team.getId());

            List<SimpleTeamBookDTO> children = team.getChildren();
            if (children != null && !children.isEmpty()) {
                log.info("  该团队有 {} 个知识库:", children.size());
                for (SimpleTeamBookDTO book : children) {
                    log.info("    - {} (ID: {})", book.getName(), book.getId());
                }
            } else {
                log.info("  该团队暂无知识库");
            }
        }

        log.info("=== 总共 {} 个团队 ===", teams.size());
    }

    /**
     * 示例2：根据知识库名称过滤
     */
    public void getTeamsWithFilteredBooks(String bookName) {
        log.info("=== 根据知识库名称过滤: {} ===", bookName);

        TeamBookQueryDTO filter = new TeamBookQueryDTO();
        filter.setName(bookName);

        List<TeamResp> teams = teamBookService.list(filter);

        for (TeamResp team : teams) {
            List<SimpleTeamBookDTO> children = team.getChildren();
            if (children != null && !children.isEmpty()) {
                log.info("团队: {} 包含匹配的知识库:", team.getName());
                for (SimpleTeamBookDTO book : children) {
                    log.info("  - {}", book.getName());
                }
            }
        }
    }

    /**
     * 示例3：统计团队知识库数量
     */
    public void getTeamBookStatistics() {
        log.info("=== 团队知识库统计 ===");

        TeamBookQueryDTO filter = new TeamBookQueryDTO();
        List<TeamResp> teams = teamBookService.list(filter);

        int totalTeams = teams.size();
        int teamsWithBooks = 0;
        int totalBooks = 0;

        for (TeamResp team : teams) {
            List<SimpleTeamBookDTO> children = team.getChildren();
            int bookCount = children != null ? children.size() : 0;

            if (bookCount > 0) {
                teamsWithBooks++;
                totalBooks += bookCount;
                log.info("团队 '{}' 有 {} 个知识库", team.getName(), bookCount);
            } else {
                log.info("团队 '{}' 暂无知识库", team.getName());
            }
        }

        log.info("统计结果:");
        log.info("  总团队数: {}", totalTeams);
        log.info("  有知识库的团队数: {}", teamsWithBooks);
        log.info("  总知识库数: {}", totalBooks);
        log.info("  平均每个团队知识库数: {}", totalTeams > 0 ? (double)totalBooks / totalTeams : 0);
    }

    /**
     * 示例4：查找特定团队的知识库
     */
    public void findBooksByTeamName(String teamName) {
        log.info("=== 查找团队 '{}' 的知识库 ===", teamName);

        TeamBookQueryDTO filter = new TeamBookQueryDTO();
        List<TeamResp> teams = teamBookService.list(filter);

        for (TeamResp team : teams) {
            if (teamName.equals(team.getName())) {
                List<SimpleTeamBookDTO> children = team.getChildren();
                if (children != null && !children.isEmpty()) {
                    log.info("找到团队 '{}' 的 {} 个知识库:", teamName, children.size());
                    for (SimpleTeamBookDTO book : children) {
                        log.info("  - {} (ID: {})", book.getName(), book.getId());
                    }
                } else {
                    log.info("团队 '{}' 暂无知识库", teamName);
                }
                return;
            }
        }

        log.warn("未找到名为 '{}' 的团队", teamName);
    }

    /**
     * 示例5：检查数据完整性
     */
    public void checkDataIntegrity() {
        log.info("=== 检查数据完整性 ===");

        TeamBookQueryDTO filter = new TeamBookQueryDTO();
        List<TeamResp> teams = teamBookService.list(filter);

        boolean hasIssues = false;

        for (TeamResp team : teams) {
            // 检查团队基本信息
            if (team.getId() == null) {
                log.warn("发现团队ID为空: {}", team.getName());
                hasIssues = true;
            }

            if (team.getName() == null || team.getName().trim().isEmpty()) {
                log.warn("发现团队名称为空: ID={}", team.getId());
                hasIssues = true;
            }

            // 检查知识库信息
            List<SimpleTeamBookDTO> children = team.getChildren();
            if (children != null) {
                for (SimpleTeamBookDTO book : children) {
                    if (book.getId() == null) {
                        log.warn("团队 '{}' 中发现知识库ID为空: {}", team.getName(), book.getName());
                        hasIssues = true;
                    }

                    if (book.getTeamId() == null) {
                        log.warn("知识库 '{}' 的teamId为空", book.getName());
                        hasIssues = true;
                    } else if (!book.getTeamId().equals(team.getId())) {
                        log.warn("知识库 '{}' 的teamId({}) 与所属团队ID({}) 不匹配", book.getName(), book.getTeamId(),
                            team.getId());
                        hasIssues = true;
                    }

                    if (book.getName() == null || book.getName().trim().isEmpty()) {
                        log.warn("团队 '{}' 中发现知识库名称为空: ID={}", team.getName(), book.getId());
                        hasIssues = true;
                    }
                }
            }
        }

        if (!hasIssues) {
            log.info("数据完整性检查通过，未发现问题");
        } else {
            log.warn("数据完整性检查发现问题，请检查上述警告信息");
        }
    }
}
