package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileQaQueryDTO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.vo.request.FileQaReq;
import com.qnvip.qwen.vo.response.FileQaResp;

/**
 * 文档QA表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
public interface FileQaService extends BaseService<FileQaDO> {

    /**
     * 文档QA表分页
     *
     * @param query
     * @return
     */
    Pageable<FileQaResp> page(FileQaQueryDTO query);

    /**
     * 保存文档QA表
     *
     * @param record
     * @return
     */
    Boolean save(FileQaReq record);

    /**
     * 获取文档QA表详情
     *
     * @param id
     * @return
     */
    FileQaResp detail(Long id);

    /**
     * 删除文档QA表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
