package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dto.FileChunkDataDTO;
import com.qnvip.qwen.dal.dto.FileChunkDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.mapper.FileChunkDataMapper;
import com.qnvip.qwen.service.FileChunkDataService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileChunkDataReq;
import com.qnvip.qwen.vo.response.FileChunkDataResp;


/**
 * 文件切块数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@Service
public class FileChunkDataServiceImpl extends BaseServiceImpl<FileChunkDataMapper, FileChunkDataDO>
    implements FileChunkDataService {

    @Resource
    private FileChunkDataMapper fileChunkDataMapper;

    @Resource
    private FileChunkDataDaoService fileChunkDataDaoService;

    @Override
    public Pageable<FileChunkDataResp> page(FileChunkDataQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileChunkDataDTO> page = fileChunkDataDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileChunkDataResp.class)));
    }

    @Override
    public Boolean save(FileChunkDataReq record) {
        return save(CopierUtil.copy(record, FileChunkDataDO.class));
    }

    @Override
    public FileChunkDataResp detail(Long id) {
        FileChunkDataDO record = get(FileChunkDataDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileChunkDataResp.class);
    }


    @Override
    public Boolean delete(Long id) {
        return remove(FileChunkDataDO::getId, id);
    }
}