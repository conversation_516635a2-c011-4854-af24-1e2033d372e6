package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileRecallLogDaoService;
import com.qnvip.qwen.dal.dto.FileChunkRerankDTO;
import com.qnvip.qwen.dal.dto.FileOriginScoreDTO;
import com.qnvip.qwen.dal.dto.FileRecallLogDTO;
import com.qnvip.qwen.dal.dto.FileRecallLogQueryDTO;
import com.qnvip.qwen.dal.dto.HybirdRecallDTO;
import com.qnvip.qwen.dal.dto.IdScoreFileDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.entity.FileRecallLogDO;
import com.qnvip.qwen.dal.mapper.FileRecallLogMapper;
import com.qnvip.qwen.enums.RecallFromEnum;
import com.qnvip.qwen.enums.RecallStageEnum;
import com.qnvip.qwen.service.FileRecallLogService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.DifyUserQuestionReq;
import com.qnvip.qwen.vo.request.DifyUserRecallReq;
import com.qnvip.qwen.vo.request.FileRecallLogReq;
import com.qnvip.qwen.vo.response.FileRecallLogResp;

/**
 * 文件召回日志表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Service
public class FileRecallLogServiceImpl extends BaseServiceImpl<FileRecallLogMapper, FileRecallLogDO>
    implements FileRecallLogService {

    @Resource
    private FileRecallLogMapper fileRecallLogMapper;

    @Resource
    private FileRecallLogDaoService fileRecallLogDaoService;

    @Override
    public Pageable<FileRecallLogResp> page(FileRecallLogQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileRecallLogDTO> page = fileRecallLogDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileRecallLogResp.class)));
    }

    @Override
    public Boolean save(FileRecallLogReq record) {
        return save(CopierUtil.copy(record, FileRecallLogDO.class));
    }

    @Override
    public FileRecallLogResp detail(Long id) {
        FileRecallLogDO record = get(FileRecallLogDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileRecallLogResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(FileRecallLogDO::getId, id);
    }

    @Override
    public void saveNavigationEsRecall(DifyUserQuestionReq req, Long questionId, List<Long> esFileOriginIds,
        RecallFromEnum recallFrom) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallFrom(recallFrom.getValue());
        record.setRecallStage(RecallStageEnum.NAVIGATION.getCode());
        record.setFileOriginId(esFileOriginIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveNavigationMilvusRecall(DifyUserRecallReq req, Long questionId, List<Long> milvusFileOriginIds,
        RecallFromEnum navigationMilvus) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallFrom(navigationMilvus.getValue());
        record.setRecallStage(RecallStageEnum.NAVIGATION.getCode());
        record.setFileOriginId(milvusFileOriginIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveMultChunkEsRecall(DifyUserRecallReq req, Long questionId, List<IdScoreFileDTO<String>> chunks,
        RecallFromEnum multEs) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallFrom(multEs.getValue());
        record.setRecallStage(RecallStageEnum.ES.getCode());
        record.setFileOriginId(chunks.stream().map(IdScoreFileDTO::getFileOriginId).map(String::valueOf).distinct()
            .collect(Collectors.joining(",")));
        record.setFileChunkUid(chunks.stream().map(IdScoreFileDTO::getId).map(String::valueOf).distinct()
            .collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveMultRecallChunkMilvusRecall(DifyUserRecallReq req, Long questionId, List<FileQaDO> chunks,
        RecallFromEnum multEs) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallFrom(multEs.getValue());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallStage(RecallStageEnum.MILVUS.getCode());
        record.setFileOriginId(chunks.stream().map(FileQaDO::getFileOriginId).distinct().map(String::valueOf)
            .collect(Collectors.joining(",")));
        record.setFileChunkUid(
            chunks.stream().map(FileQaDO::getFileChunkUid).distinct().collect(Collectors.joining(",")));
        record.setFileQaUid(chunks.stream().map(FileQaDO::getUid).distinct().collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveMultRecallMilvusRecall(DifyUserRecallReq req, Long questionId, List<FileChunkDataDO> chunks,
        RecallFromEnum multEs) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallFrom(multEs.getValue());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallStage(RecallStageEnum.MILVUS.getCode());
        record.setFileOriginId(chunks.stream().map(FileChunkDataDO::getFileOriginId).distinct().map(String::valueOf)
            .collect(Collectors.joining(",")));
        record.setFileChunkUid(
            chunks.stream().map(FileChunkDataDO::getChunkUid).distinct().collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveChunkMergeRecall(DifyUserRecallReq req, Long questionId, List<FileOriginScoreDTO> originChunksDTOS,
        RecallFromEnum multSurround) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallFrom(multSurround.getValue());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallStage(RecallStageEnum.CHUNK_MERGE.getCode());
        record.setFileOriginId(originChunksDTOS.stream().map(FileOriginScoreDTO::getFileOriginId).map(String::valueOf)
            .distinct().collect(Collectors.joining(",")));
        record.setFileChunkUid(originChunksDTOS.stream().map(FileOriginScoreDTO::getChunks).flatMap(List::stream)
            .map(FileChunkRerankDTO::getChunkUid).distinct().collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveRerankChunkRecall(DifyUserRecallReq req, Long questionId, List<FileChunkRerankDTO> chunkRerankDTOS,
        RecallFromEnum recallFrom) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallFrom(recallFrom.getValue());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallStage(RecallStageEnum.RE_RANKING.getCode());
        record.setFileOriginId(chunkRerankDTOS.stream().map(FileChunkRerankDTO::getFileOriginId).distinct()
            .map(String::valueOf).collect(Collectors.joining(",")));
        record.setFileChunkUid(
            chunkRerankDTOS.stream().map(FileChunkRerankDTO::getChunkUid).distinct().collect(Collectors.joining(",")));
        save(record);
    }

    @Override
    public void saveMultChunkGraphRecall(DifyUserRecallReq userId, Long questionId, Collection<Long> fileIds,
        List<String> chunkUids, RecallFromEnum graph) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(userId.getUserId());
        record.setRecallType(userId.getRecallType());
        record.setQuestionId(questionId);
        record.setRecallFrom(graph.getValue());
        record.setRecallStage(RecallStageEnum.GRAPH.getCode());
        record.setFileOriginId(fileIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        record.setFileChunkUid(String.join(",", chunkUids));
        save(record);
    }

    @Override
    public void saveStatic(DifyUserRecallReq req, HybirdRecallDTO hybirdRecallDTO, RecallFromEnum recallFromEnum) {
        FileRecallLogDO record = new FileRecallLogDO();
        record.setUserId(req.getUserId());
        record.setRecallType(req.getRecallType());
        record.setQuestionId(req.getQuestionId());
        record.setRecallFrom(recallFromEnum.getValue());
        record.setRecallStage(RecallStageEnum.STATIC.getCode());
        record.setFileOriginId("");
        record.setFileChunkUid(hybirdRecallDTO.getCalculateHitRate());
        save(record);
    }
}