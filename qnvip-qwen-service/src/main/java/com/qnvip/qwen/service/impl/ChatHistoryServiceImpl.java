package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.ChatHistoryDaoService;
import com.qnvip.qwen.dal.dto.ChatHistoryDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDO;
import com.qnvip.qwen.dal.entity.ChatHistoryDataDO;
import com.qnvip.qwen.dal.mapper.ChatHistoryMapper;
import com.qnvip.qwen.service.ChatHistoryDataService;
import com.qnvip.qwen.service.ChatHistoryService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.ChatHistoryLoadChatHistoryListReq;
import com.qnvip.qwen.vo.request.ChatHistoryLoadChatHistoryReq;
import com.qnvip.qwen.vo.request.ChatHistoryReq;
import com.qnvip.qwen.vo.request.DifyMutilRecallReq;
import com.qnvip.qwen.vo.response.ChatHistoryChatContextResp;
import com.qnvip.qwen.vo.response.ChatHistoryResp;

/**
 * 对话记录表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@Service
public class ChatHistoryServiceImpl extends BaseServiceImpl<ChatHistoryMapper, ChatHistoryDO>
    implements ChatHistoryService {

    @Resource
    private ChatHistoryMapper chatHistoryMapper;

    @Resource
    private ChatHistoryDaoService chatHistoryDaoService;

    @Resource
    private ChatHistoryDataService chatHistoryDataService;

    public static List<String> processList(ChatHistoryLoadChatHistoryListReq req,
        List<ChatHistoryChatContextResp> resp) {

        List<String> result = new ArrayList<>();

        // 添加原始列表中的前n个元素
        for (int i = 0; i < Math.min(resp.size(), req.getLimit()); i++) {
            result.add(resp.get(i).getData());
        }

        // 如果不足n个，填充空字符串
        while (result.size() < req.getLimit()) {
            result.add(0, "");
        }

        return result;
    }

    @Override
    public Pageable<ChatHistoryResp> page(ChatHistoryQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<ChatHistoryDTO> page = chatHistoryDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, ChatHistoryResp.class)));
    }

    @Override
    public Boolean save(ChatHistoryReq record) {
        return save(CopierUtil.copy(record, ChatHistoryDO.class));
    }

    @Override
    public ChatHistoryResp detail(Long id) {
        ChatHistoryDO record = get(ChatHistoryDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, ChatHistoryResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(ChatHistoryDO::getId, id);
    }

    @Override
    public Long saveChat(ChatHistoryReq chatHistoryReq) {
        ChatHistoryDO one = lambdaQuery().eq(ChatHistoryDO::getUserId, chatHistoryReq.getUserId())
            .eq(ChatHistoryDO::getFromUser, chatHistoryReq.getFromUser())
            .eq(ChatHistoryDO::getConversationId, chatHistoryReq.getConversationId())
            .eq(ChatHistoryDO::getAgentId, chatHistoryReq.getAgentId())
            .eq(ChatHistoryDO::getWorkflowRunId, chatHistoryReq.getWorkflowRunId()).orderByDesc(ChatHistoryDO::getId)
            .last(" limit 1 ").one();

        if (one != null) {
            return one.getId();
        }

        ChatHistoryDO historyDO = new ChatHistoryDO();
        historyDO.setUserId(chatHistoryReq.getUserId());
        historyDO.setFromUser(chatHistoryReq.getFromUser());
        historyDO.setConversationId(chatHistoryReq.getConversationId());
        historyDO.setAgentId(chatHistoryReq.getAgentId());
        historyDO.setWorkflowRunId(chatHistoryReq.getWorkflowRunId());
        historyDO.setUseBook(chatHistoryReq.getUseBook());
        historyDO.setUseSearch(chatHistoryReq.getUseSearch());
        save(historyDO);

        ChatHistoryDataDO historyData = new ChatHistoryDataDO();
        historyData.setId(historyDO.getId());
        historyData.setData(chatHistoryReq.getContent());
        chatHistoryDataService.save(historyData);
        return historyDO.getId();
    }

    @Override
    public String loadChatHistory(ChatHistoryLoadChatHistoryReq record) {

        List<ChatHistoryDO> chatHistorys =
            chatHistoryDaoService.loadLatestLimit(CopierUtil.copy(record, ChatHistoryQueryDTO.class));

        List<ChatHistoryChatContextResp> chatHistoryChatContextResps =
            CopierUtil.copyList(chatHistorys, ChatHistoryChatContextResp.class);

        chatHistoryDataService.setData(chatHistoryChatContextResps);

        return toChatContext(chatHistoryChatContextResps, record.isWithTime());
    }

    private String toChatContext(List<ChatHistoryChatContextResp> chatHistorys, boolean withTime) {
        return chatHistorys.stream().map(e -> e.toChatContext(withTime)).collect(Collectors.joining("\n"));
    }

    @Override
    public List<String> loadChatHistory(ChatHistoryLoadChatHistoryListReq req) {

        List<ChatHistoryDO> chatHistorys =
            chatHistoryDaoService.loadLatestLimit(CopierUtil.copy(req, ChatHistoryQueryDTO.class));

        List<ChatHistoryChatContextResp> resp = CopierUtil.copyList(chatHistorys, ChatHistoryChatContextResp.class);

        chatHistoryDataService.setData(resp);

        return processList(req, resp);
    }

    @Override
    public Long loadBeforeQuestionIdByWorkflowRunId(DifyMutilRecallReq record) {
        ChatHistoryDO one = lambdaQuery().select(ChatHistoryDO::getId).eq(ChatHistoryDO::getUserId, record.getUserId())
            .eq(ChatHistoryDO::getConversationId, record.getConversationId())
            .eq(ChatHistoryDO::getWorkflowRunId, record.getWorkflowRunId())
            .eq(ChatHistoryDO::getAgentId, record.getAgentId()).last(" limit 1").one();
        if (one == null) {
            return null;
        }
        return one.getId();
    }

}