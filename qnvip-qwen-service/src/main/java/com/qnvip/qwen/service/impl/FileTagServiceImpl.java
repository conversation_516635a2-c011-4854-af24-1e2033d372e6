package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileTagDaoService;
import com.qnvip.qwen.dal.dto.FileTagDTO;
import com.qnvip.qwen.dal.dto.FileTagQueryDTO;
import com.qnvip.qwen.dal.entity.FileTagDO;
import com.qnvip.qwen.dal.mapper.FileTagMapper;
import com.qnvip.qwen.service.FileTagService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileTagReq;
import com.qnvip.qwen.vo.response.FileTagResp;

/**
 * 文件标签表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Service
public class FileTagServiceImpl extends BaseServiceImpl<FileTagMapper, FileTagDO> implements FileTagService {

    @Resource
    private FileTagMapper fileTagMapper;

    @Resource
    private FileTagDaoService fileTagDaoService;

    @Override
    public Pageable<FileTagResp> page(FileTagQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileTagDTO> page = fileTagDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileTagResp.class)));
    }

    @Override
    public Boolean save(FileTagReq record) {
        return save(CopierUtil.copy(record, FileTagDO.class));
    }

    @Override
    public FileTagResp detail(Long id) {
        FileTagDO record = get(FileTagDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileTagResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(FileTagDO::getId, id);
    }

    @Override
    public void saveDeleteBefore(Long fileOriginId, List<String> tags) {
        fileTagMapper.deleteByOriginId(fileOriginId);
        List<FileTagDO> dos = tags.stream().map(tag -> FileTagDO.builder().originId(fileOriginId).name(tag).build())
            .collect(Collectors.toList());
        saveBatch(dos);
    }

}