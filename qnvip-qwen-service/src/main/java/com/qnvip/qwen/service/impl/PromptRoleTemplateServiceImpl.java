package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.PromptRoleTemplateDaoService;
import com.qnvip.qwen.dal.dto.PromptRoleTemplateQueryDTO;
import com.qnvip.qwen.dal.entity.PromptRoleCategoryDO;
import com.qnvip.qwen.dal.entity.PromptRoleTemplateDO;
import com.qnvip.qwen.dal.mapper.PromptRoleTemplateMapper;
import com.qnvip.qwen.service.PromptRoleCategoryService;
import com.qnvip.qwen.service.PromptRoleTemplateService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.vo.request.PromptRoleTemplateReq;
import com.qnvip.qwen.vo.response.PromptRoleTemplateResp;
import com.qnvip.qwen.vo.response.TeamBookDocSummaryResp;

/**
 * 角色提示词模板表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@Service
public class PromptRoleTemplateServiceImpl extends BaseServiceImpl<PromptRoleTemplateMapper, PromptRoleTemplateDO>
    implements PromptRoleTemplateService {

    @Resource
    private PromptRoleTemplateMapper promptRoleTemplateMapper;

    @Resource
    private PromptRoleTemplateDaoService promptRoleTemplateDaoService;

    @Resource
    private PromptRoleCategoryService promptRoleCategoryService;

    public void putCategoryName(List<PromptRoleTemplateResp> items) {
        List<PromptRoleCategoryDO> list = promptRoleCategoryService.list();
        Map<Long, String> idNameMap =
            list.stream().collect(Collectors.toMap(PromptRoleCategoryDO::getId, PromptRoleCategoryDO::getName));
        items.forEach(item -> item.setCategoryName(idNameMap.getOrDefault(item.getCategoryId(), "")));

    }

    @Override
    public Pageable<PromptRoleTemplateResp> page(PromptRoleTemplateQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<PromptRoleTemplateDO> page = lambdaQuery().setEntity(CopierUtil.copy(query, PromptRoleTemplateDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        Pageable<PromptRoleTemplateResp> promptRoleTemplateRespPageable =
            MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, PromptRoleTemplateResp.class)));
        putCategoryName(promptRoleTemplateRespPageable.getRecords());
        return promptRoleTemplateRespPageable;
    }

    @Override
    public List<PromptRoleTemplateResp> list(PromptRoleTemplateReq query) {
        PromptRoleTemplateDO filter = CopierUtil.copy(query, PromptRoleTemplateDO.class);
        List<PromptRoleTemplateDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        List<PromptRoleTemplateResp> promptRoleTemplateResps = CopierUtil.copyList(list, PromptRoleTemplateResp.class);
        putCategoryName(promptRoleTemplateResps);
        return promptRoleTemplateResps;
    }

    @Override
    public Boolean save(PromptRoleTemplateReq record) {
        return saveOrUpdate(CopierUtil.copy(record, PromptRoleTemplateDO.class));
    }

    @Override
    public PromptRoleTemplateResp detail(Long id) {
        PromptRoleTemplateDO record = get(PromptRoleTemplateDO::getId, id);
        if (record == null) {
            return null;
        }

        PromptRoleTemplateResp copy = CopierUtil.copy(record, PromptRoleTemplateResp.class);
        putCategoryName(Lists.newArrayList(copy));
        return copy;
    }

    @Override
    public Boolean delete(Long id) {
        return remove(PromptRoleTemplateDO::getId, id);
    }

    @Override
    public String contentReplace(String content, List<TeamBookDocSummaryResp> resp) {
        for (TeamBookDocSummaryResp item : resp) {
            content = content.replace(item.getReplaceKey(), item.getReplaceValue());
        }

        return content;
    }
}