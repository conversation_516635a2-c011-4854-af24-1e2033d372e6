package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dto.TeamBookDocDTO;
import com.qnvip.qwen.dal.dto.TeamBookDocQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.dal.mapper.TeamBookDocMapper;
import com.qnvip.qwen.service.TeamBookDocService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.TeamBookDocReq;
import com.qnvip.qwen.vo.response.TeamBookDocResp;

import cn.hutool.core.collection.CollUtil;

/**
 * 文档表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Service
public class TeamBookDocServiceImpl extends BaseServiceImpl<TeamBookDocMapper, TeamBookDocDO>
    implements TeamBookDocService {

    @Resource
    private TeamBookDocMapper teamBookDocMapper;

    @Resource
    private TeamBookDocDaoService teamBookDocDaoService;

    @Override
    public Pageable<TeamBookDocResp> page(TeamBookDocQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<TeamBookDocDTO> page = teamBookDocDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, TeamBookDocResp.class)));
    }

    @Override
    public Boolean save(TeamBookDocReq record) {
        return save(CopierUtil.copy(record, TeamBookDocDO.class));
    }

    @Override
    public TeamBookDocResp detail(Long id) {
        TeamBookDocDO record = get(TeamBookDocDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, TeamBookDocResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(TeamBookDocDO::getId, id);
    }

    @Override
    public List<TeamBookDocDO> getListByBookId(List<Long> bookIds) {
        if (CollUtil.isEmpty(bookIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDO::getBookId, bookIds);
        return list(queryWrapper);
    }

    @Override
    public List<String> loadTocNames(Long bookId, String docUid) {
        List<TeamBookDocDO> list = teamBookDocMapper.loadFileTocsByCte(bookId, docUid);
        Collections.reverse(list);
        return list.stream().map(TeamBookDocDO::getName).collect(Collectors.toList());
    }
}