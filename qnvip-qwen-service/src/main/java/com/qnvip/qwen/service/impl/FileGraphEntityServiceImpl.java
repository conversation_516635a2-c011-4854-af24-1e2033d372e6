package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileGraphEntityDaoService;
import com.qnvip.qwen.dal.dto.FileGraphEntityDTO;
import com.qnvip.qwen.dal.dto.FileGraphEntityQueryDTO;
import com.qnvip.qwen.dal.entity.FileGraphEntityDO;
import com.qnvip.qwen.dal.mapper.FileGraphEntityMapper;
import com.qnvip.qwen.service.FileGraphEntityService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileGraphEntityReq;
import com.qnvip.qwen.vo.response.FileGraphEntityResp;

/**
 * 文件图数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:44
 */
@Service
public class FileGraphEntityServiceImpl extends BaseServiceImpl<FileGraphEntityMapper, FileGraphEntityDO>
    implements FileGraphEntityService {

    @Resource
    private FileGraphEntityMapper fileGraphEntityMapper;

    @Resource
    private FileGraphEntityDaoService fileGraphEntityDaoService;

    @Override
    public Pageable<FileGraphEntityResp> page(FileGraphEntityQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileGraphEntityDTO> page = lambdaQuery().setEntity(CopierUtil.copy(query, FileGraphEntityDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileGraphEntityResp.class)));
    }

    @Override
    public List<FileGraphEntityResp> list(FileGraphEntityReq query) {
        FileGraphEntityDO filter = CopierUtil.copy(query, FileGraphEntityDO.class);
        List<FileGraphEntityDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        return CopierUtil.copyList(list, FileGraphEntityResp.class);
    }

    @Override
    public Boolean save(FileGraphEntityReq record) {
        return saveOrUpdate(CopierUtil.copy(record, FileGraphEntityDO.class));
    }

    @Override
    public FileGraphEntityResp detail(Long id) {
        FileGraphEntityDO record = get(FileGraphEntityDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileGraphEntityResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(FileGraphEntityDO::getId, id);
    }

    @Override
    public List<FileGraphEntityDO> pageLoadEntity(List<Long> fileOriginIds) {
        if (ObjectUtils.isEmpty(fileOriginIds)) {
            return new ArrayList<>();
        }

        return lambdaQuery().in(!ObjectUtils.isEmpty(fileOriginIds), FileGraphEntityDO::getFileOriginId, fileOriginIds)
            .list();
    }

    @Override
    public List<FileGraphEntityDO> groupListByName(Set<String> sourceEntities) {
        if (sourceEntities == null || sourceEntities.isEmpty()) {
            return new ArrayList<>();
        }

        return lambdaQuery().in(FileGraphEntityDO::getName, sourceEntities).groupBy(FileGraphEntityDO::getName).list();
    }

}