package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.LlmSupportModelQueryDTO;
import com.qnvip.qwen.dal.entity.LlmSupportModelDO;
import com.qnvip.qwen.vo.request.LlmSupportModelReq;
import com.qnvip.qwen.vo.response.LlmSupportModelResp;

/**
 * 支持的模型表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
public interface LlmSupportModelService extends BaseService<LlmSupportModelDO> {

    /**
     * 支持的模型表分页
     *
     * @param query
     * @return
     */
    Pageable<LlmSupportModelResp> page(LlmSupportModelQueryDTO query);

    /**
     * 支持的模型表列表
     *
     * @param query
     * @return
     */
    List<LlmSupportModelResp> list(LlmSupportModelReq query);

    /**
     * 保存支持的模型表
     *
     * @param record
     * @return
     */
    Boolean save(LlmSupportModelReq record);

    /**
     * 获取支持的模型表详情
     *
     * @param id
     * @return
     */
    LlmSupportModelResp detail(Long id);

    /**
     * 删除支持的模型表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
