package com.qnvip.qwen.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.FileQaDaoService;
import com.qnvip.qwen.dal.dao.FileQaDataDaoService;
import com.qnvip.qwen.dal.dao.FileSummaryDaoService;
import com.qnvip.qwen.dal.dao.FileTagDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;
import com.qnvip.qwen.dal.es.EsChunkService;
import com.qnvip.qwen.dal.mapper.FileTaskProgressMapper;
import com.qnvip.qwen.dal.milvus.MilvusChunkDocService;
import com.qnvip.qwen.dal.milvus.MilvusQaLinkService;
import com.qnvip.qwen.enums.TaskStatusEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileTaskProgressDatalogService;
import com.qnvip.qwen.service.FileTaskProgressService;
import com.qnvip.qwen.util.Lists;

import cn.hutool.core.collection.CollUtil;

/**
 * 文件任务进度服务实现类
 *
 * <AUTHOR>
 * @description 提供文件任务进度的相关服务
 * @date 2025/03/26 09:06
 */
@Service
public class FileTaskProgressServiceImpl extends BaseServiceImpl<FileTaskProgressMapper, FileTaskProgressDO>
    implements FileTaskProgressService {
    // 最大重试次数
    public static final int MAX_RETRIES = 3;
    @Resource
    private FileTaskProgressMapper fileTaskProgressMapper;

    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private EsChunkService esChunkService;

    @Resource
    private MilvusChunkDocService milvusChunkDocService;
    @Resource
    private MilvusQaLinkService milvusQaLinkService;
    @Resource
    private FileSummaryDaoService fileSummaryDaoService;
    @Resource
    private FileTagDaoService fileTagDaoService;
    @Resource
    private FileChunkDaoService fileChunkDaoService;
    @Resource
    private FileChunkDataDaoService fileChunkDataDaoService;
    @Resource
    private FileQaDaoService fileQaDaoService;
    @Resource
    private FileQaDataDaoService fileQaDataDaoService;
    @Resource
    private FileTaskProgressDatalogService fileTaskProgressDatalogService;
    @Resource
    private TeamBookDocDaoService teamBookDocDaoService;
    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;

    @Override
    public void saveTaskStartWithFormat(Long teamId, Long bookId, String docUid) {
        FileOriginDO byFileOrigin = fileOriginDaoService.getByFileOrigin(teamId, bookId, docUid);
        saveTask(bookId, Lists.newArrayList(byFileOrigin.getId()), TaskStepsEnum.TASK_STEP_FORMAT);

    }

    /**
     * 保存以格式化步骤开始的任务
     *
     * @param bookId
     * @param fileOriginId 文件原始ID
     */
    @Override
    public void saveTaskStartWithFormat(Long bookId, Long fileOriginId) {
        saveTask(bookId, Lists.newArrayList(fileOriginId), TaskStepsEnum.TASK_STEP_FORMAT);
    }

    /**
     * 批量保存以格式化步骤开始的任务
     *
     * @param bookId
     * @param fileOriginId
     */
    @Override
    public void saveTaskStartWithFormat(Long bookId, List<Long> fileOriginId) {
        saveTask(bookId, Lists.newArrayList(fileOriginId), TaskStepsEnum.TASK_STEP_FORMAT);
    }

    /**
     * 保存任务
     *
     * @param bookId
     * @param fileOriginIds 文件原始ID
     * @param startStep 任务开始步骤
     */
    private void saveTask(Long bookId, List<Long> fileOriginIds, TaskStepsEnum startStep) {
        // 删除未开始的任务
        deletePendingTask(bookId, fileOriginIds, startStep);
        // 插入任务
        List<FileTaskProgressDO> collect = fileOriginIds.stream().map(fileOriginId -> {
            FileTaskProgressDO record = new FileTaskProgressDO();
            record.setFileOriginId(fileOriginId);
            record.setNowStep(startStep.getCode());
            record.setStatus(TaskStatusEnum.PENDING.getCode());
            record.setMaxRetryTimes(MAX_RETRIES);
            record.setRetryTimes(0);
            record.setBookId(bookId);
            return record;
        }).collect(Collectors.toList());
        saveBatch(collect);
    }

    private void deletePendingTask(Long bookId, List<Long> fileOriginIds, TaskStepsEnum startStep) {
        if (bookId == null || CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileTaskProgressMapper.deletePendingTask(bookId, startStep.getCode(), StringUtils.join(fileOriginIds, ","));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        lambdaUpdate().eq(FileTaskProgressDO::getFileOriginId, fileOriginId).remove();
        fileTaskProgressDatalogService.deleteByFileOriginId(fileOriginId);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }

        // 删除全部任务
        fileTaskProgressMapper.deleteTaskByFileOriginIds(StringUtils.join(fileOriginIds, ","));
        // 删除文章概括
        fileSummaryDaoService.deleteByFileOriginIds(fileOriginIds);
        // 删除文章标签
        fileTagDaoService.deleteByFileOriginIds(fileOriginIds);
        // 删除db chunk
        fileChunkDaoService.deleteByFileOriginIds(fileOriginIds);
        // 删除db chunkdata
        fileChunkDataDaoService.deleteByFileOriginIds(fileOriginIds);
        // 删除db qa
        fileQaDaoService.deleteByFileOriginIds(fileOriginIds);
        // 删除db qadata
        fileQaDataDaoService.deleteByFileOriginIds(fileOriginIds);
        // 删除es 切块数据
        esChunkService.batchDeleteByFileOriginIds(fileOriginIds);
        // 删除milvus chunk数据
        milvusChunkDocService.deleteByFileOriginIds(fileOriginIds);
        // 删除milvus qa数据
        milvusQaLinkService.deleteByFileOriginIds(fileOriginIds);
    }

    @Override
    public void deleteAllByFileOriginIds(List<Long> fileOriginIds) {
        if (ObjectUtils.isEmpty(fileOriginIds)) {
            return;
        }
        List<String> docUids = fileOriginDaoService.findUidByFileOriginIds(fileOriginIds);
        if (ObjectUtils.isEmpty(docUids)) {
            return;
        }
        // 删除doc
        teamBookDocDaoService.deleteByDocUIds(docUids);
        // 删除doc data
        teamBookDocDataDaoService.deleteByDocUIds(docUids);
        deleteByFileOriginIds(fileOriginIds);

    }

}