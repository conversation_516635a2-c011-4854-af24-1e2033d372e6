package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDetailDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryCommentDO;
import com.qnvip.qwen.vo.request.ChatHistoryCommentReq;
import com.qnvip.qwen.vo.response.ChatHistoryCommentResp;

/**
 * 聊天记录评论表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
public interface ChatHistoryCommentService extends BaseService<ChatHistoryCommentDO> {



    /**
     * 获取聊天记录的所有点赞评论
     * 
     * @param query
     * @return
     */
    List<ChatHistoryCommentResp> list(ChatHistoryCommentQueryDTO query);

    /**
     * 保存聊天记录评论表
     *
     * @param record
     * @return
     */
    Boolean save(ChatHistoryCommentReq record);


    /**
     * 删除聊天记录评论表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    ChatHistoryCommentResp detailByMessageId(ChatHistoryCommentQueryDetailDTO record);
}
