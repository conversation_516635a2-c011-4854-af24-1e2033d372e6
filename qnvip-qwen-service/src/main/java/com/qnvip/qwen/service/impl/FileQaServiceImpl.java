package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileQaDaoService;
import com.qnvip.qwen.dal.dto.FileQaDTO;
import com.qnvip.qwen.dal.dto.FileQaQueryDTO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.mapper.FileQaMapper;
import com.qnvip.qwen.service.FileQaService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileQaReq;
import com.qnvip.qwen.vo.response.FileQaResp;


/**
 * 文档QA表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Service
public class FileQaServiceImpl extends BaseServiceImpl<FileQaMapper, FileQaDO> implements FileQaService {

    @Resource
    private FileQaMapper fileQaMapper;

    @Resource
    private FileQaDaoService fileQaDaoService;

    @Override
    public Pageable<FileQaResp> page(FileQaQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileQaDTO> page = fileQaDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileQaResp.class)));
    }

    @Override
    public Boolean save(FileQaReq record) {
        return save(CopierUtil.copy(record, FileQaDO.class));
    }

    @Override
    public FileQaResp detail(Long id) {
        FileQaDO record = get(FileQaDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileQaResp.class);
    }


    @Override
    public Boolean delete(Long id) {
        return remove(FileQaDO::getId, id);
    }
}