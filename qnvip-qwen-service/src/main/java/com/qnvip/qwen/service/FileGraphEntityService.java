package com.qnvip.qwen.service;

import java.util.List;
import java.util.Set;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileGraphEntityQueryDTO;
import com.qnvip.qwen.dal.entity.FileGraphEntityDO;
import com.qnvip.qwen.vo.request.FileGraphEntityReq;
import com.qnvip.qwen.vo.response.FileGraphEntityResp;

/**
 * 文件图数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:44
 */
public interface FileGraphEntityService extends BaseService<FileGraphEntityDO> {

    /**
     * 文件图数据表分页
     *
     * @param query
     * @return
     */
    Pageable<FileGraphEntityResp> page(FileGraphEntityQueryDTO query);

    /**
     * 文件图数据表列表
     *
     * @param query
     * @return
     */
    List<FileGraphEntityResp> list(FileGraphEntityReq query);

    /**
     * 保存文件图数据表
     *
     * @param record
     * @return
     */
    Boolean save(FileGraphEntityReq record);

    /**
     * 获取文件图数据表详情
     *
     * @param id
     * @return
     */
    FileGraphEntityResp detail(Long id);

    /**
     * 删除文件图数据表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    List<FileGraphEntityDO> pageLoadEntity(List<Long> param);

    List<FileGraphEntityDO> groupListByName(Set<String> sourceEntities);
}
