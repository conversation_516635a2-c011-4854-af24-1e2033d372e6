package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.LlmSupportModelDaoService;
import com.qnvip.qwen.dal.dto.LlmSupportModelDTO;
import com.qnvip.qwen.dal.dto.LlmSupportModelQueryDTO;
import com.qnvip.qwen.dal.entity.LlmSupportModelDO;
import com.qnvip.qwen.dal.mapper.LlmSupportModelMapper;
import com.qnvip.qwen.service.LlmSupportModelService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.LlmSupportModelReq;
import com.qnvip.qwen.vo.response.LlmSupportModelResp;

/**
 * 支持的模型表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
@Service
public class LlmSupportModelServiceImpl extends BaseServiceImpl<LlmSupportModelMapper, LlmSupportModelDO>
    implements LlmSupportModelService {

    @Resource
    private LlmSupportModelMapper llmSupportModelMapper;

    @Resource
    private LlmSupportModelDaoService llmSupportModelDaoService;

    @Override
    public Pageable<LlmSupportModelResp> page(LlmSupportModelQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<LlmSupportModelDTO> page = lambdaQuery().setEntity(CopierUtil.copy(query, LlmSupportModelDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, LlmSupportModelResp.class)));
    }

    @Override
    public List<LlmSupportModelResp> list(LlmSupportModelReq query) {
        LlmSupportModelDO filter = CopierUtil.copy(query, LlmSupportModelDO.class);
        List<LlmSupportModelDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        return CopierUtil.copyList(list, LlmSupportModelResp.class);
    }

    @Override
    public Boolean save(LlmSupportModelReq record) {
        return saveOrUpdate(CopierUtil.copy(record, LlmSupportModelDO.class));
    }

    @Override
    public LlmSupportModelResp detail(Long id) {
        LlmSupportModelDO record = get(LlmSupportModelDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, LlmSupportModelResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(LlmSupportModelDO::getId, id);
    }
}