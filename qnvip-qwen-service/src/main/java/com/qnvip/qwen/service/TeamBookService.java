package com.qnvip.qwen.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamBookQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.vo.request.TeamBookReq;
import com.qnvip.qwen.vo.response.TeamBookResp;
import com.qnvip.qwen.vo.response.TeamResp;

/**
 * 知识库表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
public interface TeamBookService extends BaseService<TeamBookDO> {

    /**
     * 知识库表分页
     *
     * @param query
     * @return
     */
    Pageable<TeamBookResp> page(TeamBookQueryDTO query);

    /**
     * 保存知识库表
     *
     * @param record
     * @return
     */
    Boolean save(TeamBookReq record);

    /**
     * 获取知识库表详情
     *
     * @param id
     * @return
     */
    TeamBookResp detail(Long id);

    /**
     * 删除知识库表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 加载所有的库
     * 
     * @param teamId
     * @return
     */
    List<TeamBookDO> listByTeamId(Integer teamId);

    /**
     * 加载所有的库
     *
     * @param teamId
     * @param parentUid
     * @return
     */
    Pageable<TeamBookDO> pageLoadToc(Page<TeamBookDO> page, Integer teamId, String parentUid);

    /**
     * 加载所有的库名称
     *
     * @return
     */
    Map<Long, String> loadNameMap();

    /**
     *
     * @return
     */
    List<Long> getALLBookIds();

    List<TeamResp> list(TeamBookQueryDTO record);
}
