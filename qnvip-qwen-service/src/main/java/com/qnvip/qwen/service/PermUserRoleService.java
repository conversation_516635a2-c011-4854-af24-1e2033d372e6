package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.PermUserRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermUserRoleDO;
import com.qnvip.qwen.vo.request.PermUserRoleReq;
import com.qnvip.qwen.vo.request.UserRoleTokenReq;
import com.qnvip.qwen.vo.response.AdminAndTokenInfoResp;
import com.qnvip.qwen.vo.response.PermUserRoleResp;

/**
 * 用户角色表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public interface PermUserRoleService extends BaseService<PermUserRoleDO> {

    /**
     * 用户角色表分页
     *
     * @param query
     * @return
     */
    Pageable<PermUserRoleResp> page(PermUserRoleQueryDTO query);

    /**
     * 保存用户角色表
     *
     * @param record
     * @return
     */
    Boolean save(PermUserRoleReq record);

    /**
     * 获取用户角色表详情
     *
     * @param id
     * @return
     */
    PermUserRoleResp detail(Long id);

    /**
     * 删除用户角色表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 批量分配角色给用户
     *
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    Boolean assignUsers(Long roleId, List<Long> userIds);

    /**
     * 更新用户角色和Token配置
     *
     * @param req
     * @return
     */
    void updateTokenAndRoles(UserRoleTokenReq req);

    /**
     * 放入token和角色
     * 
     * @param adminResp
     */
    void putTokenAndRoles(AdminAndTokenInfoResp adminResp);

    /**
     * 放入角色
     *
     * @param adminAndTokenInfoResps
     * @param b
     */
    void putRoles(List<AdminAndTokenInfoResp> adminAndTokenInfoResps, boolean b);
}
