package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.UserCacheQueryDTO;
import com.qnvip.qwen.dal.entity.UserCacheDO;
import com.qnvip.qwen.vo.request.UserCacheReq;
import com.qnvip.qwen.vo.response.UserCacheResp;

/**
 * 用户缓存表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
public interface UserCacheService extends BaseService<UserCacheDO> {

    /**
     * 用户缓存表分页
     *
     * @param query
     * @return
     */
    Pageable<UserCacheResp> page(UserCacheQueryDTO query);

    /**
     * 用户缓存表列表
     *
     * @param query
     * @return
     */
    List<UserCacheResp> list(UserCacheReq query);

    /**
     * 保存用户缓存表
     *
     * @param record
     * @return
     */
    Boolean save(UserCacheReq record);

    /**
     * 获取用户缓存表详情
     *
     * @param id
     * @return
     */
    UserCacheResp detail(Long id);

    /**
     * 删除用户缓存表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
