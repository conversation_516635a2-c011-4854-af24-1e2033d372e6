package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.dto.TeamDTO;
import com.qnvip.qwen.dal.dto.TeamQueryDTO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.dal.mapper.TeamMapper;
import com.qnvip.qwen.service.TeamService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.TeamReq;
import com.qnvip.qwen.vo.response.TeamResp;

/**
 * 团队表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Service
public class TeamServiceImpl extends BaseServiceImpl<TeamMapper, TeamDO> implements TeamService {

    @Resource
    private TeamMapper teamMapper;

    @Resource
    private TeamDaoService teamDaoService;

    @Override
    public Pageable<TeamResp> page(TeamQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<TeamDTO> page = teamDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, TeamResp.class)));
    }

    @Override
    public Boolean save(TeamReq record) {
        return save(CopierUtil.copy(record, TeamDO.class));
    }

    @Override
    public TeamResp detail(Long id) {
        TeamDO record = get(TeamDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, TeamResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(TeamDO::getId, id);
    }

    @Override
    public Map<Long, String> loadTeamNameMap() {
        return list().stream().collect(Collectors.toMap(TeamDO::getId, TeamDO::getName));
    }

}