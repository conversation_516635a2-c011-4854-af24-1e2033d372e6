package com.qnvip.qwen.service;

import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.enums.TaskStepsEnum;

/**
 * 文件任务进度的产出数据日志表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
public interface FileTaskProgressDatalogService extends BaseService<FileTaskProgressDatalogDO> {

    /**
     * 记录任务产出的数据
     *
     * @param originFileId
     * @param taskProgressId 任务id
     * @param nowStep
     * @param data 任务的处理结果
     */
    void recordData(Long originFileId, Long taskProgressId, TaskStepsEnum nowStep, String data);

    /**
     * 加载任务产出的数据
     * 
     * @param taskProgressId
     * @param nowStep
     * @return
     */
    FileTaskProgressDatalogDO loadLatestData(Long taskProgressId, TaskStepsEnum nowStep);

    /**
     * 删除任务产出的数据
     *
     * @param fileOriginId
     */
    void deleteByFileOriginId(Long fileOriginId);
}
