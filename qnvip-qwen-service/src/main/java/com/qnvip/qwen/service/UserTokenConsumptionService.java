package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConsumptionDO;
import com.qnvip.qwen.vo.request.UserTokenConsumptionReq;
import com.qnvip.qwen.vo.response.UserTokenConsumptionResp;

/**
 * 用户token消耗表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
public interface UserTokenConsumptionService extends BaseService<UserTokenConsumptionDO> {

    /**
     * 用户token消耗表分页
     *
     * @param query
     * @return
     */
    Pageable<UserTokenConsumptionResp> page(UserTokenConsumptionQueryDTO query);

    /**
     * 保存用户token消耗表
     *
     * @param record
     * @param isSystemRecord
     * @return
     */
    Boolean save(UserTokenConsumptionReq record, boolean isSystemRecord);

    /**
     * 获取用户token消耗表详情
     *
     * @param id
     * @return
     */
    UserTokenConsumptionResp detail(Long id);


    /**
     * 删除用户token消耗表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    Boolean saveSystem(UserTokenConsumptionReq record, boolean b);
}
