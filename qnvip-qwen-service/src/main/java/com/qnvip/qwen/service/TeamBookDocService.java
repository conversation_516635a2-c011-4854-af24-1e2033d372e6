package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamBookDocQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.vo.request.TeamBookDocReq;
import com.qnvip.qwen.vo.response.TeamBookDocResp;

/**
 * 文档表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
public interface TeamBookDocService extends BaseService<TeamBookDocDO> {

    /**
     * 文档表分页
     *
     * @param query
     * @return
     */
    Pageable<TeamBookDocResp> page(TeamBookDocQueryDTO query);

    /**
     * 保存文档表
     *
     * @param record
     * @return
     */
    Boolean save(TeamBookDocReq record);

    /**
     * 获取文档表详情
     *
     * @param id
     * @return
     */
    TeamBookDocResp detail(Long id);

    /**
     * 删除文档表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    List<TeamBookDocDO> getListByBookId(List<Long> bookIds);

    /**
     * 获取文件的目录
     * 
     * @param bookId
     * @param docUid
     * @return
     */
    List<String> loadTocNames(Long bookId, String docUid);
}
