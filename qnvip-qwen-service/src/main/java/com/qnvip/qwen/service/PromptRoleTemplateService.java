package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.PromptRoleTemplateQueryDTO;
import com.qnvip.qwen.dal.entity.PromptRoleTemplateDO;
import com.qnvip.qwen.vo.request.PromptRoleTemplateReq;
import com.qnvip.qwen.vo.response.PromptRoleTemplateResp;
import com.qnvip.qwen.vo.response.TeamBookDocSummaryResp;

/**
 * 角色提示词模板表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
public interface PromptRoleTemplateService extends BaseService<PromptRoleTemplateDO> {

    /**
     * 角色提示词模板表分页
     *
     * @param query
     * @return
     */
    Pageable<PromptRoleTemplateResp> page(PromptRoleTemplateQueryDTO query);

    /**
     * 角色提示词模板表列表
     *
     * @param query
     * @return
     */
    List<PromptRoleTemplateResp> list(PromptRoleTemplateReq query);

    /**
     * 保存角色提示词模板表
     *
     * @param record
     * @return
     */
    Boolean save(PromptRoleTemplateReq record);

    /**
     * 获取角色提示词模板表详情
     *
     * @param id
     * @return
     */
    PromptRoleTemplateResp detail(Long id);

    /**
     * 删除角色提示词模板表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 内容替换
     * 
     * @param content
     * @param resp
     * @return
     */
    String contentReplace(String content, List<TeamBookDocSummaryResp> resp);
}
