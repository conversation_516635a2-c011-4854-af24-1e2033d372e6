package com.qnvip.qwen.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileTaskProgressDatalogDaoService;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.dal.mapper.FileTaskProgressDatalogMapper;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileTaskProgressDatalogService;

/**
 * 文件任务进度的产出数据日志表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
@Service
public class FileTaskProgressDatalogServiceImpl
    extends BaseServiceImpl<FileTaskProgressDatalogMapper, FileTaskProgressDatalogDO>
    implements FileTaskProgressDatalogService {

    @Resource
    private FileTaskProgressDatalogMapper fileTaskProgressDatalogMapper;

    @Resource
    private FileTaskProgressDatalogDaoService fileTaskProgressDatalogDaoService;

    @Override
    public void recordData(Long originFileId, Long taskProgressId, TaskStepsEnum nowStep, String taskData) {
        FileTaskProgressDatalogDO datalogDO = new FileTaskProgressDatalogDO();
        datalogDO.setFileOriginId(originFileId);
        datalogDO.setTaskProgressId(taskProgressId);
        datalogDO.setNowStep(nowStep.getCode());
        datalogDO.setData(taskData);
        save(datalogDO);
    }

    @Override
    public FileTaskProgressDatalogDO loadLatestData(Long originFileId, TaskStepsEnum nowStep) {
        return lambdaQuery().eq(FileTaskProgressDatalogDO::getFileOriginId, originFileId)
            .eq(FileTaskProgressDatalogDO::getNowStep, nowStep.getCode()).orderByDesc(FileTaskProgressDatalogDO::getId)
            .last("limit 1").one();
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        lambdaUpdate().eq(FileTaskProgressDatalogDO::getFileOriginId, fileOriginId).remove();
    }
}