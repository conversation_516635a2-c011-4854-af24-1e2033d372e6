package com.qnvip.qwen.service.open.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.entity.open.ItemKeywordDO;
import com.qnvip.qwen.dal.mapper.open.ItemKeywordMapper;
import com.qnvip.qwen.service.open.ItemKeywordService;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月22日 16:22:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemKeywordServiceImpl extends BaseServiceImpl<ItemKeywordMapper, ItemKeywordDO>
    implements ItemKeywordService {

    @Resource
    private ItemKeywordMapper itemKeywordMapper;

    /**
     * 根据ID列表获取关键词列表
     *
     * @param ids ID集合
     * @return 关键词列表
     */
    @Override
    public List<ItemKeywordDO> getListByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        LambdaQueryWrapper<ItemKeywordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ItemKeywordDO::getId, ids);

        return list(queryWrapper);
    }

    /**
     * 根据平台ID获取关键词列表
     *
     * @param platformId 平台ID
     * @return 关键词列表
     */
    @Override
    public List<ItemKeywordDO> getByPlatformId(Long platformId) {
        if (platformId == null) {
            return CollUtil.newArrayList();
        }

        LambdaQueryWrapper<ItemKeywordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemKeywordDO::getPlatformId, platformId);

        return list(queryWrapper);
    }
}
