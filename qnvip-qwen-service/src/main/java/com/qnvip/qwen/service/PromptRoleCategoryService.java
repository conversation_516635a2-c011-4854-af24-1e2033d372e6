package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryQueryDTO;
import com.qnvip.qwen.dal.entity.PromptRoleCategoryDO;
import com.qnvip.qwen.vo.request.PromptRoleCategoryReq;
import com.qnvip.qwen.vo.response.PromptRoleCategoryResp;

/**
 * 角色提示词分类表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
public interface PromptRoleCategoryService extends BaseService<PromptRoleCategoryDO> {

    /**
     * 角色提示词分类表分页
     *
     * @param query
     * @return
     */
    Pageable<PromptRoleCategoryResp> page(PromptRoleCategoryQueryDTO query);

    /**
     * 角色提示词分类表列表
     *
     * @param query
     * @return
     */
    List<PromptRoleCategoryResp> list(PromptRoleCategoryReq query);

    /**
     * 保存角色提示词分类表
     *
     * @param record
     * @return
     */
    Boolean save(PromptRoleCategoryReq record);

    /**
     * 获取角色提示词分类表详情
     *
     * @param id
     * @return
     */
    PromptRoleCategoryResp detail(Long id);

    /**
     * 删除角色提示词分类表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);
}
