package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.PermUserRoleDaoService;
import com.qnvip.qwen.dal.dto.PermUserRoleDTO;
import com.qnvip.qwen.dal.dto.PermUserRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermUserRoleDO;
import com.qnvip.qwen.dal.mapper.PermUserRoleMapper;
import com.qnvip.qwen.service.PermRoleService;
import com.qnvip.qwen.service.PermUserRoleService;
import com.qnvip.qwen.service.UserTokenConfigService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.vo.request.PermUserRoleReq;
import com.qnvip.qwen.vo.request.UserRoleTokenReq;
import com.qnvip.qwen.vo.response.AdminAndTokenInfoResp;
import com.qnvip.qwen.vo.response.PermRoleResp;
import com.qnvip.qwen.vo.response.PermUserRoleResp;

/**
 * 用户角色表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Service
public class PermUserRoleServiceImpl extends BaseServiceImpl<PermUserRoleMapper, PermUserRoleDO>
    implements PermUserRoleService {

    @Resource
    private PermUserRoleMapper permUserRoleMapper;

    @Resource
    private PermUserRoleDaoService permUserRoleDaoService;

    @Resource
    private UserTokenConfigService userTokenConfigService;

    @Resource
    private PermRoleService permRoleService;

    @Override
    public Pageable<PermUserRoleResp> page(PermUserRoleQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<PermUserRoleDTO> page = permUserRoleDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, PermUserRoleResp.class)));
    }

    @Override
    public Boolean save(PermUserRoleReq record) {
        return save(CopierUtil.copy(record, PermUserRoleDO.class));
    }

    @Override
    public PermUserRoleResp detail(Long id) {
        PermUserRoleDO record = get(PermUserRoleDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, PermUserRoleResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(PermUserRoleDO::getId, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignUsers(Long roleId, List<Long> userIds) {
        // 1. 查询该角色下已存在的用户角色关系
        List<PermUserRoleDO> existingRoles =
            list(new LambdaQueryWrapper<PermUserRoleDO>().eq(PermUserRoleDO::getRoleId, roleId));

        // 2. 过滤出需要新增的用户角色关系
        List<PermUserRoleDO> newUserRoles = userIds.stream()
            .filter(userId -> existingRoles.stream().noneMatch(existing -> existing.getUserId().equals(userId)))
            .map(userId -> {
                PermUserRoleDO userRole = new PermUserRoleDO();
                userRole.setRoleId(roleId);
                userRole.setUserId(userId);
                return userRole;
            }).collect(Collectors.toList());

        // 3. 批量保存新的用户角色关系
        boolean addSuccess = saveBatch(newUserRoles);

        // 3. 过滤出需要删除的用户角色关系
        List<Long> rolesToDelete = existingRoles.stream().filter(existing -> !userIds.contains(existing.getUserId()))
            .map(PermUserRoleDO::getId).collect(Collectors.toList());

        // 5. 删除不再需要的用户角色关系
        boolean deleteSuccess = true;
        if (!rolesToDelete.isEmpty()) {
            deleteSuccess = removeByIds(rolesToDelete);
        }

        return addSuccess && deleteSuccess;
    }

    @Override
    public void updateTokenAndRoles(UserRoleTokenReq req) {
        // 1. 更新Token配置
        if (req.getPermissionAuthorizedToken() != null && req.getPermissionAuthorizedToken() > 0) {
            userTokenConfigService.updateUserTokenConfig(req.getUserId(), req.getPermissionAuthorizedToken());
        }

        // 2. 删除原有用户角色关系
        lambdaUpdate().eq(PermUserRoleDO::getUserId, req.getUserId()).remove();

        // 3. 插入新的用户角色关系
        for (Long roleId : req.getRoleIds()) {
            PermUserRoleDO userRole = new PermUserRoleDO();
            userRole.setUserId(req.getUserId());
            userRole.setRoleId(roleId);
            save(userRole);
        }
    }

    @Override
    public void putTokenAndRoles(AdminAndTokenInfoResp adminResp) {
        userTokenConfigService.putTokenInfo(Lists.newArrayList(adminResp));

        putRoles(Lists.newArrayList(adminResp), false);
    }

    @Override
    public void putRoles(List<AdminAndTokenInfoResp> adminAndTokenInfoResps, boolean hideNotPicked) {
        if (adminAndTokenInfoResps == null || adminAndTokenInfoResps.isEmpty()) {
            return;
        }

        // 1. 获取所有角色
        List<PermRoleResp> allRoles = permRoleService.list().stream()
            .map(role -> CopierUtil.copy(role, PermRoleResp.class)).collect(Collectors.toList());

        // 2. 获取所有用户的角色ID
        List<Long> userIds = adminAndTokenInfoResps.stream().map(AdminAndTokenInfoResp::getId).map(Long::valueOf)
            .collect(Collectors.toList());

        List<PermUserRoleDO> userRoles =
            list(new LambdaQueryWrapper<PermUserRoleDO>().in(PermUserRoleDO::getUserId, userIds));

        // 3. 按用户ID分组角色
        Map<Long, List<Long>> userRoleMap = userRoles.stream().collect(Collectors.groupingBy(PermUserRoleDO::getUserId,
            Collectors.mapping(PermUserRoleDO::getRoleId, Collectors.toList())));

        // 4. 为每个用户设置角色信息
        adminAndTokenInfoResps.forEach(admin -> {
            List<Long> userRoleIds = userRoleMap.getOrDefault(admin.getId().longValue(), new ArrayList<>());

            List<PermRoleResp> userRolesWithPicked = new ArrayList<>();
            if (hideNotPicked) {
                userRolesWithPicked = allRoles.stream().map(role -> {
                    PermRoleResp copiedRole = CopierUtil.copy(role, PermRoleResp.class);
                    copiedRole.setPicked(userRoleIds.contains(role.getId()));
                    return copiedRole;
                }).filter(PermRoleResp::getPicked).collect(Collectors.toList());

            } else {
                // 复制所有角色并标记已分配的角色
                userRolesWithPicked = allRoles.stream().map(role -> {
                    PermRoleResp copiedRole = CopierUtil.copy(role, PermRoleResp.class);
                    copiedRole.setPicked(userRoleIds.contains(role.getId()));
                    return copiedRole;
                }).collect(Collectors.toList());
            }

            admin.setRoles(userRolesWithPicked);
        });
    }
}