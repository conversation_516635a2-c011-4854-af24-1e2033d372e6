package com.qnvip.qwen.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dto.FileOriginDTO;
import com.qnvip.qwen.dal.dto.FileOriginQueryDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.dal.mapper.FileOriginMapper;
import com.qnvip.qwen.enums.FileExtEnum;
import com.qnvip.qwen.enums.MatchTypeEnum;
import com.qnvip.qwen.service.FileOriginService;
import com.qnvip.qwen.service.FileTaskProgressService;
import com.qnvip.qwen.service.TeamService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.ExcelJsonToMarkdownTableUtil;
import com.qnvip.qwen.util.HttpUrlExtractUtil;
import com.qnvip.qwen.util.rag.ChunkUtil;
import com.qnvip.qwen.vo.request.FileOriginReq;
import com.qnvip.qwen.vo.response.FileOriginResp;
import com.qnvip.qwen.vo.response.TeamBookDocSummaryResp;

/**
 * 原始文件表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
@Service
public class FileOriginServiceImpl extends BaseServiceImpl<FileOriginMapper, FileOriginDO>
    implements FileOriginService {

    @Resource
    private FileOriginDaoService fileOriginDaoService;

    @Resource
    private TeamBookDocDaoService teamBookDocService;

    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;

    @Resource
    private FileTaskProgressService fileTaskProgressService;

    @Resource
    private TeamService teamService;


    @Override
    public Pageable<FileOriginResp> page(FileOriginQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileOriginDTO> page = fileOriginDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileOriginResp.class)));
    }

    @Override
    public Boolean save(FileOriginReq record) {
        return save(CopierUtil.copy(record, FileOriginDO.class));
    }

    @Override
    public FileOriginResp detail(Long id) {
        FileOriginDO record = get(FileOriginDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileOriginResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(FileOriginDO::getId, id);
    }

    @Override
    public void updateToc(Long fileOriginId, List<String> tocs) {
        FileOriginDO fileOriginDO = new FileOriginDO();
        fileOriginDO.setId(fileOriginId);
        fileOriginDO.setTocs(String.join("/", tocs));

        updateById(fileOriginDO);
    }

    @Override
    public void createMarkdownTableSheet(FileOriginDO origin, List<ExcelJsonToMarkdownTableUtil.SheetData> mds) {
        if (ObjectUtils.isEmpty(mds)) {
            return;
        }

        TeamBookDocDO doc = teamBookDocService.getByBookIdAndUid(origin.getBookId(), origin.getDocUid());
        if (doc == null) {
            return;
        }

        TeamBookDocDataDO docData = teamBookDocDataDaoService.getByBookIdAndUid(origin.getBookId(), origin.getDocUid());
        if (docData == null) {
            return;
        }

        createSub(doc, docData, origin, mds);
    }

    @Override
    public List<Long> listBookIdsByIds(List<Long> needFileOriginIds) {
        if (ObjectUtils.isEmpty(needFileOriginIds)) {
            return new ArrayList<>();
        }
        List<FileOriginDO> list =
            lambdaQuery().select(FileOriginDO::getBookId).in(FileOriginDO::getId, needFileOriginIds).list();
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(FileOriginDO::getBookId).distinct().collect(Collectors.toList());
    }

    private void createSub(TeamBookDocDO parentDoc, TeamBookDocDataDO parentDocData, FileOriginDO parentFile,
        List<ExcelJsonToMarkdownTableUtil.SheetData> mds) {

        List<String> newChecksums =
            mds.stream().map(ExcelJsonToMarkdownTableUtil.SheetData::getChecksum).collect(Collectors.toList());

        // 找出老的子文件
        List<FileOriginDO> oldSubFiles = fileOriginDaoService.listByParentId(parentFile.getId());
        // 找出老的子文件checksums
        List<String> oldChecksums = oldSubFiles.stream().map(FileOriginDO::getChecksum).collect(Collectors.toList());

        // 旧集合比新集合多的就是需要删除的
        List<String> needRemoves =
            oldChecksums.stream().filter(e -> !newChecksums.contains(e)).collect(Collectors.toList());
        List<Long> fileIds = oldSubFiles.stream().filter(e -> needRemoves.contains(e.getChecksum()))
            .map(FileOriginDO::getId).collect(Collectors.toList());
        fileTaskProgressService.deleteAllByFileOriginIds(fileIds);

        // 新集合比旧集合多的就是需要添加的
        List<String> needAdds =
            newChecksums.stream().filter(e -> !oldChecksums.contains(e)).collect(Collectors.toList());
        // 保存需要添加的
        mds = mds.stream().filter(md -> needAdds.contains(md.getChecksum())).collect(Collectors.toList());

        List<Long> subFileOriginIds = new ArrayList<>();
        for (ExcelJsonToMarkdownTableUtil.SheetData md : mds) {
            if (ObjectUtils.isEmpty(md.getData())) {
                continue;
            }

            String checksum = md.getChecksum();
            String fileName = parentDoc.getName() + "_" + md.getName();

            TeamBookDocDO beforeDoc = teamBookDocService.getByBookIdAndUid(parentFile.getBookId(), checksum);
            if (beforeDoc == null) {
                TeamBookDocDO subDoc = CopierUtil.copy(parentDoc, TeamBookDocDO.class);
                subDoc.setParentId(subDoc.getId());
                subDoc.setMark(checksum);
                subDoc.setUid(checksum);
                subDoc.setName(fileName);
                subDoc.setId(null);
                subDoc.setCreateTime(null);
                subDoc.setUpdateTime(null);
                teamBookDocService.save(subDoc);
            } else {
                beforeDoc.setName(fileName);
                beforeDoc.setUpdateTime(LocalDateTime.now());
                teamBookDocService.updateById(beforeDoc);
            }

            TeamBookDocDataDO beforeDocData =
                teamBookDocDataDaoService.getByBookIdAndUid(parentFile.getBookId(), checksum);
            if (beforeDocData == null) {
                TeamBookDocDataDO subDocData = CopierUtil.copy(parentDocData, TeamBookDocDataDO.class);
                subDocData.setParentId(subDocData.getId());
                subDocData.setDocUid(checksum);
                subDocData.setMark(checksum);

                subDocData.setId(null);
                subDocData.setData(md.getData());
                subDocData.setType(FileExtEnum.MD.getDesc());
                subDocData.setCreateTime(LocalDateTime.now());
                subDocData.setUpdateTime(LocalDateTime.now());
                teamBookDocDataDaoService.save(subDocData);
            } else {
                beforeDocData.setData(md.getData());
                beforeDocData.setUpdateTime(LocalDateTime.now());
                teamBookDocDataDaoService.updateById(beforeDocData);
            }

            FileOriginDO beforeOrigin =
                fileOriginDaoService.getByFileOrigin(parentFile.getTeamId(), parentFile.getBookId(), checksum);
            if (beforeOrigin == null) {
                FileOriginDO subFile = CopierUtil.copy(parentFile, FileOriginDO.class);
                subFile.setParentId(subFile.getId());
                subFile.setDocUid(checksum);
                subFile.setChecksum(checksum);
                subFile.setFileExtension(FileExtEnum.MD.getDesc());
                subFile.setId(null);
                subFile.setFileName(fileName);
                subFile.setCreateTime(null);
                subFile.setUpdateTime(null);
                fileOriginDaoService.save(subFile);

                subFileOriginIds.add(subFile.getId());
            } else {
                beforeOrigin.setUpdateTime(LocalDateTime.now());
                beforeOrigin.setFileName(fileName);
                fileOriginDaoService.updateById(beforeOrigin);
                subFileOriginIds.add(beforeOrigin.getId());
            }
        }

        if (ObjectUtils.isNotEmpty(subFileOriginIds)) {
            fileTaskProgressService.saveTaskStartWithFormat(parentFile.getBookId(), subFileOriginIds);
        }
    }

    @Override
    public List<TeamBookDocSummaryResp> urlMatchDoc(String content) {
        List<String> urls = HttpUrlExtractUtil.extractHttpUrls(content);
        if (ObjectUtils.isEmpty(urls)) {
            return new ArrayList<>();
        }

        List<FileOriginDO> fileOrigins =
            lambdaQuery().eq(FileOriginDO::getParentId, 0).in(FileOriginDO::getTargetOriginUrl, urls).list();
        if (ObjectUtils.isEmpty(fileOrigins)) {
            return new ArrayList<>();
        }
        Map<String, FileOriginDO> hitFileOrigins =
            fileOrigins.stream().collect(Collectors.toMap(FileOriginDO::getTargetOriginUrl, e -> e, (e1, e2) -> e1));

        return urls.stream().map(e -> {
            FileOriginDO originDO = hitFileOrigins.get(e);
            if (originDO == null) {
                TeamBookDocSummaryResp r = new TeamBookDocSummaryResp();
                r.setStatus(MatchTypeEnum.FAILURE.getCode());
                r.setStatusErrMsg("匹配语雀链接失败，请检查连接");
                r.setReplaceKey(e);
                return r;
            }

            TeamBookDocSummaryResp r = new TeamBookDocSummaryResp();
            r.setBookId(originDO.getBookId());
            r.setStatus(MatchTypeEnum.SUCCESS.getCode());
            r.setFileOriginId(originDO.getId());
            r.setReplaceKey(e);
            r.setReplaceValue(
                ObjectUtils.isEmpty(originDO.getTocs()) ? originDO.getFileName() : filterTeamName(originDO.getTocs()));
            return r;

        }).collect(Collectors.toList());
    }

    public String filterTeamName(String tocs) {
        tocs = ChunkUtil.removeFirstToc(tocs);
        return tocs;
    }
}