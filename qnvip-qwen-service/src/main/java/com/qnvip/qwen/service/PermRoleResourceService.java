package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.PermRoleResourceQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleResourceDO;
import com.qnvip.qwen.vo.request.PermRoleResourceReq;
import com.qnvip.qwen.vo.request.RoleAssignBooksReq;
import com.qnvip.qwen.vo.response.PermRoleResourceResp;

/**
 * 角色资源表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public interface PermRoleResourceService extends BaseService<PermRoleResourceDO> {

    /**
     * 角色资源表分页
     *
     * @param query
     * @return
     */
    Pageable<PermRoleResourceResp> page(PermRoleResourceQueryDTO query);

    /**
     * 保存角色资源表
     *
     * @param record
     * @return
     */
    Boolean save(PermRoleResourceReq record);

    /**
     * 获取角色资源表详情
     *
     * @param id
     * @return
     */
    PermRoleResourceResp detail(Long id);

    /**
     * 删除角色资源表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 分配知识库给角色
     *
     * @param req 角色分配知识库请求
     * @return 是否成功
     */
    Boolean assignBooks(RoleAssignBooksReq req);

    /**
     * 获取用户的知识库ID列表
     *
     * @param userId 用户ID
     * @return 知识库ID列表
     */
    List<Long> getUserKnowledgeBaseIds(Long userId);
}
