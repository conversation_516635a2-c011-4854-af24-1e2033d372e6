package com.qnvip.qwen.service;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileSummaryQueryDTO;
import com.qnvip.qwen.dal.entity.FileSummaryDO;
import com.qnvip.qwen.vo.request.FileSummaryReq;
import com.qnvip.qwen.vo.response.FileSummaryResp;

/**
 * 文件概括表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
public interface FileSummaryService extends BaseService<FileSummaryDO> {

    /**
     * 文件概括表分页
     *
     * @param query
     * @return
     */
    Pageable<FileSummaryResp> page(FileSummaryQueryDTO query);

    /**
     * 保存文件概括表
     *
     * @param record
     * @return
     */
    Boolean save(FileSummaryReq record);

    /**
     * 获取文件概括表详情
     *
     * @param id
     * @return
     */
    FileSummaryResp detail(Long id);

    /**
     * 删除文件概括表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 保存并且删除之前的数据
     * 
     * @param fileOriginId
     * @param summary
     */
    void saveDeleteBefore(Long fileOriginId, String summary);
}
