package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.FileQaDataDaoService;
import com.qnvip.qwen.dal.dto.FileQaDataDTO;
import com.qnvip.qwen.dal.dto.FileQaDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileQaDataDO;
import com.qnvip.qwen.dal.mapper.FileQaDataMapper;
import com.qnvip.qwen.service.FileQaDataService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.FileQaDataReq;
import com.qnvip.qwen.vo.response.FileQaDataResp;


/**
 * 文件问答数据表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Service
public class FileQaDataServiceImpl extends BaseServiceImpl<FileQaDataMapper, FileQaDataDO>
    implements FileQaDataService {

    @Resource
    private FileQaDataMapper fileQaDataMapper;

    @Resource
    private FileQaDataDaoService fileQaDataDaoService;

    @Override
    public Pageable<FileQaDataResp> page(FileQaDataQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<FileQaDataDTO> page = fileQaDataDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, FileQaDataResp.class)));
    }

    @Override
    public Boolean save(FileQaDataReq record) {
        return save(CopierUtil.copy(record, FileQaDataDO.class));
    }

    @Override
    public FileQaDataResp detail(Long id) {
        FileQaDataDO record = get(FileQaDataDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, FileQaDataResp.class);
    }


    @Override
    public Boolean delete(Long id) {
        return remove(FileQaDataDO::getId, id);
    }
}