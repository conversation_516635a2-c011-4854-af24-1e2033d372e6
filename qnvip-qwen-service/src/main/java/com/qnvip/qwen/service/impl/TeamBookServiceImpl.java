package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dto.SimpleTeamBookDTO;
import com.qnvip.qwen.dal.dto.TeamBookQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.dal.mapper.TeamBookMapper;
import com.qnvip.qwen.service.TeamBookService;
import com.qnvip.qwen.service.TeamService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.TeamBookReq;
import com.qnvip.qwen.vo.response.TeamBookResp;
import com.qnvip.qwen.vo.response.TeamResp;

/**
 * 知识库表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Service
public class TeamBookServiceImpl extends BaseServiceImpl<TeamBookMapper, TeamBookDO> implements TeamBookService {

    @Resource
    private TeamBookMapper teamBookMapper;

    private static final int DEFAULT_NUMBER = 10000;

    @Resource
    private TeamService teamService;

    @Override
    public Pageable<TeamBookResp> page(TeamBookQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        return null;
    }

    @Override
    public Boolean save(TeamBookReq record) {
        return save(CopierUtil.copy(record, TeamBookDO.class));
    }

    @Override
    public TeamBookResp detail(Long id) {
        TeamBookDO record = get(TeamBookDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, TeamBookResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(TeamBookDO::getId, id);
    }

    @Override
    public List<TeamBookDO> listByTeamId(Integer teamId) {
        return Collections.emptyList();
    }

    @Override
    public Pageable<TeamBookDO> pageLoadToc(Page<TeamBookDO> page, Integer teamId, String parentUid) {

        return teamBookMapper.pageLoadToc(page, teamId, parentUid);
    }

    @Override
    public Map<Long, String> loadNameMap() {
        return list().stream().collect(Collectors.toMap(TeamBookDO::getId, TeamBookDO::getName));
    }

    @Override
    public List<Long> getALLBookIds() {
        return lambdaQuery().select(TeamBookDO::getId).list().stream().map(TeamBookDO::getId)
            .collect(Collectors.toList());
    }

    @Override
    public List<TeamResp> list(TeamBookQueryDTO filter) {
        // 获取TeamBook数据
        List<TeamBookDO> list = lambdaQuery().eq(filter.getId() != null, TeamBookDO::getId, filter.getId())
            .in(filter.getIds() != null, TeamBookDO::getId, filter.getIds())
            .like(!ObjectUtils.isEmpty(filter.getName()), TeamBookDO::getName, filter.getName()).list();

        // 转换为SimpleTeamBookDTO
        List<SimpleTeamBookDTO> teamBooks = CopierUtil.copyList(list, SimpleTeamBookDTO.class);

        // 获取Team数据并转换为TeamResp
        List<TeamResp> teams = CopierUtil.copyList(teamService.list(), TeamResp.class);

        // 将teamBooks按照teamId分组
        Map<Long, List<SimpleTeamBookDTO>> teamBooksGroupByTeamId =
            teamBooks.stream().collect(Collectors.groupingBy(SimpleTeamBookDTO::getTeamId));

        // 将分组后的teamBooks设置到对应team的children字段中
        teams.forEach(team -> {
            Long teamId = team.getId();
            List<SimpleTeamBookDTO> children = teamBooksGroupByTeamId.getOrDefault(teamId, Collections.emptyList());
            team.setChildren(children);
        });

        List<TeamResp> collect =
            teams.stream().filter(team -> !ObjectUtils.isEmpty(team.getChildren())).collect(Collectors.toList());

        for (TeamResp teamResp : collect) {
            teamResp.setId(teamResp.getId() + DEFAULT_NUMBER);
            for (SimpleTeamBookDTO child : teamResp.getChildren()) {
                child.setTeamId(child.getTeamId() + DEFAULT_NUMBER);
            }

        }

        return collect;
    }

}