package com.qnvip.qwen.service.impl;

import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dto.TeamBookDocDataDTO;
import com.qnvip.qwen.dal.dto.TeamBookDocDataQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.dal.mapper.TeamBookDocDataMapper;
import com.qnvip.qwen.enums.BooleanEnum;
import com.qnvip.qwen.service.TeamBookDocDataService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.TeamBookDocDataReq;
import com.qnvip.qwen.vo.response.TeamBookDocDataResp;

/**
 * 文档表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
@Service
public class TeamBookDocDataServiceImpl extends BaseServiceImpl<TeamBookDocDataMapper, TeamBookDocDataDO>
    implements TeamBookDocDataService {

    @Resource
    private TeamBookDocDataMapper teamBookDocDataMapper;

    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;

    @Override
    public Pageable<TeamBookDocDataResp> page(TeamBookDocDataQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<TeamBookDocDataDTO> page = teamBookDocDataDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, TeamBookDocDataResp.class)));
    }

    @Override
    public Boolean save(TeamBookDocDataReq record) {
        return save(CopierUtil.copy(record, TeamBookDocDataDO.class));
    }

    @Override
    public TeamBookDocDataResp detail(Long id) {
        TeamBookDocDataDO record = get(TeamBookDocDataDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, TeamBookDocDataResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(TeamBookDocDataDO::getId, id);
    }

    @Override
    public TeamBookDocDataDO getByDocUid(String docUid) {
        return lambdaQuery().eq(TeamBookDocDataDO::getDocUid, docUid)
            .eq(TeamBookDocDataDO::getIsDeleted, BooleanEnum.FALSE.getValue()).one();
    }

}