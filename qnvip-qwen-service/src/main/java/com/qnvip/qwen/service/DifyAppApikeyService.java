package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.DifyAppApikeyQueryDTO;
import com.qnvip.qwen.dal.entity.DifyAppApikeyDO;
import com.qnvip.qwen.vo.request.DifyAppApikeyReq;
import com.qnvip.qwen.vo.response.DifyAppApikeyResp;
import com.qnvip.qwen.vo.response.LLMUrlTokenResp;

/**
 * dify应用的apikey表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
public interface DifyAppApikeyService extends BaseService<DifyAppApikeyDO> {

    /**
     * dify应用的apikey表分页
     *
     * @param query
     * @return
     */
    Pageable<DifyAppApikeyResp> page(DifyAppApikeyQueryDTO query);

    /**
     * dify应用的apikey表列表
     *
     * @param query
     * @return
     */
    List<DifyAppApikeyResp> list(DifyAppApikeyReq query);

    /**
     * 保存dify应用的apikey表
     *
     * @param record
     * @return
     */
    Boolean save(DifyAppApikeyReq record);

    /**
     * 获取dify应用的apikey表详情
     *
     * @param id
     * @return
     */
    DifyAppApikeyResp detail(Long id);

    /**
     * 删除dify应用的apikey表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    LLMUrlTokenResp getByModelId(Integer modelId, String type);
}
