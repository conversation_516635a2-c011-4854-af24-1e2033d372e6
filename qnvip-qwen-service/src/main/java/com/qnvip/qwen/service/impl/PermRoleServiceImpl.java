package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.PermRoleDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDaoService;
import com.qnvip.qwen.dal.dto.PermRoleDTO;
import com.qnvip.qwen.dal.dto.PermRoleQueryDTO;
import com.qnvip.qwen.dal.dto.SimpleTeamBookDTO;
import com.qnvip.qwen.dal.entity.PermRoleDO;
import com.qnvip.qwen.dal.entity.PermRoleResourceDO;
import com.qnvip.qwen.dal.entity.PermUserRoleDO;
import com.qnvip.qwen.dal.mapper.PermRoleMapper;
import com.qnvip.qwen.dal.mapper.PermRoleResourceMapper;
import com.qnvip.qwen.enums.ResourceTypeEnum;
import com.qnvip.qwen.service.PermRoleResourceService;
import com.qnvip.qwen.service.PermRoleService;
import com.qnvip.qwen.service.PermUserRoleService;
import com.qnvip.qwen.service.TeamService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.PermRoleReq;
import com.qnvip.qwen.vo.response.AdminAndRoleAssignResp;
import com.qnvip.qwen.vo.response.PermRoleDetailResp;
import com.qnvip.qwen.vo.response.PermRoleResp;
import com.qnvip.qwen.vo.response.TeamResp;

/**
 * 角色表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Service
public class PermRoleServiceImpl extends BaseServiceImpl<PermRoleMapper, PermRoleDO> implements PermRoleService {

    @Resource
    private PermRoleMapper permRoleMapper;

    @Resource
    private PermRoleDaoService permRoleDaoService;

    @Resource
    private PermUserRoleService permUserRoleService;

    @Resource
    private PermRoleResourceService permRoleResourceService;

    @Resource
    private PermRoleResourceMapper permRoleResourceMapper;

    @Resource
    private TeamBookDaoService teamBookDaoService;

    @Resource
    private TeamService teamService;


    @Override
    public Pageable<PermRoleResp> page(PermRoleQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<PermRoleDTO> page = permRoleDaoService.page(MybatisPlusUtils.convertFrom(query), query);
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, PermRoleResp.class)));
    }

    @Override
    public Boolean save(PermRoleReq record) {
        PermRoleDO copy = CopierUtil.copy(record, PermRoleDO.class);
        boolean save = save(copy);
        record.setId(copy.getId());
        return save;
    }

    @Override
    public PermRoleResp detail(Long id) {
        PermRoleDO record = get(PermRoleDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, PermRoleResp.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long roleId) {
        // 1. 删除用户角色关系
        permUserRoleService.lambdaUpdate().eq(PermUserRoleDO::getRoleId, roleId).remove();

        // 2. 删除角色资源关系
        permRoleResourceService.lambdaUpdate().eq(PermRoleResourceDO::getRoleId, roleId).remove();

        // 3. 删除角色本身
        return remove(PermRoleDO::getId, roleId);
    }

    @Override
    public PermRoleDetailResp getDetail(Long id) {
        // 1. 获取角色信息
        PermRoleDO role = get(PermRoleDO::getId, id);
        if (role == null) {
            return null;
        }

        // 2. 获取角色资源关系
        List<PermRoleResourceDO> roleResources = permRoleResourceMapper
            .selectList(new LambdaQueryWrapper<PermRoleResourceDO>().eq(PermRoleResourceDO::getRoleId, id)
                .eq(PermRoleResourceDO::getResourceType, ResourceTypeEnum.KNOWLEDGE_BASE.getCode()));

        // 3. 获取资源ID列表
        List<Long> bookIds = roleResources.stream().map(PermRoleResourceDO::getResourceId).collect(Collectors.toList());

        PermRoleDetailResp resp = new PermRoleDetailResp();
        resp.setId(id);
        resp.setRoleName(role.getRoleName());
        resp.setBookIds(bookIds);
        return resp;
    }

    @Override
    public List<AdminAndRoleAssignResp> putIsPuckedRole(Long roleId,
        List<AdminAndRoleAssignResp> adminAndRoleAssignResps) {
        List<PermUserRoleDO> list =
            permUserRoleService.list(new LambdaQueryWrapper<PermUserRoleDO>().eq(PermUserRoleDO::getRoleId, roleId));

        Set<Long> hasRoleUserIds = list.stream().map(PermUserRoleDO::getUserId).collect(Collectors.toSet());
        return adminAndRoleAssignResps.stream().peek(e -> e.setPicked(hasRoleUserIds.contains(Long.valueOf(e.getId()))))
            .sorted(Comparator.comparing(AdminAndRoleAssignResp::getPicked).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<Long> getUserIdsByRoleId(Long roleId) {
        List<PermUserRoleDO> list =
            permUserRoleService.list(new LambdaQueryWrapper<PermUserRoleDO>().eq(PermUserRoleDO::getRoleId, roleId));
        return list.stream().map(PermUserRoleDO::getUserId).collect(Collectors.toList());
    }

    @Override
    public List<TeamResp> getTeamBooks() {
        Map<Long, List<SimpleTeamBookDTO>> teamIdBooksMap = teamBookDaoService.mapAllDto();
        List<TeamResp> teamResps = CopierUtil.copyList(teamService.list(), TeamResp.class);
        for (TeamResp team : teamResps) {
            List<SimpleTeamBookDTO> allBooks = teamIdBooksMap.get(team.getId());
            team.setChildren(allBooks);
        }
        return teamResps;
    }

    @Override
    public List<PermRoleResp> list(PermRoleReq req) {
        List<PermRoleDO> list = lambdaQuery()
            .like(ObjectUtils.isNotEmpty(req.getRoleName()), PermRoleDO::getRoleName, req.getRoleName()).list();
        List<PermRoleResp> permRoleResps = new ArrayList<>();
        for (PermRoleDO permRoleDO : list) {
            PermRoleResp permRoleResp = new PermRoleResp();
            permRoleResp.setId(permRoleDO.getId());
            permRoleResp.setRoleName(permRoleDO.getRoleName());
            permRoleResp.setCreateTime(DateUtils.format(permRoleDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            permRoleResps.add(permRoleResp);
        }
        return permRoleResps;
    }

}