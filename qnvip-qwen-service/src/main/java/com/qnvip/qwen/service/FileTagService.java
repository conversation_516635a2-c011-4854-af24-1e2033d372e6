package com.qnvip.qwen.service;

import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileTagQueryDTO;
import com.qnvip.qwen.dal.entity.FileTagDO;
import com.qnvip.qwen.vo.request.FileTagReq;
import com.qnvip.qwen.vo.response.FileTagResp;

/**
 * 文件标签表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
public interface FileTagService extends BaseService<FileTagDO> {

    /**
     * 文件标签表分页
     *
     * @param query
     * @return
     */
    Pageable<FileTagResp> page(FileTagQueryDTO query);

    /**
     * 保存文件标签表
     *
     * @param record
     * @return
     */
    Boolean save(FileTagReq record);

    /**
     * 获取文件标签表详情
     *
     * @param id
     * @return
     */
    FileTagResp detail(Long id);

    /**
     * 删除文件标签表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 保存并且删除之前的数据
     * 
     * @param fileOriginId
     * @param tags
     */
    void saveDeleteBefore(Long fileOriginId, List<String> tags);
}
