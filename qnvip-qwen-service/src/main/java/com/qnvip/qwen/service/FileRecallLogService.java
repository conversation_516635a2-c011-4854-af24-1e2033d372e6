package com.qnvip.qwen.service;

import java.util.Collection;
import java.util.List;

import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileChunkRerankDTO;
import com.qnvip.qwen.dal.dto.FileOriginScoreDTO;
import com.qnvip.qwen.dal.dto.FileRecallLogQueryDTO;
import com.qnvip.qwen.dal.dto.HybirdRecallDTO;
import com.qnvip.qwen.dal.dto.IdScoreFileDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.entity.FileRecallLogDO;
import com.qnvip.qwen.enums.RecallFromEnum;
import com.qnvip.qwen.vo.request.DifyUserQuestionReq;
import com.qnvip.qwen.vo.request.DifyUserRecallReq;
import com.qnvip.qwen.vo.request.FileRecallLogReq;
import com.qnvip.qwen.vo.response.FileRecallLogResp;

/**
 * 文件召回日志表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
public interface FileRecallLogService extends BaseService<FileRecallLogDO> {

    /**
     * 文件召回日志表分页
     *
     * @param query
     * @return
     */
    Pageable<FileRecallLogResp> page(FileRecallLogQueryDTO query);

    /**
     * 保存文件召回日志表
     *
     * @param record
     * @return
     */
    Boolean save(FileRecallLogReq record);

    /**
     * 获取文件召回日志表详情
     *
     * @param id
     * @return
     */
    FileRecallLogResp detail(Long id);

    /**
     * 删除文件召回日志表记录
     *
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 导航es召回结果
     *
     * @param req
     * @param questionId
     * @param esFileOriginIds
     * @param navigationEsResult
     */
    void saveNavigationEsRecall(DifyUserQuestionReq req, Long questionId, List<Long> esFileOriginIds,
        RecallFromEnum navigationEsResult);

    /**
     * 导航milvus召回结果
     *
     * @param req
     * @param questionId
     * @param milvusFileOriginIds
     * @param navigationMilvus
     */
    void saveNavigationMilvusRecall(DifyUserRecallReq req, Long questionId, List<Long> milvusFileOriginIds,
        RecallFromEnum navigationMilvus);

    /**
     * 多路召回es切块
     * 
     * @param userId
     * @param questionId
     * @param chunks
     * @param multEs
     */
    void saveMultChunkEsRecall(DifyUserRecallReq userId, Long questionId, List<IdScoreFileDTO<String>> chunks,
        RecallFromEnum multEs);

    /**
     * 知识图谱召回
     * 
     * @param userId
     * @param questionId
     * @param chunks
     * @param multEs
     */
    void saveMultChunkGraphRecall(DifyUserRecallReq userId, Long questionId, Collection<Long> fileIds,
        List<String> chunkUids, RecallFromEnum multEs);

    /**
     * 多路召回切块milvus
     *
     * @param req
     * @param questionId
     * @param chunks
     * @param multEs
     */
    void saveMultRecallChunkMilvusRecall(DifyUserRecallReq req, Long questionId, List<FileQaDO> chunks,
        RecallFromEnum multEs);

    void saveMultRecallMilvusRecall(DifyUserRecallReq req, Long questionId, List<FileChunkDataDO> chunks,
        RecallFromEnum multEs);


    void saveChunkMergeRecall(DifyUserRecallReq req, Long questionId, List<FileOriginScoreDTO> originChunksDTOS,
        RecallFromEnum multSurround);

    void saveRerankChunkRecall(DifyUserRecallReq req, Long questionId, List<FileChunkRerankDTO> chunkRerankDTOS,
        RecallFromEnum multRerank);


    void saveStatic(DifyUserRecallReq req, HybirdRecallDTO hybirdRecallDTO, RecallFromEnum recallFromEnum);
}
