package com.qnvip.qwen.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.orm.util.MybatisPlusUtils;
import com.qnvip.qwen.dal.dao.PromptRoleCategoryDaoService;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryDTO;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryQueryDTO;
import com.qnvip.qwen.dal.entity.PromptRoleCategoryDO;
import com.qnvip.qwen.dal.mapper.PromptRoleCategoryMapper;
import com.qnvip.qwen.service.PromptRoleCategoryService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.PromptRoleCategoryReq;
import com.qnvip.qwen.vo.response.PromptRoleCategoryResp;

/**
 * 角色提示词分类表服务
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
@Service
public class PromptRoleCategoryServiceImpl extends BaseServiceImpl<PromptRoleCategoryMapper, PromptRoleCategoryDO>
    implements PromptRoleCategoryService {

    @Resource
    private PromptRoleCategoryMapper promptRoleCategoryMapper;

    @Resource
    private PromptRoleCategoryDaoService promptRoleCategoryDaoService;

    @Override
    public Pageable<PromptRoleCategoryResp> page(PromptRoleCategoryQueryDTO query) {
        if (query == null) {
            return Pageable.of(0, new ArrayList<>());
        }

        IPage<PromptRoleCategoryDTO> page = lambdaQuery().setEntity(CopierUtil.copy(query, PromptRoleCategoryDO.class))
            .page(MybatisPlusUtils.convertFrom(query));
        return MybatisPlusUtils.convertFrom(page.convert(item -> CopierUtil.copy(item, PromptRoleCategoryResp.class)));
    }

    @Override
    public List<PromptRoleCategoryResp> list(PromptRoleCategoryReq query) {
        PromptRoleCategoryDO filter = CopierUtil.copy(query, PromptRoleCategoryDO.class);
        List<PromptRoleCategoryDO> list = lambdaQuery().setEntity(filter).last("limit 500").list();
        return CopierUtil.copyList(list, PromptRoleCategoryResp.class);
    }

    @Override
    public Boolean save(PromptRoleCategoryReq record) {
        return saveOrUpdate(CopierUtil.copy(record, PromptRoleCategoryDO.class));
    }

    @Override
    public PromptRoleCategoryResp detail(Long id) {
        PromptRoleCategoryDO record = get(PromptRoleCategoryDO::getId, id);
        if (record == null) {
            return null;
        }

        return CopierUtil.copy(record, PromptRoleCategoryResp.class);
    }

    @Override
    public Boolean delete(Long id) {
        return remove(PromptRoleCategoryDO::getId, id);
    }
}