package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件任务进度的产出数据日志表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
@ApiModel("文件任务进度的产出数据日志表")
@Data
public class FileTaskProgressDatalogResp {

    @ApiModelProperty("任务进度Id")
    private Long id;

    @ApiModelProperty("任务步骤id")
    private Long taskProgressId;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("数据")
    private String data;

}