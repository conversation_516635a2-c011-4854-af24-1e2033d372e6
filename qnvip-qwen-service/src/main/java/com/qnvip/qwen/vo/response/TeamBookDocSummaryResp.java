package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@ApiModel("文档表")
@Data
public class TeamBookDocSummaryResp {

    @ApiModelProperty("知识库Id")
    private Long bookId;
    @ApiModelProperty("文章id")
    private Long fileOriginId;

    @ApiModelProperty("类型 1成功 2失败（提示错误原因）")
    private Integer status;

    @ApiModelProperty("状态异常原因")
    private String statusErrMsg;

    @ApiModelProperty("替换key")
    private String replaceKey;

    @ApiModelProperty("替换值")
    private String replaceValue;

}