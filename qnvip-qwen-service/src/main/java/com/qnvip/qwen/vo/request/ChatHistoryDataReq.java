package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@ApiModel("对话记录表")
@Data
public class ChatHistoryDataReq {

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("问题Id")
    private Long chatHistoryId;

    @ApiModelProperty("内容")
    private String data;

}