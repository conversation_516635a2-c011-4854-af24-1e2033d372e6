package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件概括表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@ApiModel("文件概括表")
@Data
public class FileSummaryReq {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("文件Id")
    private Long originId;

    @ApiModelProperty("标签Id")
    private String data;

}