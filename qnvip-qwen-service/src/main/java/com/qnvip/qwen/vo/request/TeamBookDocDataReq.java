package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
@ApiModel("文档表")
@Data
public class TeamBookDocDataReq {

    @ApiModelProperty("文档Id")
    private Long id;

    @ApiModelProperty("所属团队Id")
    private Long docId;

    @ApiModelProperty("文章或者目录名字")
    private String data;

}