package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 知识库表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@ApiModel("知识库表")
@Data
public class TeamBookResp {

    @ApiModelProperty("知识库Id")
    private Long id;

    @ApiModelProperty("团队Id")
    private Long teamId;

    @ApiModelProperty("三方唯一标识")
    private String mark;

    @ApiModelProperty("知识库名称")
    private String name;

    @ApiModelProperty("")
    private String description;

}