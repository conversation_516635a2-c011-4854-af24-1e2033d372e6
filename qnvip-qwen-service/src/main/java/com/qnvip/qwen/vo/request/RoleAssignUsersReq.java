package com.qnvip.qwen.vo.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色分配用户请求模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Data
@ApiModel("角色分配用户请求模型")
public class RoleAssignUsersReq {

    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("用户ID列表")
    private List<Long> userIds;
}