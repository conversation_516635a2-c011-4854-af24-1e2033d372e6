package com.qnvip.qwen.vo.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件切块表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
@ApiModel("文件切块表")
@Data
public class FileChunkResp {



    @ApiModelProperty("唯一标识")
    private String uid;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

  @ApiModelProperty("翻译文件Id")
  private Long fileTranslateId;

  @ApiModelProperty("排序")
  private Integer sort;

}