package com.qnvip.qwen.vo.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件切块数据表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@ApiModel("文件切块数据表")
@Data
public class FileChunkDataResp {

    @ApiModelProperty("切块ID")
    private Long id;

    @ApiModelProperty("文件切块唯一标识")
    private String chunkUid;

  @ApiModelProperty("文件切块内容")
  private String data;

}