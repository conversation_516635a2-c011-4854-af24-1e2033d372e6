package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户token用量配置表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
@ApiModel("用户token用量配置表")
@Data
public class UserTokenConfigReq {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("权限授权token")
    private Long permissionAuthorizedToken;

    @ApiModelProperty("月度已使用的token")
    private Long usedToken;

}