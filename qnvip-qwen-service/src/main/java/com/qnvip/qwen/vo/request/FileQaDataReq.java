package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件问答数据表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@ApiModel("文件问答数据表")
@Data
public class FileQaDataReq {

    @ApiModelProperty("问答数据Id")
    private Long id;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("问答标识")
    private String qaUid;

    @ApiModelProperty("问题内容")
    private String question;

    @ApiModelProperty("答案内容")
    private String answer;

}