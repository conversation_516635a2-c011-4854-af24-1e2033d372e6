package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@ApiModel("角色表")
@Data
public class PermRoleResp {

    @ApiModelProperty("角色Id")
    private Long id;

    @ApiModelProperty("角色名字")
    private String roleName;

    @ApiModelProperty("是否选中")
    private Boolean picked;

    private String createTime;

}