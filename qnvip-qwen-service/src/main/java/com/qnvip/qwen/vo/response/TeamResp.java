package com.qnvip.qwen.vo.response;

import java.util.List;

import com.qnvip.qwen.dal.dto.SimpleTeamBookDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 团队表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@ApiModel("团队表")
@Data
public class TeamResp {

    @ApiModelProperty("团队Id")
    private Long id;

    @ApiModelProperty("团队名称")
    private String name;

    @ApiModelProperty("知识库列表")
    private List<SimpleTeamBookDTO> children;

}