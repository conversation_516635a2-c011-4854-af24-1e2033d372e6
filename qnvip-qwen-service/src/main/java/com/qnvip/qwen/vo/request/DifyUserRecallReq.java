package com.qnvip.qwen.vo.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("dify数据召回表")
@Data
public class DifyUserRecallReq {

    @ApiModelProperty("用户id")
    @NotNull
    private Long userId;

    @ApiModelProperty("问题id")
    @NotNull
    private Long questionId;

    @ApiModelProperty("用户问题")
    @NotNull
    private String userInput;
    private List<Long> fileOriginIds;

    @ApiModelProperty("召回类型")
    private String recallType;

    @ApiModelProperty("知识库id的集合 可能为空")
    private List<Long> bookIds;

    @ApiModelProperty("工作流运行id")
    private String workflowRunId;

    @ApiModelProperty("多路召回配置")
    private MultiRouteRecallConfig recallConfig;

    @ApiModelProperty("排除的问题id的集合")
    private List<String> excludedChunkIds;

}
