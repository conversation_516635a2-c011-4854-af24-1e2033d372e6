package com.qnvip.qwen.vo.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户角色和Token配置请求对象
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@ApiModel("用户角色和Token配置请求")
@Data
public class UserRoleTokenReq {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("授权Token数量")
    private Long permissionAuthorizedToken;

    @ApiModelProperty("角色ID列表")
    private List<Long> roleIds;
}