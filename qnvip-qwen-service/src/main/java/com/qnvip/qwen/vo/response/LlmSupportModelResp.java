package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支持的模型表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
@ApiModel("支持的模型表")
@Data
public class LlmSupportModelResp {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("模型名称")
    private String name;

    @ApiModelProperty("模型图标")
    private String icon;

    @ApiModelProperty("模型描述")
    private String description;

}