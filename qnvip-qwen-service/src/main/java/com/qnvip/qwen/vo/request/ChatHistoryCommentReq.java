package com.qnvip.qwen.vo.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 聊天记录评论表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@ApiModel("聊天记录评论表")
@Data
public class ChatHistoryCommentReq {

    @ApiModelProperty(value = "用户Id", hidden = true)
    private Long userId;

    @ApiModelProperty("会话Id")
    private String conversationId;



    @ApiModelProperty("模型id")
    private Integer modelId;

    @ApiModelProperty("消息id")
    private String messageId;

    @ApiModelProperty("评价标签")
    private List<String> evaluateTags;

    @ApiModelProperty("是否点赞，1:赞，0:否 -1踩")
    private Integer likeFlag;

    @ApiModelProperty("评论内容")
    private String content;

}