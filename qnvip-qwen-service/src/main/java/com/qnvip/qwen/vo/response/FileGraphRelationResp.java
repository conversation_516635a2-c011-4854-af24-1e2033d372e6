package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件图数据表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
@ApiModel("文件图数据表")
@Data
public class FileGraphRelationResp {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("文件块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("知识库id")
    private Long bookId;

    @ApiModelProperty("源实体")
    private String source;

    @ApiModelProperty("目标实体")
    private String target;

    @ApiModelProperty("关系词")
    private String keyword;

    @ApiModelProperty("关系强度")
    private Integer strength;

    @ApiModelProperty("关系强度")
    private String description;

}