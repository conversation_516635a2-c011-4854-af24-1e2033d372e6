package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 团队表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@ApiModel("模型")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMUrlTokenResp {

    @ApiModelProperty("模型id")
    private Long modelId;

    @ApiModelProperty("请求地址")
    private String url;

    @ApiModelProperty("Authorization鉴权 Bearer 卡头")
    private String apiKey;

    @ApiModelProperty("类型 chat , inspiration")
    private String type;



}