package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@ApiModel("对话记录表")
@Data
public class ChatHistoryResp {

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("问题Id")
    private Long questionId;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("是否来自某一方（1:是，0:否）")
    private Integer fromUser;

    @ApiModelProperty("机器人Id")
    private Long agentId;

}