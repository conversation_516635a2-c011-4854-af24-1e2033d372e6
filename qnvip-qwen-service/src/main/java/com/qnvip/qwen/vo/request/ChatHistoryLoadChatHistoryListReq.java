package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@ApiModel("对话记录表")
@Data
public class ChatHistoryLoadChatHistoryListReq {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("limit")
    private Long limit = 10L;

    @ApiModelProperty("agentId 青优千问1")
    private Long agentId;

}