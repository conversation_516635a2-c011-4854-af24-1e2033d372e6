package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色提示词模板表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@ApiModel("角色提示词模板表")
@Data
public class PromptRoleTemplateReq {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("分类Id")
    private Long categoryId;

    @ApiModelProperty("是否公共标志，1为公共，0为个人")
    private Integer publicFlag = 1;

    @ApiModelProperty("创建者用户Id，0为系统")
    private Long creatorUserId = 0L;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("内容")
    private String content;

}