package com.qnvip.qwen.vo.response;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class AdminAndTokenInfoResp {

    @ApiModelProperty("ID")
    private Integer id;
    @ApiModelProperty("名字")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("已用token数")
    private Long usedToken;
    @ApiModelProperty("总token数")
    private Long permissionAuthorizedToken;
    @ApiModelProperty("角色")
    private List<PermRoleResp> roles;
    @ApiModelProperty("角色的名字")
    private String rolesNameStr;

}
