package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色提示词模板表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@ApiModel("角色提示词模板表")
@Data
public class PromptRoleTemplateResp {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("分类Id")
    private Long categoryId;

    @ApiModelProperty("分类名字")
    private String categoryName;

    @ApiModelProperty("分类类型  筛选时空为全部 0通用 1内部（勾选内部知识库） 2外部（取消勾选内部知识库）")
    private Integer categoryType;

    @ApiModelProperty("是否公共标志，1为公共，0为个人")
    private Integer publicFlag;

    @ApiModelProperty("创建者用户Id，0为系统")
    private Long creatorUserId;


    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("示例输入数据")
    private String exampleInput;

    @ApiModelProperty("示例输出数据")
    private String exampleOutput;

}