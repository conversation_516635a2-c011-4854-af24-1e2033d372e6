package com.qnvip.qwen.vo.response;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色详情响应对象
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@ApiModel("角色详情")
@Data
public class PermRoleDetailResp {

    @ApiModelProperty("角色ID")
    private Long id;

    private String roleName;

    @ApiModelProperty("团队")
    private List<Long> bookIds;
}