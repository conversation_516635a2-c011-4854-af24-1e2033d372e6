package com.qnvip.qwen.vo.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色分配知识库请求模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Data
@ApiModel("角色分配知识库请求模型")
public class RoleAssignBooksReq {

    @ApiModelProperty("角色ID")
    private Long id;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("知识库ID列表")
    private List<Long> bookIds;
}