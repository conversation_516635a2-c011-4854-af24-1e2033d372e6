package com.qnvip.qwen.vo.request;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件任务进度表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
@ApiModel("文件任务进度表")
@Data
public class FileTaskProgressReq {

    @ApiModelProperty("任务进度Id")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70图谱结构化 80完结")
    private Integer nowStep;

    @ApiModelProperty("状态 0待开始 1进行中 2已结束 3异常")
    private Integer status;

    @ApiModelProperty("处理开始时间")
    private LocalDateTime processStartTime;

    @ApiModelProperty("处理结束时间")
    private LocalDateTime processFinishTime;

    @ApiModelProperty("重试次数")
    private Integer retryTimes;

    @ApiModelProperty("错误发生时间")
    private LocalDateTime errTime;

    @ApiModelProperty("错误信息")
    private String errMsg;

}