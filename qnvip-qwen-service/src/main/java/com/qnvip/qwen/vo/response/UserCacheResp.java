package com.qnvip.qwen.vo.response;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户缓存表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@ApiModel("用户缓存表")
@Data
public class UserCacheResp {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("模型ID")
    private Long modelId;

    @ApiModelProperty("@name:是否使用知识库, @dicts:[1-是,0-否]")
    private Integer useBookFlag;

    @ApiModelProperty("@name:是否使用联网搜索, @dicts:[1-是,0-否]")
    private Integer useSearchFlag;

    @ApiModelProperty("@name:是否使用深度搜索, @dicts:[1-是,0-否]")
    private Integer useDeepSearchFlag;

    @ApiModelProperty("选中的知识库")
    private List<Long> bookIds;

}