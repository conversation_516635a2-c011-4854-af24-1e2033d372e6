package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色资源表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@ApiModel("角色资源表")
@Data
public class PermRoleResourceResp {

    @ApiModelProperty("自增主键Id")
    private Long id;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("资源id -1全部权限")
    private Long resourceId;

    @ApiModelProperty("@name:资源类型, @dicts:[1-知识库资源]")
    private Integer resourceType;

}