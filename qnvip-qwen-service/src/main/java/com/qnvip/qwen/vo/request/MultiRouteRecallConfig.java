package com.qnvip.qwen.vo.request;

import java.math.BigDecimal;

import com.qnvip.qwen.dal.dto.FilterOptionsDTO;

import lombok.Data;

@Data
public class MultiRouteRecallConfig {




    /**
     * chunk全文检索过滤条件
     */
    private FilterOptionsDTO chunkFullText;

    /**
     * chunk向量检索过滤条件
     */
    private FilterOptionsDTO qaVector;


    /**
     * 知识图谱-向量
     */
    private FilterOptionsDTO graphVectorFilter;

    /**
     * 知识图谱-切块个数
     */
    private FilterOptionsDTO graphChunkIdFilter;

    /**
     * 重排序chunk过滤条件
     */
    private FilterOptionsDTO rerankChunk;


    /**
     * 返回最多文档数
     */
    private Integer docTopN;

    /**
     * 关键字权重
     */
    private BigDecimal tkweight;

    /**
     * 向量权重
     */
    private BigDecimal vtweight;

}
