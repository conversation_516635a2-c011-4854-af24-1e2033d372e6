package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@ApiModel("文档表")
@Data
public class TeamBookDocReq {

    @ApiModelProperty("文档Id")
    private Long id;

    @ApiModelProperty("所属团队Id")
    private Long teamId;

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("知识库Id")
    private Long bookId;

    @ApiModelProperty("类型 1文章 2目录")
    private Integer type;

    @ApiModelProperty("三方唯一标识")
    private String mark;

    @ApiModelProperty("文章或者目录名字")
    private String name;

}