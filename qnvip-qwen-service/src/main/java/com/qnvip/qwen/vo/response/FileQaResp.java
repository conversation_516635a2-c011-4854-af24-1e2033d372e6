package com.qnvip.qwen.vo.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档QA表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@ApiModel("文档QA表")
@Data
public class FileQaResp {

    @ApiModelProperty("文档QA Id")
    private Long id;

    @ApiModelProperty("唯一标识")
    private String uid;

  @ApiModelProperty("原文档Id")
  private Long fileOriginId;

  @ApiModelProperty("翻译文档Id")
  private Long fileTranslateId;

  @ApiModelProperty("文档分块Id")
  private Long fileChunkId;

}