package com.qnvip.qwen.vo.response;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.qnvip.qwen.enums.BooleanEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@ApiModel("对话记录表")
@Data
public class ChatHistoryChatContextResp {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String TEMPLATE_USER = "用户 {time}：";
    private static final String TEMPLATE_AGENT = "你 {time}：";

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("是否来自某一方（1:是，0:否）")
    private Integer fromUser;

    @ApiModelProperty("消息体")
    private String data;

    @ApiModelProperty("机器人Id")
    private Long agentId;

    private LocalDateTime createTime;

    public String toChatContext(boolean withTime) {
        if (fromUser == BooleanEnum.TRUE.getValue()) {
            return wrapWithTime(TEMPLATE_USER, data, withTime);
        } else {
            return wrapWithTime(TEMPLATE_AGENT, data, withTime);
        }
    }

    private String wrapWithTime(String template, String data, boolean withTime) {
        String time = "";
        if (withTime) {
            time = createTime.format(FORMATTER);
        }
        template = template.replace("{time}", time);
        return template + data;
    }

}