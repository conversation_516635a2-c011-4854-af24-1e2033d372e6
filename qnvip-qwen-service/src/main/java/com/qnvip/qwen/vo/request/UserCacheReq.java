package com.qnvip.qwen.vo.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户缓存表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@ApiModel("用户缓存表")
@Data
public class UserCacheReq {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("模型ID")
    @NotNull
    private Long modelId;

    @ApiModelProperty("@name:是否使用知识库, @dicts:[1-是,0-否]")
    @NotNull
    private Integer useBookFlag;

    @ApiModelProperty("@name:是否使用联网搜索, @dicts:[1-是,0-否]")
    @NotNull
    private Integer useSearchFlag;

    @ApiModelProperty("@name:是否使用深度搜索, @dicts:[1-是,0-否]")
    @NotNull
    private Integer useDeepSearchFlag;

    @ApiModelProperty("知识库ID列表字符串")
    private List<Integer> bookIds;

}