package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * dify应用的apikey表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
@ApiModel("dify应用的apikey表")
@Data
public class DifyAppApikeyResp {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("difyApiKey")
    private String apiKey;

    @ApiModelProperty("模型id")
    private Long modelId;

    @ApiModelProperty("url")
    private String url;

    @ApiModelProperty("@name:类型, @dicts:[chat]")
    private Integer type;

}