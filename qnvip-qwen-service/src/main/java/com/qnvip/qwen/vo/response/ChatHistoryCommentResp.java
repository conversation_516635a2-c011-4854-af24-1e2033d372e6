package com.qnvip.qwen.vo.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 聊天记录评论表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@ApiModel("聊天记录评论表")
@Data
public class ChatHistoryCommentResp {

    @ApiModelProperty("评论Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("会话Id")
    private String conversationId;



    @ApiModelProperty("是否点赞，1:是，0:否")
    private Integer likeFlag;

    @ApiModelProperty(value = "评论标签", hidden = true)
    @JsonIgnore
    private String evaluateTag;

    @ApiModelProperty("评价标签")
    private List<LLMEvaluateResp> evaluateTags;

    @ApiModelProperty("评论内容")
    private String content;

}