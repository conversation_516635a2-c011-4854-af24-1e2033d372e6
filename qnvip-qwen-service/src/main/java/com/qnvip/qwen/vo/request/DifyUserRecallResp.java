package com.qnvip.qwen.vo.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("dify数据召回表")
@Data
public class DifyUserRecallResp {

    private List<OriginFile> originFiles;

    @Data
    public static class OriginFile {
        @ApiModelProperty("命中的文件id")
        private Long fileOriginId;
        @ApiModelProperty("命中的文件url")
        private String targetOriginUrl;
        @ApiModelProperty("命中的文件目录")
        private String tocs;
        @ApiModelProperty("命中的文件名字")
        private String fileName;
        @ApiModelProperty("切块")
        private List<String> chunks;
    }
}
