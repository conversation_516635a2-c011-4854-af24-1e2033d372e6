package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@ApiModel("对话记录表")
@Data
public class ChatHistoryReq {

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("是否来自某一方（1:是，0:否）")
    private Integer fromUser;

    @ApiModelProperty("机器人Id")
    private Long agentId;

    @ApiModelProperty("问题")
    private String content;

    @ApiModelProperty("工作流运行id")
    private String workflowRunId;

    @ApiModelProperty("true 使用知识库")
    private String useBook;

    @ApiModelProperty("true 使用搜索")
    private String useSearch;



}