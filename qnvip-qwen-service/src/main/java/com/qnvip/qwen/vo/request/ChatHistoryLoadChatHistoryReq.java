package com.qnvip.qwen.vo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表输入模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@ApiModel("对话记录表")
@Data
public class ChatHistoryLoadChatHistoryReq {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("是否来自某一方（1:是，0:否）")
    private Integer fromUser;

    @ApiModelProperty("是否带时间")
    private boolean withTime = false;

    @ApiModelProperty("limit")
    private Long limit = 50L;

}