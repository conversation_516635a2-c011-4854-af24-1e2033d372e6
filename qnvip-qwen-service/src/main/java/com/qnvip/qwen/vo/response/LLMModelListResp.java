package com.qnvip.qwen.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 团队表 输出模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@ApiModel("模型")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMModelListResp {

    @ApiModelProperty("模型id")
    private Integer id;

    @ApiModelProperty("模型名字")
    private String name;

    @ApiModelProperty("模型描述")
    private String description;

    @ApiModelProperty("图标")
    private String icon;

}