<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qnvip</groupId>
        <artifactId>qnvip-qwen</artifactId>
        <version>${project.version}</version>
    </parent>

    <artifactId>qnvip-qwen-acl</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-qwen-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.qnvip</groupId>-->
        <!--            <artifactId>qnvip-qwen-client</artifactId>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-orm-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.4.11</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-neo4j</artifactId>
        </dependency>


    </dependencies>


</project>
