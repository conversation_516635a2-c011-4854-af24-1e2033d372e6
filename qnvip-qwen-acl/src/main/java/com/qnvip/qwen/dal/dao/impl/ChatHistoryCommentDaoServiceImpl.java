package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.ChatHistoryCommentDaoService;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryCommentDO;
import com.qnvip.qwen.dal.mapper.ChatHistoryCommentMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 聊天记录评论表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatHistoryCommentDaoServiceImpl extends BaseServiceImpl<ChatHistoryCommentMapper, ChatHistoryCommentDO>
    implements ChatHistoryCommentDaoService {
    @Resource
    private ChatHistoryCommentMapper chatHistoryCommentMapper;

    @Override
    public IPage<ChatHistoryCommentDTO> page(IPage<ChatHistoryCommentDO> page, ChatHistoryCommentQueryDTO query) {
        IPage<ChatHistoryCommentDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, ChatHistoryCommentDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, ChatHistoryCommentDTO.class));
    }
}
