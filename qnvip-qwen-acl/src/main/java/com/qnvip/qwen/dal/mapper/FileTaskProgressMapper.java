package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;

/**
 * 文件任务进度表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
@Mapper
public interface FileTaskProgressMapper extends CustomBaseMapper<FileTaskProgressDO> {

    @Delete("delete from qw_file_task_progress where book_id = #{bookId} and now_step = #{nowStep} and status = 0 and file_origin_id in (${fileOriginIds})")
    void deletePendingTask(@Param("bookId") Long bookId, @Param("nowStep") Integer nowStep,
        @Param("fileOriginIds") String fileOriginIds);

    @Delete("delete from qw_file_task_progress where file_origin_id in (${fileOriginIds})")
    void deleteTaskByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);

}