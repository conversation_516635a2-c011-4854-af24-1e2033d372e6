package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支持的模型表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
@Data
@TableName("qw_llm_support_model")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LlmSupportModelDO extends BaseDO {

    @ApiModelProperty("主键Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("模型名称")
    private String name;

    @ApiModelProperty("模型图标")
    private String icon;

    @ApiModelProperty("模型描述")
    private String description;
}
