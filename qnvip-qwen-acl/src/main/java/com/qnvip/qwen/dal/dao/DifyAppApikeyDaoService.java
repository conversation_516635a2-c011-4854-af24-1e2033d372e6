package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.DifyAppApikeyDTO;
import com.qnvip.qwen.dal.dto.DifyAppApikeyQueryDTO;
import com.qnvip.qwen.dal.entity.DifyAppApikeyDO;

/**
 * dify应用的apikey表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
public interface DifyAppApikeyDaoService {

    IPage<DifyAppApikeyDTO> page(IPage<DifyAppApikeyDO> page, DifyAppApikeyQueryDTO query);

}
