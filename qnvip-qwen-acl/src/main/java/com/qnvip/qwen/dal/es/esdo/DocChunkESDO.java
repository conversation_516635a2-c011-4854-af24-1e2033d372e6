package com.qnvip.qwen.dal.es.esdo;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月31日 10:07:00
 */
@Data
@Document(indexName = "index_chunk_v3_")
public class DocChunkESDO {

    @Id
    @Field(type = FieldType.Keyword)
    @ApiModelProperty("切块uid")
    private String chunkUId;

    @Field(type = FieldType.Keyword)
    @ApiModelProperty("文档uid")
    private String docId;

    @Field(type = FieldType.Text)
    @ApiModelProperty("切块内容")
    private String content;

    @Field(type = FieldType.Text)
    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("目录")
    @Field(type = FieldType.Text)
    private String tocs;

    @ApiModelProperty("文件标签")
    @Field(type = FieldType.Text)
    private String tags;

    @Field(type = FieldType.Long)
    @ApiModelProperty("文件名")
    private Long fileOriginId;

    @Field(type = FieldType.Long)
    @ApiModelProperty("团队ID")
    private Long teamId;

    @Field(type = FieldType.Long)
    @ApiModelProperty("知识库ID")
    private Long bookId;


}
