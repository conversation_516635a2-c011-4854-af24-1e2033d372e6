package com.qnvip.qwen.dal.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NavigationDomainModel {

    public static final String BOOK = "book";
    public static final String TOC = "toc";
    public static final String TAG = "tag";
    public static final String SUMMARY = "summary";
    public static final String DOC = "doc";

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("名字")
    private String name;
    @ApiModelProperty("类型 book库 toc目录 tag标签 summary概要 doc文档 ")
    private String type;
    @ApiModelProperty("归属的元素")
    private List<NavigationDomainModel> children;

}
