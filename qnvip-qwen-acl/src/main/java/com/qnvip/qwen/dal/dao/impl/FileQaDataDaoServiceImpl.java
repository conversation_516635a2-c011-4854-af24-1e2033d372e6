package com.qnvip.qwen.dal.dao.impl;


import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileQaDataDaoService;
import com.qnvip.qwen.dal.dto.FileQaDataDTO;
import com.qnvip.qwen.dal.dto.FileQaDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileQaDataDO;
import com.qnvip.qwen.dal.mapper.FileQaDataMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 文件问答数据表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileQaDataDaoServiceImpl extends BaseServiceImpl<FileQaDataMapper, FileQaDataDO>
    implements FileQaDataDaoService {
    @Resource
    private FileQaDataMapper fileQaDataMapper;


    @Override
    public IPage<FileQaDataDTO> page(IPage<FileQaDataDO> page, FileQaDataQueryDTO query) {
        IPage<FileQaDataDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileQaDataDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileQaDataDTO.class));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        fileQaDataMapper.deleteByFileOriginId(fileOriginId);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileQaDataMapper.deleteByFileOriginIds(StringUtils.join(fileOriginIds, ","));
    }

}
