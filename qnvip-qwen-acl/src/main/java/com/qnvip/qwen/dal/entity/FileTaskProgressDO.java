package com.qnvip.qwen.dal.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件任务进度表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
@Data
@TableName("qw_file_task_progress")
public class FileTaskProgressDO extends BaseDO {

    @ApiModelProperty("上一任务id")
    private Long beforeProgressId;

    @ApiModelProperty("库id")
    private Long bookId;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70图谱结构化 80完结")
    private Integer nowStep;

    @ApiModelProperty("状态 0待开始 1进行中 2已结束 3异常")
    private Integer status;

    @ApiModelProperty("处理开始时间")
    private LocalDateTime processStartTime;

    @ApiModelProperty("处理结束时间")
    private LocalDateTime processFinishTime;

    @ApiModelProperty("最大重试次数")
    private Integer maxRetryTimes;

    @ApiModelProperty("重试次数")
    private Integer retryTimes;

    @ApiModelProperty("错误发生时间")
    private LocalDateTime errTime;

    @ApiModelProperty("错误信息")
    private String errMsg;

}
