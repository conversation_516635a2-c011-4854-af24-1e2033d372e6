package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * dify应用的apikey表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
@Data
@TableName("qw_dify_app_apikey")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DifyAppApikeyDO extends BaseDO {

    @ApiModelProperty("主键Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("difyApiKey")
    private String apiKey;

    @ApiModelProperty("模型id")
    private Long modelId;

    @ApiModelProperty("url")
    private String url;

    @ApiModelProperty("@name:类型, @dicts:[chat]")
    private String type;

}
