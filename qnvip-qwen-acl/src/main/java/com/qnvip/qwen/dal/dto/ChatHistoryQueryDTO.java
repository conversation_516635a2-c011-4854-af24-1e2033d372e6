package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 对话记录表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("对话记录表")
public class ChatHistoryQueryDTO extends PageParam {

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;



    @ApiModelProperty("机器人Id")
    private Long agentId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("是否来自某一方（1:是，0:否）")
    private Integer fromUser;

    @ApiModelProperty("limit")
    private Long limit;

    @ApiModelProperty("是否带时间")
    private boolean withTime = false;

}
