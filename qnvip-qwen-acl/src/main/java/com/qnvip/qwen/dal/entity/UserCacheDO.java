package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户缓存表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@Data
@TableName("qw_user_cache")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserCacheDO extends BaseDO {

    @ApiModelProperty("主键ID")
    @TableId(type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("模型ID")
    private Long modelId;

    @ApiModelProperty("@name:是否使用知识库, @dicts:[1-是,0-否]")
    private Integer useBookFlag;

    @ApiModelProperty("@name:是否使用联网搜索, @dicts:[1-是,0-否]")
    private Integer useSearchFlag;

    @ApiModelProperty("@name:是否使用深度搜索, @dicts:[1-是,0-否]")
    private Integer useDeepSearchFlag;

    @ApiModelProperty("知识库ID列表字符串")
    private String bookIdsStr;
}
