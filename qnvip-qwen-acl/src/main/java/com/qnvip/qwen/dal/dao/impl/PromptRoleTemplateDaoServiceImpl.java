package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.PromptRoleTemplateDaoService;
import com.qnvip.qwen.dal.entity.PromptRoleTemplateDO;
import com.qnvip.qwen.dal.mapper.PromptRoleTemplateMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色提示词模板表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromptRoleTemplateDaoServiceImpl extends BaseServiceImpl<PromptRoleTemplateMapper, PromptRoleTemplateDO>
    implements PromptRoleTemplateDaoService {
    @Resource
    private PromptRoleTemplateMapper promptRoleTemplateMapper;

}
