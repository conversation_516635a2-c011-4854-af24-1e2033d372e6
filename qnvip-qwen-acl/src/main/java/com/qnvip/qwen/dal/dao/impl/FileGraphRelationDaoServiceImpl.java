package com.qnvip.qwen.dal.dao.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.lark.oapi.core.utils.Sets;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileGraphRelationDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileGraphRelationDTO;
import com.qnvip.qwen.dal.dto.FileGraphRelationQueryDTO;
import com.qnvip.qwen.dal.dto.graph.GraphRelationship;
import com.qnvip.qwen.dal.entity.FileGraphRelationDO;
import com.qnvip.qwen.dal.mapper.FileGraphRelationMapper;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.Lists;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件图数据表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileGraphRelationDaoServiceImpl extends BaseServiceImpl<FileGraphRelationMapper, FileGraphRelationDO>
    implements FileGraphRelationDaoService {
    @Resource
    private FileGraphRelationMapper fileGraphRelationMapper;

    @Override
    public IPage<FileGraphRelationDTO> page(IPage<FileGraphRelationDO> page, FileGraphRelationQueryDTO query) {
        IPage<FileGraphRelationDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, FileGraphRelationDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileGraphRelationDTO.class));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        if (ObjectUtils.isEmpty(fileOriginId)) {
            return;
        }
        lambdaUpdate().eq(FileGraphRelationDO::getFileOriginId, fileOriginId).remove();
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (ObjectUtils.isEmpty(fileOriginIds)) {
            return;
        }
        lambdaUpdate().in(FileGraphRelationDO::getFileOriginId, fileOriginIds).remove();
    }

    @Override
    public List<FileGraphRelationDO> listSimpleBySourceNameOrTargetName(List<String> entityNames) {
        if (ObjectUtils.isEmpty(entityNames)) {
            return new ArrayList<>();
        }

        List<
            FileGraphRelationDO> src =
                lambdaQuery()
                    .select(FileGraphRelationDO::getBookId, FileGraphRelationDO::getFileOriginId,
                        FileGraphRelationDO::getChunkUid, FileGraphRelationDO::getSource,
                        FileGraphRelationDO::getTarget, FileGraphRelationDO::getKeyword)
                    .in(FileGraphRelationDO::getSource, entityNames).list();

        List<
            FileGraphRelationDO> tgt =
                lambdaQuery()
                    .select(FileGraphRelationDO::getBookId, FileGraphRelationDO::getFileOriginId,
                        FileGraphRelationDO::getChunkUid, FileGraphRelationDO::getSource,
                        FileGraphRelationDO::getTarget, FileGraphRelationDO::getKeyword)
                    .in(FileGraphRelationDO::getTarget, entityNames).list();
        src.addAll(tgt);
        return src;
    }

    @Override
    public List<Long> getDistinctOriginId(DataInitSearchDTO param) {
        return lambdaQuery().select(FileGraphRelationDO::getFileOriginId).groupBy(FileGraphRelationDO::getFileOriginId)
            .last(param.getLimitSql()).list().stream().map(FileGraphRelationDO::getFileOriginId)
            .collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> mapEdgeRelationshipStrength(List<Long> bookIds, List<String> excludedChunkIds,
        Collection<String> allEdges) {
        if (ObjectUtils.isEmpty(allEdges)) {
            return Collections.emptyMap();
        }

        return lambdaQuery().select(FileGraphRelationDO::getLinkSortSourceTarget, FileGraphRelationDO::getStrength)
            .in(!ObjectUtils.isEmpty(bookIds), FileGraphRelationDO::getBookId, bookIds)
            .notIn(!ObjectUtils.isEmpty(excludedChunkIds), FileGraphRelationDO::getChunkUid, excludedChunkIds)
            .in(FileGraphRelationDO::getLinkSortSourceTarget, allEdges).list().stream()
            .collect(Collectors.toMap(FileGraphRelationDO::getLinkSortSourceTarget, FileGraphRelationDO::getStrength,
                (k1, k2) -> k1.compareTo(k2) > 0 ? k1 : k2));
    }

    @Override
    public Map<String, GraphRelationship> mapEdgeRelationship(List<Long> bookIds, List<String> excludedChunkIds, Set<String> allEdges) {
        if (ObjectUtils.isEmpty(allEdges)) {
            return Collections.emptyMap();
        }

        // 使用mapper查询数据
        List<FileGraphRelationDO> result = fileGraphRelationMapper.selectTopNByLinkSortSourceTarget(bookIds,
                excludedChunkIds, allEdges);

        // 按linkSortSourceTarget分组，转换为Map<String, FileGraphRelationDO>
        Map<String, List<FileGraphRelationDO>> groupedByLink = result.stream()
                .collect(Collectors.groupingBy(FileGraphRelationDO::getLinkSortSourceTarget));

        // 将分组后的数据转换为Map<String, FileGraphRelationDO>（取每组第一个）
        Map<String, FileGraphRelationDO> fileGraphRelationMap = groupedByLink.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get(0) // 取每组第一个FileGraphRelationDO
                ));

        // 转换为List<GraphRelationship>
        List<GraphRelationship> graphRelationships = result.stream()
                .map(this::convertToGraphRelationship)
                .collect(Collectors.toList());

        // 将List<GraphRelationship>转换为Map<String, GraphRelationship>
        return graphRelationships.stream()
                .collect(Collectors.toMap(
                        rel -> rel.getSourceEntity() + "_" + rel.getTargetEntity(),
                        rel -> rel,
                        (existing, replacement) -> existing // 如果有重复key，保留第一个
                ));
    }

    /**
     * 将FileGraphRelationDO转换为GraphRelationship
     */
    private GraphRelationship convertToGraphRelationship(FileGraphRelationDO item) {
        GraphRelationship relationship = new GraphRelationship();
        relationship.setBookId(Lists.newArrayList(item.getBookId()));
        relationship.setFileOriginId(Lists.newArrayList(item.getFileOriginId()));
        relationship.setFileChunkUid(Lists.newArrayList(item.getChunkUid()));
        relationship.setSourceEntity(item.getSource());
        relationship.setTargetEntity(item.getTarget());
        relationship.setRelationshipStrength(item.getStrength());
        relationship.setRelationshipKeyword(Lists.newArrayList(item.getKeyword()));
        return relationship;
    }

}
