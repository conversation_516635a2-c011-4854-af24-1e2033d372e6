package com.qnvip.qwen.dal.dao.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileChunkDTO;
import com.qnvip.qwen.dal.dto.FileChunkQueryDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.dal.es.dto.EsChunkInitDTO;
import com.qnvip.qwen.dal.mapper.FileChunkMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件切块表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileChunkDaoServiceImpl extends BaseServiceImpl<FileChunkMapper, FileChunkDO>
    implements FileChunkDaoService {
    @Resource
    private FileChunkMapper fileChunkMapper;

    @Override
    public IPage<FileChunkDTO> page(IPage<FileChunkDO> page, FileChunkQueryDTO query) {
        IPage<FileChunkDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileChunkDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileChunkDTO.class));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        fileChunkMapper.deleteByFileOriginId(fileOriginId);
    }

    @Override
    public List<Long> getIdListByUIds(List<String> uIds) {
        if (CollUtil.isEmpty(uIds)) {
            return new ArrayList<>();
        }
        String uIdsStr = "'" + StrUtil.join("','", uIds) + "'";
        return fileChunkMapper.getIdListByUIds(uIdsStr);
    }

    @Override
    public List<String> getSelfAndNearChunkUIdsByUIds(List<String> uIds) {
        if (CollUtil.isEmpty(uIds)) {
            return new ArrayList<>();
        }
        String uIdsStr = "'" + StrUtil.join("','", uIds) + "'";
        return fileChunkMapper.getSelfAndNearChunkUIdsByUIds(uIdsStr);
    }

    @Override
    public List<EsChunkInitDTO> getEsInitList(DataInitSearchDTO param) {
        return fileChunkMapper.getEsInitList(param);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileChunkMapper.deleteByFileOriginIds(StringUtils.join(fileOriginIds, ","));
    }

    @Override
    public List<FileChunkDO> listFileOriginIdByUids(List<Long> bookIds, List<String> chunkUIds) {
        List<FileChunkDO> list = lambdaQuery().select(FileChunkDO::getFileOriginId, FileChunkDO::getUid)
            .in(!ObjectUtils.isEmpty(bookIds), FileChunkDO::getBookId, bookIds)
            .in(FileChunkDO::getUid, chunkUIds).list();
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<RagChunkDTO> getMilvusInitList(List<Long> fileOriginIds) {
        return fileChunkMapper.getMilvusInitList(fileOriginIds);
    }

    @Override
    public List<Long> getDistinctOriginId(DataInitSearchDTO param) {
        return lambdaQuery().select(FileChunkDO::getFileOriginId).eq(FileChunkDO::getIsDeleted, 0)
            .in(!ObjectUtils.isEmpty(param.getBookIds()), FileChunkDO::getFileOriginId, param.getBookIds())
            .groupBy(FileChunkDO::getFileOriginId).last(param.getLimitSql()).list().stream()
            .map(FileChunkDO::getFileOriginId).collect(Collectors.toList());
    }

}
