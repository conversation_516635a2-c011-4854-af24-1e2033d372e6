package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 角色表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("角色表")
@Builder
public class PermRoleDTO extends PageParam {

    @ApiModelProperty("角色Id")
    private Long id;

    @ApiModelProperty("角色名字")
    private String roleName;
}
