package com.qnvip.qwen.dal.dto.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Setter
@Getter
public class AiMessageDTO {

    public static final String TEXT_TYPE = "text";
    public static final String IMG_TYPE = "img";

    public static AiMessageDTOBuilder system() {
        return AiMessageDTO.builder().role("system").type(TEXT_TYPE);
    }

    public static AiMessageDTOBuilder assistant() {
        return AiMessageDTO.builder().role("assistant").type(TEXT_TYPE);
    }

    public static AiMessageDTOBuilder user() {
        return AiMessageDTO.builder().role("user").type(TEXT_TYPE);
    }

    public static AiMessageDTOBuilder userImg() {
        return AiMessageDTO.builder().role("user").type(IMG_TYPE);
    }

    /**
     * 格式 text ,img
     */
    private String type;

    /**
     * role 只支持 system,user,assistant 其一
     */
    private String role;

    /**
     * 内容
     */
    private Object content;
}