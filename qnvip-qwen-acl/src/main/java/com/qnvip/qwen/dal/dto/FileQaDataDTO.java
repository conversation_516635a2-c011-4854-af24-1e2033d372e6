package com.qnvip.qwen.dal.dto;


import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件问答数据表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件问答数据表")
@Builder
public class FileQaDataDTO extends PageParam {

    @ApiModelProperty("问答数据Id")
    private Long id;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("问答标识")
    private String qaUid;

    @ApiModelProperty("问题内容")
    private String question;

    @ApiModelProperty("答案内容")
    private String answer;
}

