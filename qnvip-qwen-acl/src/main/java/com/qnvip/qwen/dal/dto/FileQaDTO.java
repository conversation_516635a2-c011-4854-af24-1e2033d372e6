package com.qnvip.qwen.dal.dto;


import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文档QA表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文档QA表")
@Builder
public class FileQaDTO extends PageParam {

    @ApiModelProperty("文档QA Id")
    private Long id;

    @ApiModelProperty("唯一标识")
    private String uid;

    @ApiModelProperty("原文档Id")
    private Long fileOriginId;

    @ApiModelProperty("翻译文档Id")
    private Long fileTranslateId;

    @ApiModelProperty("文档分块Id")
    private Long fileChunkId;
}

