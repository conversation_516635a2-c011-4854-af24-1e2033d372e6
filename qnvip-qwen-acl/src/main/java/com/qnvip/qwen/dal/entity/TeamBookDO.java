package com.qnvip.qwen.dal.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 知识库表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Data
@TableName("qw_team_book")
public class TeamBookDO extends BaseDO {

    @ApiModelProperty("团队类型 1语雀，2飞书")
    private Integer type;

    @ApiModelProperty("团队Id")
    private Long teamId;

    @ApiModelProperty("团队唯一标识")
    private String teamMark;

    @ApiModelProperty("三方唯一标识")
    private String mark;

    @ApiModelProperty("知识库名称")
    private String name;

    @ApiModelProperty("")
    private String description;

    @ApiModelProperty("内容更新时间")
    private LocalDateTime contentUpdateTime;
}
