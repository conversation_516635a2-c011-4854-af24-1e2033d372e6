package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileChunkDTO;
import com.qnvip.qwen.dal.dto.FileChunkQueryDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.dal.es.dto.EsChunkInitDTO;

/**
 * 文件切块表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
public interface FileChunkDaoService extends BaseService<FileChunkDO> {

    IPage<FileChunkDTO> page(IPage<FileChunkDO> page, FileChunkQueryDTO query);

    void deleteByFileOriginId(Long fileOriginId);

    List<Long> getIdListByUIds(List<String> uIds);

    List<String> getSelfAndNearChunkUIdsByUIds(List<String> uIds);

    List<EsChunkInitDTO> getEsInitList(DataInitSearchDTO param);

    void deleteByFileOriginIds(List<Long> fileOriginIds);

    List<FileChunkDO> listFileOriginIdByUids(List<Long> bookIds, List<String> chunkUIds);

    List<RagChunkDTO> getMilvusInitList(List<Long> fileOriginIds);

    List<Long> getDistinctOriginId(DataInitSearchDTO param);
}
