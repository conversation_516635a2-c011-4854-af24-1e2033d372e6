package com.qnvip.qwen.dal.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileGraphEntityDaoService;
import com.qnvip.qwen.dal.dto.FileGraphEntityDTO;
import com.qnvip.qwen.dal.dto.FileGraphEntityQueryDTO;
import com.qnvip.qwen.dal.entity.FileGraphEntityDO;
import com.qnvip.qwen.dal.mapper.FileGraphEntityMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件图数据表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileGraphEntityDaoServiceImpl extends BaseServiceImpl<FileGraphEntityMapper, FileGraphEntityDO>
    implements FileGraphEntityDaoService {
    @Resource
    private FileGraphEntityMapper fileGraphEntityMapper;

    @Override
    public IPage<FileGraphEntityDTO> page(IPage<FileGraphEntityDO> page, FileGraphEntityQueryDTO query) {
        IPage<FileGraphEntityDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, FileGraphEntityDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileGraphEntityDTO.class));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        if (ObjectUtils.isEmpty(fileOriginId)) {
            return;
        }
        lambdaUpdate().eq(FileGraphEntityDO::getFileOriginId, fileOriginId).remove();
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (ObjectUtils.isEmpty(fileOriginIds)) {
            return;
        }
        lambdaUpdate().in(FileGraphEntityDO::getFileOriginId, fileOriginIds).remove();
    }

    @Override
    public List<FileGraphEntityDO> listByFileId(Long fileId) {
        return lambdaQuery().eq(FileGraphEntityDO::getFileOriginId, fileId).list();
    }

    @Override
    public List<FileGraphEntityDO> listSimpleByEntityName(List<String> entityNames) {
        if (ObjectUtils.isEmpty(entityNames)) {
            return new ArrayList<>();
        }
        return lambdaQuery().select(FileGraphEntityDO::getChunkUid, FileGraphEntityDO::getFileOriginId,
            FileGraphEntityDO::getBookId, FileGraphEntityDO::getName, FileGraphEntityDO::getType)
            .in(FileGraphEntityDO::getName).list();
    }
}
