package com.qnvip.qwen.dal.mapper.sqlProvider;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import com.qnvip.qwen.dal.dto.DataInitSearchDTO;

import cn.hutool.core.collection.CollUtil;

/**
 * 文件切块表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
public class FileChunkSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_file_chunk where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }

    public String esInitList(Map<String, Object> parameter) {
        DataInitSearchDTO param = (DataInitSearchDTO)parameter.get("param");

        StringBuilder sql = new StringBuilder();
        sql.append("select c.uid as chunkUId,c.file_origin_id as fileOriginId,cd.data as content,"
            + " f.doc_uid as docId,f.file_name as fileName,f.tocs,f.book_id as bookId \n"
            + " from qw_file_chunk c\n" + " left join qw_file_chunk_data cd on cd.chunk_uid=c.uid \n"
            + " left join qw_file_origin f on f.id = c.file_origin_id \n"
        );
        sql.append(" where 1=1 and c.is_deleted = 0 and cd.is_deleted = 0 and f.is_deleted = 0 ");


        if (CollUtil.isNotEmpty(param.getBookIds())) {
            sql.append(" and f.book_id  in (").append(StringUtils.join(param.getBookIds(), ",")).append(") ");
        }
        sql.append(param.getLimitSql());
        return sql.toString();
    }

    public String getMilvusInitList(Map<String, Object> parameter) {
        List<Long> param = (List<Long>)parameter.get("fileOriginIds");

        StringBuilder sql = new StringBuilder();
        sql.append(
            "select c.uid as chunkUId,c.file_origin_id as fileOriginId,cd.data as chunk, f.doc_uid as docId,f.file_name as fileName,f.tocs,f.book_id as bookId \n"
                + " from qw_file_chunk c\n" + " left join qw_file_chunk_data cd on cd.chunk_uid=c.uid \n"
                + " left join qw_file_origin f on f.id = c.file_origin_id \n");
        sql.append(" where  c.is_deleted = 0 and cd.is_deleted = 0 and f.is_deleted = 0 ");
        sql.append(" and f.id  in (").append(StringUtils.join(param, ",")).append(") ");
        return sql.toString();
    }


}
