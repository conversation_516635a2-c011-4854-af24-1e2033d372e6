package com.qnvip.qwen.dal.mapper.sqlProvider;

import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 用户token用量配置表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
public class UserTokenConfigSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_user_token_config where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
