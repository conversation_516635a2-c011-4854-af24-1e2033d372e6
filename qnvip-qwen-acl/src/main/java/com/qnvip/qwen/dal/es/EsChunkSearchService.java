package com.qnvip.qwen.dal.es;

import java.io.IOException;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.dal.dto.IdScoreFileDTO;
import com.qnvip.qwen.dal.es.dto.EsChunkSearchDTO;
import com.qnvip.qwen.dal.es.esdo.DocChunkESDO;
import com.qnvip.qwen.es.EsClientConfig;
import com.qnvip.qwen.util.rag.LanguageProcessUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月31日 10:00:00
 */
@Slf4j
@Service
@DependsOn("esClientConfig")
public class EsChunkSearchService {


    private final RestHighLevelClient client = EsClientConfig.rebuildByException(null, null, this.getClass());


    /**
     * 多条件文档搜索
     *
     * @return SearchResponse 按相关性评分排序的前100条结果
     */
    public List<IdScoreFileDTO<String>> searchRelevantDocuments(EsChunkSearchDTO searchDTO) {
        // 构建索引名称模式
        String[] indices = buildIndices(searchDTO.getBookIds());

        // 构建复合查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 分词
        List<String> queryTks = LanguageProcessUtil.getEsQuestion(searchDTO.getUserInput());

        // 同义词
        List<String> synonyms = LanguageProcessUtil.synonyms(queryTks, 3);

        buildMultiMatchQuery(boolQuery, queryTks, synonyms);
        if (ObjectUtils.isNotEmpty(searchDTO.getExcludedChunkIds())) {
            boolQuery.mustNot(QueryBuilders.termsQuery("chunkUId", searchDTO.getExcludedChunkIds()));
        }

        if (ObjectUtils.isNotEmpty(searchDTO.getFileOriginIds())) {
            boolQuery.must(QueryBuilders.termsQuery("fileOriginId", searchDTO.getFileOriginIds()));
        }

        // 构建搜索请求
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(boolQuery)
            .size(searchDTO.getChunkFullText().getTopN());

        SearchRequest searchRequest = new SearchRequest(indices).source(sourceBuilder)
            .indicesOptions(new IndicesOptions(EnumSet.of(IndicesOptions.Option.ALLOW_NO_INDICES,
                IndicesOptions.Option.IGNORE_UNAVAILABLE, IndicesOptions.Option.IGNORE_THROTTLED),
                EnumSet.of(IndicesOptions.WildcardStates.OPEN)));

        // 执行查询
        SearchResponse response = null;
        try {
            log.info("es search {}", searchRequest);
            response = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (!Objects.equals(response.status(), RestStatus.OK)) {
            log.info("调用es失败，返回数据：{}", response);
            throw FrameworkException.instance("调用es请求失败");
        }

        List<IdScoreFileDTO<String>> resultChunks = new ArrayList<>();
        for (int i = 0; i < response.getHits().getHits().length; i++) {
            DocChunkESDO docChunkESDO =
                JSON.parseObject(response.getHits().getHits()[i].getSourceAsString(), DocChunkESDO.class);
            IdScoreFileDTO<String> dto = new IdScoreFileDTO<>();
            dto.setId(docChunkESDO.getChunkUId());
            dto.setFileOriginId(docChunkESDO.getFileOriginId());
            dto.setFileOriginId(docChunkESDO.getFileOriginId());
            resultChunks.add(dto);
        }

        return resultChunks;

    }


    /**
     * 构建多字段匹配查询
     * 
     * <pre>
     *     关键词匹配优先级规则
     *     1. 100%命中
     *     2. 75%命中
     *     3. 50%命中
     *     4. 25%命中
     *     5. 有命中
     *     6. 近义词命中
     * </pre>
     *
     */
    private void buildMultiMatchQuery(BoolQueryBuilder boolQuery, List<String> segment, List<String> synonyms) {
        synonyms.removeAll(segment);
        String segmentText = String.join(" ", segment);
        String synonymsText = String.join(" ", synonyms);

        // First should clause - bool with must (AND operator) and boost 3
        MultiMatchQueryBuilder firstBoolQuery =
            QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f).field("tocs", 5.0f)
                .type(MultiMatchQueryBuilder.Type.CROSS_FIELDS).minimumShouldMatch("100%").boost(6.0f);

        // Second should clause - multi_match with minimum_should_match 75% and boost 2
        MultiMatchQueryBuilder secondQuery =
            QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f).field("tocs", 5.0f)
                .type(MultiMatchQueryBuilder.Type.CROSS_FIELDS).minimumShouldMatch("75%").boost(5.0f);

        MultiMatchQueryBuilder thirdQuery =
            QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f).field("tocs", 5.0f)
                .type(MultiMatchQueryBuilder.Type.CROSS_FIELDS).minimumShouldMatch("50%").boost(4.0f);

        MultiMatchQueryBuilder fourthQuery =
            QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f).field("tocs", 5.0f)
                .type(MultiMatchQueryBuilder.Type.CROSS_FIELDS).minimumShouldMatch("25%").boost(3.0f);

        MultiMatchQueryBuilder fifthQuery =
            QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f).field("tocs", 5.0f)
                .type(MultiMatchQueryBuilder.Type.CROSS_FIELDS).operator(Operator.OR).boost(2.0f);

        MultiMatchQueryBuilder sixthQuery =
            QueryBuilders.multiMatchQuery(synonymsText).field("content", 0.12f).field("tags", 0.12f)
                .field("tocs", 0.15f).type(MultiMatchQueryBuilder.Type.CROSS_FIELDS).operator(Operator.OR).boost(1.0f);

        boolQuery.should(firstBoolQuery).should(secondQuery).should(thirdQuery).should(fourthQuery).should(fifthQuery)
            .should(sixthQuery);

        boolQuery.minimumShouldMatch(1);
    }

    // private void buildMultiMatchQuery(BoolQueryBuilder boolQuery,List<String> segment, List<String> synonyms) {
    //
    // synonyms.removeAll(segment);
    // // Combine both segments and synonyms with appropriate weights
    // String segmentText = String.join(" ", segment);
    // String synonymsText = String.join(" ", synonyms);
    //
    // boolQuery .should(QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f)
    // .field("tocs", 5.0f).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.BEST_FIELDS))
    // .should(QueryBuilders.multiMatchQuery(segmentText).field("content", 4.0f).field("tags", 4.0f)
    // .field("tocs", 5.0f).type(MultiMatchQueryBuilder.Type.BEST_FIELDS))
    // .should(QueryBuilders.multiMatchQuery(synonymsText).field("content", 0.12f) // 1x weight for synonyms
    // .field("tags", 0.12f).field("tocs", 0.15f).type(MultiMatchQueryBuilder.Type.BEST_FIELDS));
    //
    // }



    private String[] buildIndices(List<Long> bookIdList) {
        // 优先按 bookId 匹配（忽略 teamId）
        return bookIdList.stream().map(bookId -> EsChunkService.INDEX_NAME + bookId).toArray(String[]::new);
    }

}
