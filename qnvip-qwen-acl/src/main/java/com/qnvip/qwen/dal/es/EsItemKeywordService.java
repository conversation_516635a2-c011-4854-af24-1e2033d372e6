package com.qnvip.qwen.dal.es;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.dal.es.esdo.ItemKeywordESDO;
import com.qnvip.qwen.es.EsClientConfig;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月31日 10:00:00
 */
@Slf4j
@Service
@DependsOn("esClientConfig")
public class EsItemKeywordService {

    private RestHighLevelClient client = EsClientConfig.rebuildByException(null, null, this.getClass());

    public static final String INDEX_NAME = "index_item_keyword";

    public void save(ItemKeywordESDO esdo) {
        String indexName = getIndexName();

        // 检查索引是否存在，不存在则创建
        createIndexIfNotExists(indexName);

        // 写入新的数据
        save(esdo, indexName);
    }

    /**
     * 批量保存或更新文档块到Elasticsearch
     *
     */
    public void batchSaveOrUpdate(List<ItemKeywordESDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String index = getIndexName();

        createIndexIfNotExists(index);

        // 批量处理
        for (List<ItemKeywordESDO> batch : ListUtil.partition(list, 2000)) {
            BulkRequest bulkRequest = new BulkRequest();

            for (ItemKeywordESDO doc : batch) {
                Long id = doc.getId(); // 获取文档 ID
                // 创建更新请求
                String jsonDoc = JSONUtil.toJsonStr(doc);
                IndexRequest indexRequest =
                    new IndexRequest(index).id(String.valueOf(id)).source(jsonDoc, XContentType.JSON);
                UpdateRequest updateRequest =
                    new UpdateRequest(index, String.valueOf(id)).doc(jsonDoc, XContentType.JSON).upsert(indexRequest);
                bulkRequest.add(updateRequest);

            }

            // 执行批量请求
            executeBulkRequest(bulkRequest);
        }
    }

    private String getIndexName() {
        return INDEX_NAME;
    }

    private void createIndexIfNotExists(String indexName) {
        GetIndexRequest existsRequest = new GetIndexRequest(indexName);
        try {
            boolean exists = client.indices().exists(existsRequest, RequestOptions.DEFAULT);
            if (!exists) {
                CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);

                // 使用新的 mapping 配置
                String mapping = "{" + "\"settings\":{" + "  \"index.max_ngram_diff\":10," + "  \"analysis\":{"
                    + "    \"analyzer\":{" + "      \"my_edge_ngram_analyzer\":{" + "        \"type\":\"custom\","
                    + "        \"tokenizer\":\"standard\"," + "        \"filter\":[\"lowercase\",\"my_edge_ngram\"]"
                    + "      }," + "      \"my_ngram_analyzer\":{" + "        \"type\":\"custom\","
                    + "        \"tokenizer\":\"standard\"," + "        \"filter\":[\"lowercase\",\"my_ngram\"]"
                    + "      }" + "    }," + "    \"filter\":{" + "      \"my_edge_ngram\":{"
                    + "        \"type\":\"edge_ngram\"," + "        \"min_gram\":2," + "        \"max_gram\":6"
                    + "      }," + "      \"my_ngram\":{" + "        \"type\":\"ngram\"," + "        \"min_gram\":2,"
                    + "        \"max_gram\":6" + "      }" + "    }" + "  }" + "}," + "\"mappings\":{"
                    + "  \"properties\":{" + "    \"id\":{ \"type\":\"long\" },"
                    + "    \"platformId\":{ \"type\":\"long\" }," + "    \"name\":{" + "      \"type\":\"text\","
                    + "      \"fields\":{" + "        \"edge_ngram\":{" + "          \"type\":\"text\","
                    + "          \"analyzer\":\"my_edge_ngram_analyzer\"" + "        }," + "        \"ngram\":{"
                    + "          \"type\":\"text\"," + "          \"analyzer\":\"my_ngram_analyzer\"" + "        }"
                    + "      }" + "    }" + "  }" + "}" + "}";

                createIndexRequest.source(mapping, XContentType.JSON);
                CreateIndexResponse createIndexResponse =
                    client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
                if (!createIndexResponse.isAcknowledged()) {
                    System.err.println("Failed to create index: " + indexName);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void save(ItemKeywordESDO esdo, String indexName) {
        try {
            String json = JSON.toJSONString(esdo);
            IndexRequest indexRequest = new IndexRequest(indexName).source(json, XContentType.JSON);
            client.index(indexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public List<ItemKeywordESDO> search(Long platformId, String matchTxt, Integer topK) {
        // 验证 platformId 不为空
        if (platformId == null) {
            return new ArrayList<>();
        }

        // 构建布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 添加 platformId 过滤条件
        boolQuery.must(QueryBuilders.termQuery("platformId", platformId));

        // 添加多字段匹配查询
        if (matchTxt != null && !matchTxt.isEmpty()) {
            // MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(matchTxt, "name.edge_ngram",
            // "name.ngram")
            // .field("name.edge_ngram", 1.0f).field("name.ngram", 1.0f);
            MultiMatchQueryBuilder multiMatchQuery =
                QueryBuilders.multiMatchQuery(matchTxt, "name.ngram").field("name.ngram", 1.0f);
            boolQuery.must(multiMatchQuery);
        }

        // 构建搜索请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        // 按得分降序排序
        // searchSourceBuilder.sort("_score", SortOrder.DESC);
        // 获取得分最高的20个结果
        searchSourceBuilder.size(topK);

        SearchRequest searchRequest = new SearchRequest(getIndexName());
        searchRequest.source(searchSourceBuilder);

        List<ItemKeywordESDO> resultList = new ArrayList<>();
        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                String sourceAsString = hit.getSourceAsString();
                ItemKeywordESDO esdo = JSON.parseObject(sourceAsString, ItemKeywordESDO.class);
                resultList.add(esdo);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultList;
    }

    /**
     * 根据ID列表删除ES中的关键词数据
     * 
     * @param ids 要删除的ID列表
     * @return 删除结果，true表示成功，false表示失败
     */
    public boolean deleteByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }

        try {
            BulkRequest bulkRequest = new BulkRequest();
            for (Long id : ids) {
                DeleteRequest deleteRequest = new DeleteRequest(getIndexName()).id(id.toString());
                bulkRequest.add(deleteRequest);
            }

            BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                log.error("ES批量删除失败: {}", bulkResponse.buildFailureMessage());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("删除ES关键词数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    private void executeBulkRequest(BulkRequest bulkRequest) {
        if (bulkRequest.numberOfActions() >= 1) {
            try {
                client.bulk(bulkRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.error("Bulk request failed: {}", e.getMessage());
                // 处理重试逻辑
                handleBulkRequestRetry(bulkRequest);
            }
        }
    }

    private void handleBulkRequestRetry(BulkRequest bulkRequest) {
        try {
            client = EsClientConfig.rebuildByException(new RuntimeException("Retrying bulk request"), client,
                this.getClass());
            client.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("ES重试更新IOException: {}", e.getMessage());
            throw FrameworkException.instance("ES重试更新IOException");
        }
    }
}
