package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件切块数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@Data
@TableName("qw_file_chunk_data")
public class FileChunkDataDO extends BaseDO {


    @ApiModelProperty("文件切块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("文件切块内容")
    private String data;

    @ApiModelProperty("排序")
    private Integer sort;
}

