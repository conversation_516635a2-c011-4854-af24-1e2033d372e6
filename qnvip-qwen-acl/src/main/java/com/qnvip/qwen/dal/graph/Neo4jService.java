package com.qnvip.qwen.dal.graph;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.ogm.model.Result;
import org.neo4j.ogm.session.Session;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.qnvip.qwen.dal.dto.FileGraphExtractDataDTO;
import com.qnvip.qwen.dal.dto.graph.GraphEntity;
import com.qnvip.qwen.dal.dto.graph.GraphRelationship;
import com.qnvip.qwen.enums.CategoryEnum;

@Repository
public class Neo4jService {

    @Resource
    private Session neo4jSession;

    public Result executeQuery(String cypher) {
        return neo4jSession.query(cypher, Collections.emptyMap());
    }

    public void executeQuery(String cypher, Map<String, Object> properties) {
        neo4jSession.query(cypher, properties);
    }




    public void deleteAll() {
        /// 删除关系
        String deleteRelationshipCypher = "MATCH ()-[r]-()\n" + "DELETE r";
        executeQuery(deleteRelationshipCypher, new HashMap<>());

        /// 删除节点
        String deleteEntityCypher = "MATCH (n)\n" + "DELETE n";
        executeQuery(deleteEntityCypher, new HashMap<>());
    }

    public void deleteByFileOriginId(Long fileOriginId) {
        // 参数验证
        if (fileOriginId == null) {
            throw new IllegalArgumentException("fileOriginId cannot be null");
        }

        // 1. 首先处理关系 - 删除或更新关系的fileOriginId
        String deleteRelationshipCypher = "MATCH ()-[r]->() " + "WHERE $fileOriginId IN r.fileOriginId "
            + "SET r.fileOriginId = [id IN r.fileOriginId WHERE id <> $fileOriginId] " + "WITH r "
            + "WHERE size(r.fileOriginId) = 0 " + "DELETE r";

        Map<String, Object> params = new HashMap<>();
        params.put("fileOriginId", fileOriginId);
        executeQuery(deleteRelationshipCypher, params);

        // 2. 处理节点 - 更新fileOriginId并删除空节点
        String deleteEntityCypher = "MATCH (n) " + "WHERE $fileOriginId IN n.fileOriginId "
            + "SET n.fileOriginId = [id IN n.fileOriginId WHERE id <> $fileOriginId] " + "WITH n "
            + "WHERE size(n.fileOriginId) = 0 " + "OPTIONAL MATCH (n)-[r]-() " + "DELETE r, n";
        executeQuery(deleteEntityCypher, params);
    }

    public void saveBatch(List<FileGraphExtractDataDTO> graphs) {
        // 1. 输入校验
        if (CollectionUtils.isEmpty(graphs)) {
            return;
        }

        // 2. 更高效的分组方式
        Map<Boolean, List<FileGraphExtractDataDTO>> groupedData =
            graphs.stream().collect(Collectors.partitioningBy(dto -> StringUtils.isNotBlank(dto.getEntName())));

        // 3. 处理实体数据
        List<GraphEntity> entities = processEntities(groupedData.get(true));

        // 4. 处理关系数据
        List<GraphRelationship> relationships = processRelationships(groupedData.get(false));

        // 5. 批量保存
        if (!entities.isEmpty()) {
            saveEntity(entities);
        }
        if (!relationships.isEmpty()) {
            saveRelationship(relationships);
        }
    }

    // 处理实体数据的私有方法
    private List<GraphEntity> processEntities(List<FileGraphExtractDataDTO> entityData) {
        if (CollectionUtils.isEmpty(entityData)) {
            return Collections.emptyList();
        }

        return entityData.stream().collect(Collectors.groupingBy(FileGraphExtractDataDTO::getEntName,
            Collectors.collectingAndThen(Collectors.toList(), list -> {
                FileGraphExtractDataDTO first = list.get(0);
                GraphEntity entity = new GraphEntity();
                entity.setCategory(CategoryEnum.ENTITY.getRealDesc());
                entity.setBookId(extractDistinctValues(list, FileGraphExtractDataDTO::getBookId));
                entity.setFileOriginId(extractDistinctValues(list, FileGraphExtractDataDTO::getFileOriginId));
                entity.setFileChunkUid(extractDistinctStringValues(list, FileGraphExtractDataDTO::getChunkUid));
                entity.setEntityName(first.getEntName());
                entity.setEntityType(first.getEntType());
                return entity;
            }))).values().stream().collect(Collectors.toList());
    }

    // 处理关系数据的私有方法
    private List<GraphRelationship> processRelationships(List<FileGraphExtractDataDTO> relationData) {
        if (CollectionUtils.isEmpty(relationData)) {
            return Collections.emptyList();
        }

        return relationData.stream()
            .filter(
                dto -> StringUtils.isNotBlank(dto.getSrc()) && StringUtils.isNotBlank(dto.getTgt()))
            .collect(Collectors.groupingBy(dto -> dto.getSrc() + "_" + dto.getTgt(),
                Collectors.collectingAndThen(Collectors.toList(), list -> {
                    FileGraphExtractDataDTO first = list.get(0);
                    GraphRelationship rel = new GraphRelationship();
                    rel.setCategory(CategoryEnum.RELATIONSHIP.getRealDesc());
                    rel.setBookId(extractDistinctValues(list, FileGraphExtractDataDTO::getBookId));
                    rel.setFileOriginId(extractDistinctValues(list, FileGraphExtractDataDTO::getFileOriginId));
                    rel.setFileChunkUid(extractDistinctStringValues(list, FileGraphExtractDataDTO::getChunkUid));
                    rel.setSourceEntity(first.getSrc());
                    rel.setTargetEntity(first.getTgt());
                    rel.setRelationshipKeyword(
                        Collections.singletonList(list.stream().map(FileGraphExtractDataDTO::getRelKeys)
                            .filter(StringUtils::isNotBlank).collect(Collectors.joining(","))));
                    return rel;
                })))
            .values().stream().collect(Collectors.toList());
    }

    // 提取去重Long值的通用方法
    private List<Long> extractDistinctValues(List<FileGraphExtractDataDTO> list,
        Function<FileGraphExtractDataDTO, Long> extractor) {
        return list.stream().map(extractor).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    // 提取去重String值的通用方法
    private List<String> extractDistinctStringValues(List<FileGraphExtractDataDTO> list,
        Function<FileGraphExtractDataDTO, String> extractor) {
        return list.stream().map(extractor).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    private void saveRelationship(List<GraphRelationship> relationships) {
        synchronized (this) {
            String relationshipsCypher =
                "UNWIND $relationships AS rel\n" + "MATCH (source {entityName: rel.sourceEntity})\n"
                    + "MATCH (target {entityName: rel.targetEntity})\n" + "MERGE (source)-[r:RELATIONSHIP]->(target)\n"
                    + "ON CREATE SET \n" + "    r.bookId = apoc.coll.toSet(rel.bookId),\n"
                    + "    r.fileOriginId = apoc.coll.toSet(rel.fileOriginId),\n"
                    + "    r.fileChunkUid = rel.fileChunkUid,\n"
                    + "    r.relationshipKeyword = rel.relationshipKeyword\n" + "ON MATCH SET \n"
                    + "    r.bookId = apoc.coll.toSet(apoc.coll.flatten([r.bookId, rel.bookId])),\n"
                    + "    r.fileOriginId = apoc.coll.toSet(apoc.coll.flatten([r.fileOriginId, rel.fileOriginId])),\n"
                    + "    r.fileChunkUid = apoc.coll.flatten([r.fileChunkUid, rel.fileChunkUid]),\n"
                    + "    r.relationshipKeyword = apoc.coll.toSet(r.relationshipKeyword + rel.relationshipKeyword)";

            Map<String, Object> relationshipsBatchParams = new HashMap<>();
            relationshipsBatchParams.put("relationships", JSON.parseArray(JSON.toJSONString(relationships)));
            neo4jSession.query(relationshipsCypher, relationshipsBatchParams);
        }
    }

    public void saveEntity(List<GraphEntity> entities) {
        Map<String, List<GraphEntity>> grouped =
            entities.stream().collect(Collectors.groupingBy(GraphEntity::getEntityType));
        for (Map.Entry<String, List<GraphEntity>> entry : grouped.entrySet()) {
            String label = entry.getKey();
            List<GraphEntity> groupEntities = entry.getValue();
            String cypher = "UNWIND $entities AS entity\n" + "MERGE (n:" + label + " {entityName: entity.entityName})\n"
                + "ON CREATE SET n.entityType = entity.entityType, \n"
                + "n.bookId = apoc.coll.toSet(entity.bookId), n.fileOriginId = apoc.coll.toSet(entity.fileOriginId), \n"
                + "n.fileChunkUid = entity.fileChunkUid\n" + "ON MATCH SET  n.entityType = entity.entityType, "
                + "n.bookId = apoc.coll.toSet(apoc.coll.flatten([n.bookId, entity.bookId])), "
                + "n.fileOriginId = apoc.coll.toSet(apoc.coll.flatten([n.fileOriginId, entity.fileOriginId])), "
                + "n.fileChunkUid = apoc.coll.flatten([n.fileChunkUid, entity.fileChunkUid])";
            Map<String, Object> params = new HashMap<>();
            params.put("entities", groupEntities);
            neo4jSession.query(cypher, params);
        }
    }

    public Result execute(String cypherMatch, List<Long> bookIds,
        List<String> excludedFileChunkUid) {
        String cypher = cypherMatch;

        Map<String, Object> parameters = new HashMap<>();
        // bookIds条件
        if (!ObjectUtils.isEmpty(bookIds)) {
            parameters.put("bookIds", bookIds);
        }

        // fileChunkUids条件（支持排除）
        if (ObjectUtils.isEmpty(excludedFileChunkUid)) {
            parameters.put("fileChunkUids", excludedFileChunkUid);
        }

        return neo4jSession.query(cypher, parameters);
    }

    public Map<String, Integer> nodeDegreesBatch(List<Long> bookIds, List<String> noInChunkIds, List<String> nodeIds) {

        // 1. 构建基础查询
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("MATCH (n) ");

        // 2. 拼接where条件
        boolean hasBookIds = bookIds != null && !bookIds.isEmpty();
        boolean hasNoInChunkIds = noInChunkIds != null && !noInChunkIds.isEmpty();
        List<String> conditions = new ArrayList<>();
        conditions.add("n.entityName IN $nodeIds");
        if (hasBookIds) {
            conditions.add("any(bid IN n.bookId WHERE bid IN $bookIds)");
        }
        if (hasNoInChunkIds) {
            conditions.add("NOT n.fileChunkUid IN $noInChunkIds");
        }
        if (!conditions.isEmpty()) {
            queryBuilder.append("WHERE ").append(String.join(" AND ", conditions)).append(" ");
        }

        queryBuilder.append("RETURN n.entityName as entityName, size((n)--()) AS degree");

        String query = queryBuilder.toString();

        // 3. 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("nodeIds", nodeIds);
        if (hasBookIds) {
            params.put("bookIds", bookIds);
        }
        if (hasNoInChunkIds) {
            params.put("noInChunkIds", noInChunkIds);
        }

        // 4. 执行查询
        Result result = neo4jSession.query(query, params);

        Map<String, Integer> degrees = new HashMap<>();
        for (Map<String, Object> queryResult : result.queryResults()) {
            String entityId = queryResult.get("entityName").toString();
            int degree = Integer.parseInt(queryResult.get("degree").toString());
            degrees.put(entityId, degree);
        }

        // 处理未找到的节点
        for (String nodeId : nodeIds) {
            if (!degrees.containsKey(nodeId)) {
                degrees.put(nodeId, 0);
            }
        }

        return degrees;
    }

    /**
     * 批量获取节点及其边的关系
     * 
     * @return Map<节点名称, List<边>>，其中边用 Map 表示，包含 source 和 target 键
     */
    public Map<String, List<GraphRelationship>> getNodesEdgesBatch(List<Long> bookIds, List<String> noInChunkIds,
                                                                   List<String> nodeIds) {
        // 1. 构建基础查询
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("MATCH (n) ");

        // 2. 拼接where条件
        boolean hasBookIds = bookIds != null && !bookIds.isEmpty();
        boolean hasNoInChunkIds = noInChunkIds != null && !noInChunkIds.isEmpty();
        List<String> conditions = new ArrayList<>();
        conditions.add("n.entityName IN $nodeIds");
        if (hasBookIds) {
            conditions.add("any(bid IN n.bookId WHERE bid IN $bookIds)");
        }
        if (hasNoInChunkIds) {
            conditions.add("NOT n.fileChunkUid IN $noInChunkIds");
        }
        if (!conditions.isEmpty()) {
            queryBuilder.append("WHERE ").append(String.join(" AND ", conditions)).append(" ");
        }

        queryBuilder.append("WITH n ")
                .append("OPTIONAL MATCH (n)-[r]-(connected) ")
                .append("WHERE connected.entityName IS NOT NULL ")
                .append("WITH n, connected, r, startNode(r).entityName AS startEntityId ")
                .append("ORDER BY n.entityName, r ") // 可以添加排序条件，如r.createTime DESC等
                .append("WITH n.entityName AS entityName, collect([connected.entityName, startEntityId]) AS allConnections ")
                .append("UNWIND allConnections[0..30] AS connection ")
                .append("RETURN entityName AS queried_id, entityName AS node_entity_id, ")
                .append("connection[0] AS connected_entity_id, connection[1] AS start_entity_id");

        String query = queryBuilder.toString();

        // 3. 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("nodeIds", nodeIds);
        if (hasBookIds) {
            params.put("bookIds", bookIds);
        }
        if (hasNoInChunkIds) {
            params.put("noInChunkIds", noInChunkIds);
        }

        // 4. 执行查询和后续逻辑不变
        Iterable<Map<String, Object>> results = neo4jSession.query(query, params);

        Map<String, List<GraphRelationship>> edgesMap = new HashMap<>();
        for (String id : nodeIds) {
            edgesMap.put(id, new ArrayList<>());
        }

        for (Map<String, Object> record : results) {
            String queriedId = (String)record.get("queried_id");
            String nodeEntityId = (String)record.get("node_entity_id");
            String connectedEntityId = (String)record.get("connected_entity_id");
            String startEntityId = (String)record.get("start_entity_id");

            if (nodeEntityId == null || connectedEntityId == null) {
                continue;
            }

            String src;
            String tgt;
            if (nodeEntityId.equals(startEntityId)) {
                src = nodeEntityId;
                tgt = connectedEntityId;
            } else {
                src = connectedEntityId;
                tgt = nodeEntityId;
            }

            GraphRelationship edge = new GraphRelationship();
            edge.setSourceEntity(src);
            edge.setTargetEntity(tgt);
            edgesMap.computeIfAbsent(queriedId, k -> new ArrayList<>()).add(edge);
        }

        return edgesMap;
    }

    /**
     * 批量获取多个(src, tgt)节点对的边属性
     * 
     * @param pairs 包含源节点和目标节点ID的Map列表，每个Map包含"src"和"tgt"键
     * @return 返回一个Map，键是"src_tgt"字符串，值是对应的边属性Map
     */
    public Map<String, GraphRelationship> getEdgesBatch(List<Long> bookIds, List<String> noInChunkIds,
        List<GraphRelationship> pairs) {
        if (ObjectUtils.isEmpty(pairs)) {
            return Maps.newHashMap();
        }

        // 1. 构建基础查询
        StringBuilder cypherBuilder = new StringBuilder();
        cypherBuilder.append("UNWIND $pairs AS pair ")
            .append("MATCH (start {entityName: pair.sourceEntity})-[r]-(end {entityName: pair.targetEntity}) ");

        // 2. 拼接bookId和noInChunkIds条件
        boolean hasBookIds = bookIds != null && !bookIds.isEmpty();
        boolean hasNoInChunkIds = noInChunkIds != null && !noInChunkIds.isEmpty();
        List<String> conditions = new ArrayList<>();
        if (hasBookIds) {
            conditions.add("any(bid IN start.bookId WHERE bid IN $bookIds)");
        }
        if (hasNoInChunkIds) {
            conditions.add("NOT start.fileChunkUid IN $noInChunkIds");
        }
        if (!conditions.isEmpty()) {
            cypherBuilder.append("WHERE ").append(String.join(" AND ", conditions)).append(" ");
        }

        cypherBuilder.append(
            "RETURN pair.sourceEntity AS sourceEntity, pair.targetEntity AS targetEntity, collect(properties(r)) AS edges");
        String cypher = cypherBuilder.toString();

        // 3. 构建参数
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("pairs", pairs);
        if (hasBookIds) {
            parameters.put("bookIds", bookIds);
        }
        if (hasNoInChunkIds) {
            parameters.put("noInChunkIds", noInChunkIds);
        }

        // 4. 执行查询
        Iterable<Map<String, Object>> results = neo4jSession.query(cypher, parameters).queryResults();

        // 初始化结果Map
        Map<String, GraphRelationship> edgesMap = new HashMap<>();

        // 处理查询结果
        for (Map<String, Object> record : results) {
            String sourceEntity = (String)record.get("sourceEntity");
            String targetEntity = (String)record.get("targetEntity");
            List<GraphRelationship> edges =
                JSON.parseArray(JSON.toJSONString(record.get("edges")), GraphRelationship.class);

            // 生成复合键
            String edgeKey = sourceEntity + "_" + targetEntity;
            if (edges != null && !edges.isEmpty()) {
                GraphRelationship graphRelationship = edges.get(0);
                graphRelationship.setSourceEntity(sourceEntity);
                graphRelationship.setTargetEntity(targetEntity);
                edgesMap.put(edgeKey, edges.get(0));
            } else {
                edgesMap.put(edgeKey, new GraphRelationship());
            }
        }

        return edgesMap;
    }

    public Map<String, GraphEntity> getNodesBatch(List<Long> bookIds, List<String> noInChunkIds, List<String> nodeIds) {

        // 1. 构建基础查询
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("MATCH (n) ");

        // 2. 拼接where条件
        boolean hasBookIds = bookIds != null && !bookIds.isEmpty();
        boolean hasNoInChunkIds = noInChunkIds != null && !noInChunkIds.isEmpty();
        List<String> conditions = new ArrayList<>();
        conditions.add("n.entityName IN $nodeIds");
        if (hasBookIds) {
            conditions.add("any(bid IN n.bookId WHERE bid IN $bookIds)");
        }
        if (hasNoInChunkIds) {
            conditions.add("NOT n.fileChunkUid IN $noInChunkIds");
        }
        if (!conditions.isEmpty()) {
            queryBuilder.append("WHERE ").append(String.join(" AND ", conditions)).append(" ");
        }

        queryBuilder.append("RETURN n.entityName AS entityName, properties(n) as node_properties");

        String query = queryBuilder.toString();

        // 3. 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("nodeIds", nodeIds);
        if (hasBookIds) {
            params.put("bookIds", bookIds);
        }
        if (hasNoInChunkIds) {
            params.put("noInChunkIds", noInChunkIds);
        }

        // 4. 执行查询
        Iterable<Map<String, Object>> result =
            neo4jSession.query(query, params);

        Map<String, GraphEntity> nodes = new HashMap<>();
        for (Map<String, Object> record : result) {
            String entityName = (String)record.get("entityName");
            GraphEntity edges = JSON.parseObject(JSON.toJSONString(record.get("node_properties")), GraphEntity.class);
            nodes.put(entityName, edges);
        }

        return nodes;
    }


}
