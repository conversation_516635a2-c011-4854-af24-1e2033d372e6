package com.qnvip.qwen.dal.dao.impl;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileQaDaoService;
import com.qnvip.qwen.dal.dto.FileQaDTO;
import com.qnvip.qwen.dal.dto.FileQaQueryDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.mapper.FileQaMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 文档QA表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileQaDaoServiceImpl extends BaseServiceImpl<FileQaMapper, FileQaDO> implements FileQaDaoService {
    @Resource
    private FileQaMapper fileQaMapper;


    @Override
    public IPage<FileQaDTO> page(IPage<FileQaDO> page, FileQaQueryDTO query) {
        IPage<FileQaDO> pageData = lambdaQuery().eq(query.getId() != null, FileQaDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileQaDTO.class));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        fileQaMapper.deleteByFileOriginId(fileOriginId);
    }


    @Override
    public List<FileQaDO> getSortedQaByUIds(List<String> uids) {
        if (CollUtil.isEmpty(uids)) {
            return new ArrayList<>();
        }
        String uidStr = "'" + String.join("','", uids) + "'";
        List<FileQaDO> chunkUIdListByUIds = fileQaMapper.getChunkUIdListByUIds(uidStr);
        return sortByQaUid(chunkUIdListByUIds, uids);
    }

    private static List<FileQaDO> sortByQaUid(List<FileQaDO> fileQas, List<String> qaUids) {
        Map<String, FileQaDO> uidToFileQaMap = fileQas.stream().collect(Collectors.toMap(FileQaDO::getUid, qa -> qa));

        return qaUids.stream().map(uidToFileQaMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<RagChunkDTO> getMilvusInitList(List<Long> fileOriginIds) {
        return fileQaMapper.getMilvusInitList(fileOriginIds);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileQaMapper.deleteByFileOriginIds(StringUtils.join(fileOriginIds, ","));
    }
}
