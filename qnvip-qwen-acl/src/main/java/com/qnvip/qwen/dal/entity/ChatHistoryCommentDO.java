package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天记录评论表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@Data
@TableName("qw_chat_history_comment")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatHistoryCommentDO extends BaseDO {

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("会话Id")
    private String conversationId;


    @ApiModelProperty("消息id")
    private String messageId;

    @ApiModelProperty("是否点赞，1:是，0:否 -1踩")
    private Integer likeFlag;

    @ApiModelProperty("评论内容")
    private String content;

    @ApiModelProperty("评论标签")
    private String evaluateTag;
}
