package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.annotation.JSONField;
import com.qnvip.qwen.util.Lists;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;


public class EmbeddingClient {

    private static final Logger log = LoggerFactory.getLogger(EmbeddingClient.class);
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_DELAY_MILLIS = 3000; // 1秒延迟
    /**
     * http://localhost:8001/get_text_embedding
     */
    private final String url;

    public EmbeddingClient(String url) {
        this.url = url;
    }

    /**
     * 请求获取Embeddings，请求出错返回null
     *
     * @param msg 需要Embeddings的信息
     * @return 为null则请求失败，反之放回正确结果
     */
    public List<List<Float>> doEmbedding(String msg) {

        ArrayList<String> sourceSentence = Lists.newArrayList(msg);

        return doEmbedding(sourceSentence);
    }

    // public List<List<Float>> doEmbedding(List<String> sourceSentence) {
    // EmbeddingInput embeddingInput = new EmbeddingInput(sourceSentence);
    // String jsonArrayStr = HttpUtil.post(url, JSON.toJSONString(embeddingInput));
    // try {
    // return JSON.parseObject(jsonArrayStr, new TypeReference<List<List<Float>>>() {});
    // } catch (Exception e) {
    // log.error("doEmbedding异常,sourceSentence:{},jsonArrayStr:{}", JSONUtil.toJsonStr(sourceSentence), jsonArrayStr,
    // e);
    // }
    // return null;
    // }

    public List<List<Float>> doEmbedding(List<String> sourceSentence) {
        EmbeddingInput embeddingInput = new EmbeddingInput(sourceSentence);
        int retries = 0;

        while (retries < MAX_RETRIES) {
            try {
                String jsonArrayStr = HttpUtil.post(url, JSON.toJSONString(embeddingInput));
                return JSON.parseObject(jsonArrayStr, new TypeReference<List<List<Float>>>() {});
            } catch (Exception e) {
                retries++;
                log.error("doEmbedding异常, 重试次数: {}, sourceSentence: {}", retries, JSONUtil.toJsonStr(sourceSentence),
                    e);
                if (retries < MAX_RETRIES) {
                    try {
                        Thread.sleep(RETRY_DELAY_MILLIS);
                    } catch (InterruptedException interruptedException) {
                        Thread.currentThread().interrupt();
                        log.error("重试期间线程被中断: ", interruptedException);
                    }
                }
            }
        }

        log.error("doEmbedding失败，达到最大重试次数，sourceSentence: {}", JSONUtil.toJsonStr(sourceSentence));
        return null;
    }

    @Data
    @AllArgsConstructor
    public static class EmbeddingInput {
        @JSONField(name = "source_sentence")
        private List<String> sourceSentence;
    }

    public static void main(String[] args) {
        String str =
            "";
        List<String> sourceSentence = JSONUtil.toList(str, String.class);
        EmbeddingInput embeddingInput = new EmbeddingInput(sourceSentence);
        String jsonArrayStr =
            HttpUtil.post("http://115.190.96.78:8001/get_text_embedding", JSON.toJSONString(embeddingInput));
        System.out.println(jsonArrayStr);
    }

}
