// package com.qnvip.qwen.dal.es;
//
// import java.io.IOException;
// import java.util.List;
//
// import org.elasticsearch.action.bulk.BulkRequest;
// import org.elasticsearch.action.index.IndexRequest;
// import org.elasticsearch.action.update.UpdateRequest;
// import org.elasticsearch.client.RequestOptions;
// import org.elasticsearch.client.RestHighLevelClient;
// import org.elasticsearch.client.indices.GetIndexRequest;
// import org.elasticsearch.common.xcontent.XContentType;
// import org.elasticsearch.index.query.QueryBuilders;
// import org.elasticsearch.index.reindex.BulkByScrollResponse;
// import org.elasticsearch.index.reindex.DeleteByQueryRequest;
// import org.springframework.context.annotation.DependsOn;
// import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
// import org.springframework.data.elasticsearch.core.IndexOperations;
// import org.springframework.data.elasticsearch.core.document.Document;
// import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
// import org.springframework.stereotype.Service;
//
// import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
// import com.qnvip.common.exception.FrameworkException;
// import com.qnvip.qwen.dal.es.esdo.DocChunkESDO;
// import com.qnvip.qwen.es.EsClientConfig;
//
// import cn.hutool.core.collection.ListUtil;
// import cn.hutool.json.JSONUtil;
// import lombok.extern.slf4j.Slf4j;
//
/// **
// * <AUTHOR>
// * @desc 描述
// * @createTime 2025年03月31日 10:00:00
// */
// @Slf4j
// @Service
// @DependsOn("esClientConfig")
// public class EsGraphService {
//
// public static final String INDEX_NAME = "index_graph_v3_";
// private final ElasticsearchOperations elasticsearchOperations = EsClientConfig.elasticsearchOperations();
// private RestHighLevelClient client = EsClientConfig.rebuildByException(null, null, this.getClass());
//
// public void createIndexIfNotExists(Long bookId) {
// String indexName = INDEX_NAME + bookId;
//
// // 使用 IndexCoordinates 指定索引名，不再使用 withTypes()
// IndexOperations indexOps = elasticsearchOperations.indexOps(IndexCoordinates.of(indexName));
//
// if (!indexOps.exists()) {
// synchronized (this) {
// if (!indexOps.exists()) {
// try {
// indexOps.create();
// Document mapping = indexOps.createMapping(DocChunkESDO.class);
// indexOps.putMapping(mapping);
// indexOps.refresh();
// } catch (Exception e) {
// e.printStackTrace();
// }
// }
// }
//
// }
//
// }
//
// /**
// * 批量保存或更新文档块到Elasticsearch
// *
// * @param chunkESDOList 文档块列表
// * @param bookId 知识库ID
// */
// public void batchSaveOrUpdate(List<DocChunkESDO> chunkESDOList, Long bookId) {
// String index = INDEX_NAME + bookId;
//
// if (CollectionUtils.isEmpty(chunkESDOList)) {
// return;
// }
//
// createIndexIfNotExists(bookId);
//
// // 批量处理
// for (List<DocChunkESDO> batch : ListUtil.partition(chunkESDOList, 2000)) {
// BulkRequest bulkRequest = new BulkRequest();
//
// for (DocChunkESDO doc : batch) {
// String id = doc.getChunkUId(); // 获取文档 ID
// // 创建更新请求
// String jsonDoc = JSONUtil.toJsonStr(doc);
// IndexRequest indexRequest = new IndexRequest(index).id(id).source(jsonDoc, XContentType.JSON);
// UpdateRequest updateRequest =
// new UpdateRequest(index, id).doc(jsonDoc, XContentType.JSON).upsert(indexRequest);
// bulkRequest.add(updateRequest);
//
// }
//
// // 执行批量请求
// executeBulkRequest(bulkRequest);
// }
// }
//
// /**
// * 根据文件原始ID删除分块数据
// *
// * @param fileOriginId 文件原始ID
// * @param bookId 知识库ID
// */
// public void deleteChunksByFileOriginId(Long fileOriginId, Long bookId) {
// String index = INDEX_NAME + bookId;
//
// createIndexIfNotExists(bookId);
//
// // 使用 DeleteByQueryRequest 删除匹配的文档
// DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(index);
// deleteByQueryRequest.setQuery(QueryBuilders.termQuery("fileOriginId", fileOriginId));
// deleteByQueryRequest.setConflicts("proceed");
// try {
// // 执行删除请求
// BulkByScrollResponse response = client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
// log.info("deleteByQuery: {}, response:{}", index, response);
// } catch (IOException e) {
// log.error(e.getMessage(), e);
// throw FrameworkException.instance("es数据更新失败");
// } catch (RuntimeException e) {
// try {
// client = EsClientConfig.rebuildByException(e, client, this.getClass());
// client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
// } catch (IOException ioException) {
// log.error("es 重试 update IOException :{}", ioException.getMessage());
// throw FrameworkException.instance("es重试更新IOException");
// }
// }
// }
//
// /**
// * 根据文件原始ID删除分块数据
// *
// * @param fileOriginIds 文件原始ID
// */
// public void batchDeleteByFileOriginIds(List<Long> fileOriginIds) {
// String index = INDEX_NAME + "*";
//
// // 使用 DeleteByQueryRequest 删除匹配的文档
// DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(index);
// deleteByQueryRequest.setQuery(QueryBuilders.termsQuery("fileOriginId", fileOriginIds));
// deleteByQueryRequest.setConflicts("proceed");
// try {
// // 执行删除请求
// BulkByScrollResponse response = client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
// log.info("deleteByQuery: {}, response:{}", index, response);
// } catch (Exception e) {
// log.error(e.getMessage(), e);
// e.printStackTrace();
// }
// }
//
// private void executeBulkRequest(BulkRequest bulkRequest) {
// if (bulkRequest.numberOfActions() >= 1) {
// try {
// client.bulk(bulkRequest, RequestOptions.DEFAULT);
// } catch (IOException e) {
// log.error("Bulk request failed: {}", e.getMessage());
// // 处理重试逻辑
// handleBulkRequestRetry(bulkRequest);
// }
// }
// }
//
// private void handleBulkRequestRetry(BulkRequest bulkRequest) {
// try {
// client = EsClientConfig.rebuildByException(new RuntimeException("Retrying bulk request"), client,
// this.getClass());
// client.bulk(bulkRequest, RequestOptions.DEFAULT);
// } catch (IOException e) {
// log.error("ES重试更新IOException: {}", e.getMessage());
// throw FrameworkException.instance("ES重试更新IOException");
// }
// }
//
// /**
// * 检查索引是否存在
// *
// * @param indexName
// * @return
// * @throws Exception
// */
// private boolean indexExists(String indexName) throws Exception {
// return client.indices().exists(new GetIndexRequest(indexName), RequestOptions.DEFAULT);
// }
//
// }
