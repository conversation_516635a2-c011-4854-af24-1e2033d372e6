// package com.qnvip.qwen.dal.es;
//
// import java.io.IOException;
// import java.util.ArrayList;
// import java.util.List;
//
// import org.elasticsearch.action.bulk.BulkRequest;
// import org.elasticsearch.action.index.IndexRequest;
// import org.elasticsearch.action.search.SearchRequest;
// import org.elasticsearch.action.search.SearchResponse;
// import org.elasticsearch.action.update.UpdateRequest;
// import org.elasticsearch.client.RequestOptions;
// import org.elasticsearch.client.RestHighLevelClient;
// import org.elasticsearch.client.indices.CreateIndexRequest;
// import org.elasticsearch.client.indices.CreateIndexResponse;
// import org.elasticsearch.client.indices.GetIndexRequest;
// import org.elasticsearch.common.xcontent.XContentType;
// import org.elasticsearch.index.query.BoolQueryBuilder;
// import org.elasticsearch.index.query.MultiMatchQueryBuilder;
// import org.elasticsearch.index.query.QueryBuilder;
// import org.elasticsearch.index.query.QueryBuilders;
// import org.elasticsearch.index.reindex.BulkByScrollResponse;
// import org.elasticsearch.index.reindex.DeleteByQueryRequest;
// import org.elasticsearch.search.SearchHit;
// import org.elasticsearch.search.builder.SearchSourceBuilder;
// import org.elasticsearch.search.sort.SortBuilders;
// import org.elasticsearch.search.sort.SortOrder;
// import org.springframework.context.annotation.DependsOn;
// import org.springframework.stereotype.Service;
// import org.springframework.util.ObjectUtils;
//
// import com.alibaba.fastjson2.JSON;
// import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
// import com.qnvip.common.exception.FrameworkException;
// import com.qnvip.qwen.dal.dto.FilterOptionsDTO;
// import com.qnvip.qwen.dal.es.esdo.TagNavigationESDO;
// import com.qnvip.qwen.es.EsClientConfig;
// import com.qnvip.qwen.util.ElasticsearchScoreNormalizeUtil;
//
// import cn.hutool.core.collection.ListUtil;
// import cn.hutool.json.JSONUtil;
// import lombok.extern.slf4j.Slf4j;
//
/// **
// * <AUTHOR>
// * @desc 描述
// * @createTime 2025年03月31日 10:00:00
// */
// @Slf4j
// @Service
// @DependsOn("esClientConfig")
// public class EsNavigationService {
//
// public static final String INDEX_NAME = "index_navigation_v2_";
// private RestHighLevelClient client = EsClientConfig.rebuildByException(null, null, this.getClass());
//
// public static void main(String[] args) {
// System.out.println(
// "{\"mappings\":{\"properties\":{\"fileOriginId\":{\"type\":\"long\"},\"teamId\":{\"type\":\"long\"},\"bookId\":{\"type\":\"long\"},\"summary\":{\"type\":\"text\"},\"tags\":{\"type\":\"text\"},\"tocs\":{\"type\":\"text\"}}}}");
// }
//
// public void saveAndDeleteBefore(TagNavigationESDO esdo) {
// String indexName = getIndexName(esdo.getTeamId(), esdo.getBookId());
//
// // 检查索引是否存在，不存在则创建
// createIndexIfNotExists(indexName);
//
// // 删除之前的数据
// deleteByFileOriginId(indexName, esdo.getFileOriginId());
//
// // 写入新的数据
// save(esdo, indexName);
// }
//
// /**
// * 构建索引名称数组
// */
// private String[] buildIndices(List<Long> bookIds) {
// if (ObjectUtils.isEmpty(bookIds)) {
// return new String[] {INDEX_NAME + "*"};
// } else {
// return bookIds.stream().map(bookId -> INDEX_NAME + "*_" + bookId).toArray(String[]::new);
// }
// }
//
// private String getIndexName(Long teamId, Long bookId) {
// String bookStr = bookId == null ? "*" : bookId.toString();
// return INDEX_NAME + teamId + "_" + bookStr;
// }
//
// private void createIndexIfNotExists(String indexName) {
// GetIndexRequest existsRequest = new GetIndexRequest(indexName);
// try {
// boolean exists = client.indices().exists(existsRequest, RequestOptions.DEFAULT);
// if (!exists) {
// CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
// String mapping =
// "{\"mappings\":{\"properties\":{\"fileOriginId\":{\"type\":\"long\"},\"teamId\":{\"type\":\"long\"},\"bookId\":{\"type\":\"long\"},\"summary\":{\"type\":\"text\"},\"tags\":{\"type\":\"text\"},\"tocs\":{\"type\":\"text\"}}}}";
// createIndexRequest.source(mapping, XContentType.JSON);
// CreateIndexResponse createIndexResponse =
// client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
// if (!createIndexResponse.isAcknowledged()) {
// System.err.println("Failed to create index: " + indexName);
// }
// }
// } catch (IOException e) {
// e.printStackTrace();
// }
// }
//
// private void deleteByFileOriginId(String indexName, Long fileOriginId) {
// DeleteByQueryRequest request = new DeleteByQueryRequest(indexName);
//
// // 构建查询（假设fileOriginId是keyword类型）
// QueryBuilder query = QueryBuilders.termQuery("fileOriginId", fileOriginId);
// request.setQuery(query);
//
// try {
// BulkByScrollResponse response = client.deleteByQuery(request, RequestOptions.DEFAULT);
// System.out.println("成功删除文档数: " + response.getDeleted());
// } catch (Exception e) {
// e.printStackTrace();
// }
// }
//
// private void save(TagNavigationESDO esdo, String indexName) {
// try {
// String json = JSON.toJSONString(esdo);
// IndexRequest indexRequest = new IndexRequest(indexName).source(json, XContentType.JSON);
// client.index(indexRequest, RequestOptions.DEFAULT);
// } catch (IOException e) {
// e.printStackTrace();
// }
// }
//
// public List<TagNavigationESDO> search(List<Long> bookIds, String matchTxt, FilterOptionsDTO filter) {
//
// BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//
// if (!ObjectUtils.isEmpty(bookIds)) {
// boolQuery.filter(QueryBuilders.termsQuery("bookId", bookIds));
// }
//
// if (!ObjectUtils.isEmpty(matchTxt)) {
// MultiMatchQueryBuilder multiMatchQuery = QueryBuilders.multiMatchQuery(matchTxt, "summary", "tags", "tocs")
// .field("summary", 1.0f).field("tags", 1.5f).field("tocs", 2f);
// boolQuery.must(multiMatchQuery);
// }
//
// // 按得分降序排序 获取得分最高的topN个结果
// SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(boolQuery).size(filter.getTopN())
// .sort(SortBuilders.scoreSort().order(SortOrder.DESC)); // 按评分降序
//
//
// SearchRequest searchRequest = new SearchRequest(buildIndices(bookIds)).source(sourceBuilder);
//
// List<TagNavigationESDO> resultList = new ArrayList<>();
// try {
// SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
// List<SearchHit> searchHits =
// ElasticsearchScoreNormalizeUtil.filterScore(searchResponse, filter.getMinScore());
// for (SearchHit hit : searchHits) {
// String sourceAsString = hit.getSourceAsString();
// TagNavigationESDO esdo = JSON.parseObject(sourceAsString, TagNavigationESDO.class);
// resultList.add(esdo);
// }
// } catch (IOException e) {
// e.printStackTrace();
// }
// return resultList;
// }
//
// /**
// * 批量保存或更新文档块到Elasticsearch
// *
// * @param list
// * @param teamId 团队ID
// * @param bookId 知识库ID
// */
// public void batchSaveOrUpdate(List<TagNavigationESDO> list, Long teamId, Long bookId) {
// String index = INDEX_NAME + teamId + "_" + bookId;
//
// if (CollectionUtils.isEmpty(list)) {
// return;
// }
//
// try {
// // 检查并创建索引
// if (!indexExists(index)) {
// org.elasticsearch.action.admin.indices.create.CreateIndexRequest createIndexRequest =
// new org.elasticsearch.action.admin.indices.create.CreateIndexRequest(index);
// client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
// }
// } catch (Exception e) {
// log.warn("ES批量保存校验索引是否存在异常," + e.getMessage());
// }
//
// // 批量处理
// for (List<TagNavigationESDO> batch : ListUtil.partition(list, 2000)) {
// BulkRequest bulkRequest = new BulkRequest();
//
// for (TagNavigationESDO esdo : batch) {
// String id = String.valueOf(esdo.getFileOriginId()); // 获取文档 ID
// // 创建更新请求
// String jsonDoc = JSONUtil.toJsonStr(esdo);
// IndexRequest indexRequest = new IndexRequest(index).id(id).source(jsonDoc, XContentType.JSON);
// UpdateRequest updateRequest =
// new UpdateRequest(index, id).doc(jsonDoc, XContentType.JSON).upsert(indexRequest);
// bulkRequest.add(updateRequest);
//
// }
//
// // 执行批量请求
// executeBulkRequest(bulkRequest);
// }
// }
//
// /**
// * 根据文件原始ID删除分块数据
// *
// * @param fileOriginIds 文件原始ID
// */
// public void batchDeleteByFileOriginIds(List<Long> fileOriginIds) {
// String index = INDEX_NAME + "*";
//
// // 使用 DeleteByQueryRequest 删除匹配的文档
// DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(index);
// deleteByQueryRequest.setQuery(QueryBuilders.termsQuery("fileOriginId", fileOriginIds));
// deleteByQueryRequest.setConflicts("proceed");
// try {
// // 执行删除请求
// BulkByScrollResponse response = client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
// log.info("deleteByQuery: {}, response:{}", index, response);
// } catch (IOException e) {
// log.error(e.getMessage(), e);
// throw FrameworkException.instance("es数据更新失败");
// } catch (RuntimeException e) {
// try {
// client = EsClientConfig.rebuildByException(e, client, this.getClass());
// client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
// } catch (IOException ioException) {
// log.error("es 重试 update IOException :{}", ioException.getMessage());
// throw FrameworkException.instance("es重试更新IOException");
// }
// }
// }
//
// private void executeBulkRequest(BulkRequest bulkRequest) {
// if (bulkRequest.numberOfActions() >= 1) {
// try {
// client.bulk(bulkRequest, RequestOptions.DEFAULT);
// } catch (IOException e) {
// log.error("Bulk request failed: {}", e.getMessage());
// // 处理重试逻辑
// handleBulkRequestRetry(bulkRequest);
// }
// }
// }
//
// private void handleBulkRequestRetry(BulkRequest bulkRequest) {
// try {
// client = EsClientConfig.rebuildByException(new RuntimeException("Retrying bulk request"), client,
// this.getClass());
// client.bulk(bulkRequest, RequestOptions.DEFAULT);
// } catch (IOException e) {
// log.error("ES重试更新IOException: {}", e.getMessage());
// throw FrameworkException.instance("ES重试更新IOException");
// }
// }
//
// /**
// * 检查索引是否存在
// *
// * @param indexName
// * @return
// * @throws Exception
// */
// private boolean indexExists(String indexName) throws Exception {
// return client.indices().exists(new GetIndexRequest(indexName), RequestOptions.DEFAULT);
// }
//
// }
