package com.qnvip.qwen.dal.dto;

import java.time.LocalDateTime;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件任务进度表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件任务进度表")
public class FileTaskProgressDTO extends PageParam {

    @ApiModelProperty("任务进度Id")
    private Long id;

    @ApiModelProperty("上一任务id")
    private Long beforeProgressId;



    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70图谱结构化 80完结")
    private Integer nowStep;

    @ApiModelProperty("状态 0待开始 1进行中 2已结束 3异常")
    private Integer status;

    @ApiModelProperty("处理开始时间")
    private LocalDateTime processStartTime;

    @ApiModelProperty("处理结束时间")
    private LocalDateTime processFinishTime;

    @ApiModelProperty("重试次数")
    private Integer retryTimes;

    @ApiModelProperty("最大重试次数")
    private Integer maxRetryTimes;

    @ApiModelProperty("错误发生时间")
    private LocalDateTime errTime;

    @ApiModelProperty("错误信息")
    private String errMsg;
}
