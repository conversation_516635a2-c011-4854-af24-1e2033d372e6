package com.qnvip.qwen.dal.dao;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileOriginDTO;
import com.qnvip.qwen.dal.dto.FileOriginQueryDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;

/**
 * 原始文件表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
public interface FileOriginDaoService extends BaseService<FileOriginDO> {

    IPage<FileOriginDTO> page(IPage<FileOriginDO> page, FileOriginQueryDTO query);

    Map<String, Long> getMapByBookIdAndUid(Long bookId, List<String> docUidList);

    Map<Long, FileOriginDO> getMapByIds(List<Long> ids);

    FileOriginDO getByFileOrigin(Long teamId, Long bookId, String docUid);

    List<FileOriginDO> getListByDocUids(List<String> docUids);

    void deleteByDocUids(List<String> docUids);

    List<FileOriginDO> getListByBookId(Long bookId);

    List<String> findUidByFileOriginIds(List<Long> fileOriginIds);

    List<FileOriginDO> listByParentId(Long id);
}
