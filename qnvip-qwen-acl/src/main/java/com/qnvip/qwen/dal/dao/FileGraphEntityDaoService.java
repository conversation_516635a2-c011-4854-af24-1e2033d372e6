package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.FileGraphEntityDTO;
import com.qnvip.qwen.dal.dto.FileGraphEntityQueryDTO;
import com.qnvip.qwen.dal.entity.FileGraphEntityDO;

/**
 * 文件图数据表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:44
 */
public interface FileGraphEntityDaoService {

    IPage<FileGraphEntityDTO> page(IPage<FileGraphEntityDO> page, FileGraphEntityQueryDTO query);

    void deleteByFileOriginId(Long fileOriginId);

    void deleteByFileOriginIds(List<Long> fileOriginIds);

    List<FileGraphEntityDO> listByFileId(Long fileId);

    List<FileGraphEntityDO> listSimpleByEntityName(List<String> entityNames);
}
