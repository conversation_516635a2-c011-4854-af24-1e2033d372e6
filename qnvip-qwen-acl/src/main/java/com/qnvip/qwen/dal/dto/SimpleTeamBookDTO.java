package com.qnvip.qwen.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 知识库表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("知识库表")
public class SimpleTeamBookDTO {

    @ApiModelProperty("知识库Id")
    private Long id;

    @ApiModelProperty("团队Id")
    private Long teamId;

    @ApiModelProperty("知识库名称")
    private String name;

}
