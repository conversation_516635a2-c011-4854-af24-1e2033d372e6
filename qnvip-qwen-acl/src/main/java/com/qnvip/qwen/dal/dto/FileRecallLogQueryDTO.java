package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件召回日志表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件召回日志表")
public class FileRecallLogQueryDTO extends PageParam {

    @ApiModelProperty("日志Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("问题Id")
    private Long questionId;

    @ApiModelProperty("召回来源1es 2milvus")
    private Integer recallSource;

    @ApiModelProperty("召回阶段10导航召回 20内容召回 30周围分块召回 40重排序召回")
    private Integer recallStage;

    @ApiModelProperty("原始文件Id")
    private String fileOriginId;

    @ApiModelProperty("翻译文件Id")
    private String fileTranslateId;

    @ApiModelProperty("分块文件Id")
    private String fileChunkId;

    @ApiModelProperty("问答文件Id")
    private String fileQaId;
}
