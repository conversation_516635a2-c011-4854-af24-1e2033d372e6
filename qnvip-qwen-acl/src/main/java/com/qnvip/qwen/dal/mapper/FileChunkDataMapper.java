package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;

/**
 * 文件切块数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@Mapper
public interface FileChunkDataMapper extends CustomBaseMapper<FileChunkDataDO> {

    @Delete("delete from qw_file_chunk_data where file_origin_id = #{fileOriginId}")
    int deleteByFileOriginId(@Param("fileOriginId") long fileOriginId);

    @Delete("delete from qw_file_chunk_data where file_origin_id in (${fileOriginIds})")
    void deleteByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);

}