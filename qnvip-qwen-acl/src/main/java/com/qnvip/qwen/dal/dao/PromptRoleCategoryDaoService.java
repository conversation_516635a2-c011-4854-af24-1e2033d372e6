package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryDTO;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryQueryDTO;
import com.qnvip.qwen.dal.entity.PromptRoleCategoryDO;

/**
 * 角色提示词分类表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
public interface PromptRoleCategoryDaoService {

    IPage<PromptRoleCategoryDTO> page(IPage<PromptRoleCategoryDO> page, PromptRoleCategoryQueryDTO query);

}
