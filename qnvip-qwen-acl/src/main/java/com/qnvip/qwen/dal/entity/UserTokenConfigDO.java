package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 用户token用量配置表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
@Data
@TableName("qw_user_token_config")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserTokenConfigDO extends BaseDO {

    @ApiModelProperty("团队Id")
    @TableId(type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("权限授权token")
    private Long permissionAuthorizedToken;

    @ApiModelProperty("月度已使用的token")
    private Long usedToken;
}
