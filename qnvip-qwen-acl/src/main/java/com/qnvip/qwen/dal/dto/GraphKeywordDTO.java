package com.qnvip.qwen.dal.dto;

import java.util.List;

import lombok.Data;

@Data
public class GraphKeywordDTO {

    private List<String> highLevelKeywords;
    private List<String> lowLevelKeywords;

    public String toHighLevelKeywordsStr() {
        return String.join(" ", highLevelKeywords);
    }

    public String toLowLevelKeywordsStr() {
        return String.join(" ", lowLevelKeywords);
    }
}
