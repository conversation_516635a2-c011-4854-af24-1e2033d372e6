package com.qnvip.qwen.dal.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.mapper.sqlProvider.FileQaSqlProvider;

/**
 * 文档QA表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Mapper
public interface FileQaMapper extends CustomBaseMapper<FileQaDO> {

    @Delete("delete from qw_file_qa where file_origin_id=#{fileOriginId}")
    int deleteByFileOriginId(@Param("fileOriginId") Long fileOriginId);

    @Select("select file_origin_id , file_chunk_uid ,uid from qw_file_qa where uid in (${uIds})")
    List<FileQaDO> getChunkUIdListByUIds(@Param("uIds") String uIds);

    @SelectProvider(type = FileQaSqlProvider.class, method = "milvusInitList")
    List<RagChunkDTO> getMilvusInitList(@Param("fileOriginIds") List<Long> fileOriginIds);

    @Delete("delete from qw_file_qa where file_origin_id in (${fileOriginIds})")
    void deleteByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);
}