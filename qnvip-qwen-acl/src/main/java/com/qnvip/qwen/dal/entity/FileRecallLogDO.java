package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件召回日志表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Data
@TableName("qw_file_recall_log")
@NoArgsConstructor
public class FileRecallLogDO extends BaseDO {

    @ApiModelProperty("日志Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("问题Id")
    private Long questionId;

    @ApiModelProperty("召回来源")
    private String recallFrom;

    @ApiModelProperty("召回类型hasPermit,noPermit")
    private String recallType;

    @ApiModelProperty("召回阶段10导航召回 20内容召回 30周围分块召回 40重排序召回")
    private Integer recallStage;

    @ApiModelProperty("库id")
    private String bookId;

    @ApiModelProperty("原始文件Id")
    private String fileOriginId;

    @ApiModelProperty("翻译文件Id")
    private String fileTranslateId;

    @ApiModelProperty("分块文件uId")
    private String fileChunkUid;


    @ApiModelProperty("问答文件uId")
    private String fileQaUid;
}
