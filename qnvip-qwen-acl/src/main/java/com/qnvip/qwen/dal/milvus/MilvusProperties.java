package com.qnvip.qwen.dal.milvus;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

@ConfigurationProperties(prefix = MilvusProperties.PREFIX)
@Data
public class MilvusProperties {

    public static final String PREFIX = "milvus";

    /**
     * milvus ip addr
     */
    private String ipAddr;

    /**
     * milvus port
     */
    private Integer port;

    private String userName;

    private String password;

    /**
     * 向量计算模型地址 http://localhost:8001/get_text_embedding
     */
    private String embeddingClientUrl;

    /**
     * 文本切块 127.0.0.1:8002/load_and_split
     *
     */
    private String fileChunkClientUrl;

}
