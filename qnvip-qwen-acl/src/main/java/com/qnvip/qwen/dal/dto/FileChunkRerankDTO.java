package com.qnvip.qwen.dal.dto;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月01日 16:07:00
 */
@Data
public class FileChunkRerankDTO {
    @ApiModelProperty("文件切块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("文件切块内容")
    private String content;

    private String query;

    private BigDecimal score;

    private Integer sort;

    public String toPrintStr() {
        Map<String, Object> tmp = new HashMap<>();
        tmp.put("fileOriginId", fileOriginId);
        tmp.put("chunkUid", chunkUid);
        if (score != null) {
            tmp.put("score", score);
        }
        return JSON.toJSONString(tmp);
    }
}
