package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件标签表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件标签表")
public class FileTagQueryDTO extends PageParam {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("文件Id")
    private Long originId;

    @ApiModelProperty("标签Id")
    private String name;
}
