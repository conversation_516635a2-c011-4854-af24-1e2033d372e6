package com.qnvip.qwen.dal.dao.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileTaskProgressDaoService;
import com.qnvip.qwen.dal.dto.FileTaskProgressJobQueryDTO;
import com.qnvip.qwen.dal.entity.BaseDO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;
import com.qnvip.qwen.dal.mapper.FileTaskProgressMapper;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件任务进度表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileTaskProgressDaoServiceImpl extends BaseServiceImpl<FileTaskProgressMapper, FileTaskProgressDO>
    implements FileTaskProgressDaoService {
    @Resource
    private FileTaskProgressMapper fileTaskProgressMapper;

    @Override
    public List<Long> findByStatus(Long bookId, Integer status, Integer limit) {
        List<FileTaskProgressDO> list = lambdaQuery().select(FileTaskProgressDO::getId)
            .eq(bookId != null, FileTaskProgressDO::getBookId, bookId).eq(FileTaskProgressDO::getStatus, status)
            .orderByAsc(FileTaskProgressDO::getNowStep).last("limit " + limit).list();
        return list.stream().map(FileTaskProgressDO::getId).collect(Collectors.toList());
    }

    @Override
    public FileTaskProgressDO getById(Long id) {
        return fileTaskProgressMapper.selectById(id);
    }

    @Override
    public List<Long> findByStatusAndStepList(FileTaskProgressJobQueryDTO query) {
        if (query == null || query.getStatus() == null) {
            return new ArrayList<>();
        }
        Long bookId = query.getBookId();
        Integer status = query.getStatus();
        List<Integer> setpList = query.getSetpList();
        Integer limit = query.getLimit();
        Long id = query.getId();
        List<FileTaskProgressDO> list =
            lambdaQuery().select(FileTaskProgressDO::getId).eq(bookId != null, FileTaskProgressDO::getBookId, bookId)
                .in(CollUtil.isNotEmpty(setpList), FileTaskProgressDO::getNowStep, setpList)
                .eq(FileTaskProgressDO::getStatus, status).orderByAsc(FileTaskProgressDO::getNowStep)
                .eq(id != null, BaseDO::getId, id)
                .last("limit " + limit).list();
        return list.stream().map(FileTaskProgressDO::getId).collect(Collectors.toList());
    }

}
