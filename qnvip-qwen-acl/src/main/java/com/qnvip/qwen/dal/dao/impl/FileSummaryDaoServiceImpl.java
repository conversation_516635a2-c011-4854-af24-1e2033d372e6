package com.qnvip.qwen.dal.dao.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileSummaryDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileSummaryDTO;
import com.qnvip.qwen.dal.dto.FileSummaryQueryDTO;
import com.qnvip.qwen.dal.entity.FileSummaryDO;
import com.qnvip.qwen.dal.es.dto.EsNavigationInitDTO;
import com.qnvip.qwen.dal.mapper.FileSummaryMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件概括表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileSummaryDaoServiceImpl extends BaseServiceImpl<FileSummaryMapper, FileSummaryDO>
    implements FileSummaryDaoService {
    @Resource
    private FileSummaryMapper fileSummaryMapper;

    @Override
    public IPage<FileSummaryDTO> page(IPage<FileSummaryDO> page, FileSummaryQueryDTO query) {
        IPage<FileSummaryDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileSummaryDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileSummaryDTO.class));
    }

    @Override
    public List<EsNavigationInitDTO> getEsInitList(DataInitSearchDTO param) {
        return fileSummaryMapper.getEsInitList(param);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileSummaryMapper.deleteByFileOriginIds(StringUtils.join(fileOriginIds, ","));
    }
}
