package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.PermRoleDTO;
import com.qnvip.qwen.dal.dto.PermRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleDO;

/**
 * 角色表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public interface PermRoleDaoService {

    IPage<PermRoleDTO> page(IPage<PermRoleDO> page, PermRoleQueryDTO query);

}
