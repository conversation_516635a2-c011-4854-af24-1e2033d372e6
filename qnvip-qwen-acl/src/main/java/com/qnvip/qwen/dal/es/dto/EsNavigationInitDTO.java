package com.qnvip.qwen.dal.es.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月15日 11:15:00
 */
@Data
public class EsNavigationInitDTO {
    @ApiModelProperty("原始文件id")
    private Long fileOriginId;
    @ApiModelProperty("团队id")
    private Long teamId;
    @ApiModelProperty("知识库Id")
    private Long bookId;
    @ApiModelProperty("目录")
    private String tocs;
    @ApiModelProperty("概要")
    private String summary;

    private String teamName;
}
