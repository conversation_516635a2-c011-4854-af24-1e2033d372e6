package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileSummaryDTO;
import com.qnvip.qwen.dal.dto.FileSummaryQueryDTO;
import com.qnvip.qwen.dal.entity.FileSummaryDO;
import com.qnvip.qwen.dal.es.dto.EsNavigationInitDTO;

/**
 * 文件概括表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
public interface FileSummaryDaoService {

    IPage<FileSummaryDTO> page(IPage<FileSummaryDO> page, FileSummaryQueryDTO query);

    List<EsNavigationInitDTO> getEsInitList(DataInitSearchDTO param);

    void deleteByFileOriginIds(List<Long> fileOriginIds);
}
