package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 原始文件表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("原始文件表")
public class FileOriginQueryDTO extends PageParam {

    @ApiModelProperty("文件Id")
    private Long id;

    @ApiModelProperty("文件校验码")
    private String checksum;

    @ApiModelProperty("语雀原始文件URL")
    private String targetOriginUrl;

    @ApiModelProperty("文件类型 1:本地文件 2:语雀文件")
    private Integer type;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件扩展名")
    private String fileExtension;

    @ApiModelProperty("文件大小（字节）")
    private Long fileSize;

    @ApiModelProperty("团队Id")
    private Long teamId;

    @ApiModelProperty("知识库Id")
    private Long bookId;

    @ApiModelProperty("文档的目录id")
    private Long docCategoryId;

    @ApiModelProperty("文档Id")
    private Long docId;
}

