package com.qnvip.qwen.dal.dao;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamBookDocDataDTO;
import com.qnvip.qwen.dal.dto.TeamBookDocDataQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;

/**
 * 文档表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
public interface TeamBookDocDataDaoService extends BaseService<TeamBookDocDataDO> {

    IPage<TeamBookDocDataDTO> page(IPage<TeamBookDocDataDO> page, TeamBookDocDataQueryDTO query);

    Map<String, TeamBookDocDataDO> getMapByDocUIdList(List<String> docIdList, Long bookId);

    Map<String, TeamBookDocDataDO> getMapByBookIdAndMarkList(List<String> markList, Long bookId);

    void deleteByDocUIds(List<String> docIds);

    TeamBookDocDataDO getByBookIdAndUid(Long bookId, String docUid);
}
