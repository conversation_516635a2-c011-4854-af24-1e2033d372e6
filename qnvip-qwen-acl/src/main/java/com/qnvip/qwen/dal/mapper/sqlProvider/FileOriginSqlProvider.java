package com.qnvip.qwen.dal.mapper.sqlProvider;


import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 原始文件表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
public class FileOriginSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_file_origin where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
