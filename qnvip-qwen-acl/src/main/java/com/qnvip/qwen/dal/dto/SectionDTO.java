package com.qnvip.qwen.dal.dto;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年03月28日 11:38:00
 */
@Data
public class SectionDTO {
    private String title;
    private String content;
    private List<SectionDTO> subSectionDTOS;

    public SectionDTO(String title, String content) {
        this.title = title;
        this.content = content;
        this.subSectionDTOS = new ArrayList<>();
    }

    public void addSubSection(SectionDTO sectionDTO) {
        subSectionDTOS.add(sectionDTO);
    }

    public String getProcessContent() {
        StringBuilder sb = new StringBuilder();
        if (CollUtil.isEmpty(subSectionDTOS)) {
            return sb.append(content).toString();
        }
        for (SectionDTO subSectionDTO : subSectionDTOS) {
            sb.append(subSectionDTO.getTitleContent()); // 递归打印子章节
        }
        return sb.toString();
    }

    public String getTitleContent() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.title).append("\n");
        if (CollUtil.isEmpty(subSectionDTOS)) {
            return sb.append(content).toString();
        }
        for (SectionDTO subSectionDTO : subSectionDTOS) {
            sb.append(subSectionDTO.getTitleContent()); // 递归打印子章节
        }
        return sb.toString();
    }
}
