package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileRecallLogDaoService;
import com.qnvip.qwen.dal.dto.FileRecallLogDTO;
import com.qnvip.qwen.dal.dto.FileRecallLogQueryDTO;
import com.qnvip.qwen.dal.entity.FileRecallLogDO;
import com.qnvip.qwen.dal.mapper.FileRecallLogMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件召回日志表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileRecallLogDaoServiceImpl extends BaseServiceImpl<FileRecallLogMapper, FileRecallLogDO>
    implements FileRecallLogDaoService {
    @Resource
    private FileRecallLogMapper fileRecallLogMapper;

    @Override
    public IPage<FileRecallLogDTO> page(IPage<FileRecallLogDO> page, FileRecallLogQueryDTO query) {
        IPage<FileRecallLogDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileRecallLogDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileRecallLogDTO.class));
    }
}
