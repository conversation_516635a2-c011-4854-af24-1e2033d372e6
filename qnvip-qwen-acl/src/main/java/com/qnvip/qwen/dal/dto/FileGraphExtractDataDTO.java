package com.qnvip.qwen.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件图数据抽取
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/17 09:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件图数据表")
public class FileGraphExtractDataDTO {

    private Long bookId;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("文件块唯一标识")
    private String chunkUid;

    /// 实体分类
    /// @see com.qnvip.qwen.enums.CategoryEnum
    private String type;

    /// 实体属性-名字
    private String entName;
    /// 实体属性-类型 ["组织", "人物", "位置", "事件","类别","技术","时间","任务","概念","项目","问题","解决方案","文档","业务线"]
    private String entType;
    /// 实体属性-描述
    private String entDesc;

    /// 关系属性-源实体
    private String src;
    /// 关系属性-目标实体
    private String tgt;
    /// 关系属性-关系描述
    private String relDesc;
    /// 关系属性-关键词
    private String relKeys;
    /// 关系属性-关系强度 1-10分
    private Integer relStrength;
    /// 关键词
    private String highLevelKeywords;

}
