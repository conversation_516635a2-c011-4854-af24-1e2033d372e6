package com.qnvip.qwen.dal.dao.impl;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.ChatHistoryDaoService;
import com.qnvip.qwen.dal.dto.ChatHistoryDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDO;
import com.qnvip.qwen.dal.mapper.ChatHistoryMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话记录表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatHistoryDaoServiceImpl extends BaseServiceImpl<ChatHistoryMapper, ChatHistoryDO>
    implements ChatHistoryDaoService {

    @Resource
    private ChatHistoryMapper chatHistoryMapper;

    @Override
    public IPage<ChatHistoryDTO> page(IPage<ChatHistoryDO> page, ChatHistoryQueryDTO query) {
        IPage<ChatHistoryDO> pageData = lambdaQuery().setEntity(CopierUtil.copy(query, ChatHistoryDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, ChatHistoryDTO.class));
    }

    @Override
    public List<ChatHistoryDO> loadLatestLimit(ChatHistoryQueryDTO queryDTO) {

        List<ChatHistoryDO> list = lambdaQuery().setEntity(CopierUtil.copy(queryDTO, ChatHistoryDO.class))
            .orderByDesc(ChatHistoryDO::getId).last("limit " + queryDTO.getLimit()).list();

        return list.stream().sorted(Comparator.comparing(ChatHistoryDO::getId)).collect(Collectors.toList());
    }
}
