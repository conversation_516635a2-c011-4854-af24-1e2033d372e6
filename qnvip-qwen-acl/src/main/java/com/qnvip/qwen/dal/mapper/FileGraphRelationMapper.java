package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Mapper;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.FileGraphRelationDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * 文件图数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
@Mapper
public interface FileGraphRelationMapper extends CustomBaseMapper<FileGraphRelationDO> {

    @Select("SELECT "
            + "link_sort_source_target, "
            + "ROUND(AVG(strength)) AS strength "
            + "FROM ( "
            + "SELECT "
            + "id, "
            + "link_sort_source_target, "
            + "strength, "
            + "ROW_NUMBER() OVER ( "
            + "PARTITION BY link_sort_source_target "
            + "ORDER BY strength DESC "
            + ") AS rn "
            + "FROM qw_file_graph_relation "
            + "WHERE "
            + "(#{bookIds} IS NULL OR book_id IN "
            + "<foreach collection='bookIds' item='id' open='(' separator=',' close=')'> "
            + "#{id} "
            + "</foreach> "
            + ") "
            + "AND (#{excludedChunkIds} IS NULL OR chunk_uid NOT IN "
            + "<foreach collection='excludedChunkIds' item='id' open='(' separator=',' close=')'> "
            + "#{id} "
            + "</foreach> "
            + ") "
            + "AND link_sort_source_target IN "
            + "<foreach collection='allEdges' item='edge' open='(' separator=',' close=')'> "
            + "#{edge} "
            + "</foreach> "
            + ") AS ranked "
            + "WHERE rn <= 10 "
            + "GROUP BY link_sort_source_target")
    List<FileGraphRelationDO> selectTopNByLinkSortSourceTarget(
            @Param("bookIds") List<Long> bookIds,
            @Param("excludedChunkIds") List<String> excludedChunkIds,
            @Param("allEdges") Set<String> allEdges);
}