package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.FileTagDTO;
import com.qnvip.qwen.dal.dto.FileTagQueryDTO;
import com.qnvip.qwen.dal.entity.FileTagDO;

/**
 * 文件标签表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
public interface FileTagDaoService {

    IPage<FileTagDTO> page(IPage<FileTagDO> page, FileTagQueryDTO query);

    List<FileTagDO> getListByFileOriginId(Long fileOriginId);

    List<FileTagDO> getListByFileOriginIds(List<Long> fileOriginIds);

    void deleteByFileOriginIds(List<Long> fileOriginIds);
}
