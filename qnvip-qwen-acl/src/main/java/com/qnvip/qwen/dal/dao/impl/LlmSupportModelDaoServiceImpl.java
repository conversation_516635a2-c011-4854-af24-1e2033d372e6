package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.LlmSupportModelDaoService;
import com.qnvip.qwen.dal.dto.LlmSupportModelDTO;
import com.qnvip.qwen.dal.dto.LlmSupportModelQueryDTO;
import com.qnvip.qwen.dal.entity.LlmSupportModelDO;
import com.qnvip.qwen.dal.mapper.LlmSupportModelMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 支持的模型表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LlmSupportModelDaoServiceImpl extends BaseServiceImpl<LlmSupportModelMapper, LlmSupportModelDO>
    implements LlmSupportModelDaoService {
    @Resource
    private LlmSupportModelMapper llmSupportModelMapper;

    @Override
    public IPage<LlmSupportModelDTO> page(IPage<LlmSupportModelDO> page, LlmSupportModelQueryDTO query) {
        IPage<LlmSupportModelDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, LlmSupportModelDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, LlmSupportModelDTO.class));
    }
}
