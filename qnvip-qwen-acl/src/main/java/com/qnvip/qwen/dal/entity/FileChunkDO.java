package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件切块表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
@Data
@TableName("qw_file_chunk")
public class FileChunkDO extends BaseDO {


    @ApiModelProperty("唯一标识")
    private String uid;

    private Long bookId;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("翻译文件Id")
    private Long fileTranslateId;

    @ApiModelProperty("排序")
    private Integer sort;
}

