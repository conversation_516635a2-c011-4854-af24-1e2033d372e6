package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户token消耗表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("用户token消耗表")
public class UserTokenConsumptionQueryDTO extends PageParam {


    @ApiModelProperty("自增主键Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("用户输入预览")
    private String userInputPrefix;

    @ApiModelProperty("应用id")
    private String appId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("消耗总token")
    private Long totalConsumedToken;

    @ApiModelProperty("消耗输入token")
    private Long inputConsumedToken;

    @ApiModelProperty("消耗输出token")
    private Long outputConsumedToken;
}

