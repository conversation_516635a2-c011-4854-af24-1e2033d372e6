package com.qnvip.qwen.dal.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
@Data
@TableName("qw_team_book_doc_data")
public class TeamBookDocDataDO extends BaseDO {

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("所属文档uid")
    private String docUid;

    private Long bookId;

    @ApiModelProperty("第三方文档标识")
    private String mark;

    @ApiModelProperty("文档类型")
    private String type;

    @ApiModelProperty("内容更新时间")
    private LocalDateTime contentUpdateTime;

    @ApiModelProperty("文章或者目录名字")
    private String data;
}
