package com.qnvip.qwen.dal.entity.open;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qnvip.qwen.dal.entity.BaseDO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月22日 16:12:00
 */
@Data
@TableName("qw_open_item_keyword")
public class ItemKeywordDO extends BaseDO {

    @ApiModelProperty("平台id 1-租赁 2-分期")
    private Long platformId;

    @ApiModelProperty("关键词")
    private String keyword;
}
