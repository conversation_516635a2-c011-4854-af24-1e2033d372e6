package com.qnvip.qwen.dal.dao.impl;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dto.FileOriginDTO;
import com.qnvip.qwen.dal.dto.FileOriginQueryDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.mapper.FileOriginMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 原始文件表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileOriginDaoServiceImpl extends BaseServiceImpl<FileOriginMapper, FileOriginDO>
    implements FileOriginDaoService {
    @Resource
    private FileOriginMapper fileOriginMapper;


    @Override
    public IPage<FileOriginDTO> page(IPage<FileOriginDO> page, FileOriginQueryDTO query) {
        IPage<FileOriginDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileOriginDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileOriginDTO.class));
    }

    @Override
    public Map<String, Long> getMapByBookIdAndUid(Long bookId, List<String> docUidList) {
        if (bookId == null || CollUtil.isEmpty(docUidList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<FileOriginDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileOriginDO::getBookId, bookId);
        queryWrapper.in(FileOriginDO::getDocUid, docUidList);
        List<FileOriginDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(FileOriginDO::getDocUid, FileOriginDO::getId, (k1, k2) -> k2));
    }

    @Override
    public Map<Long, FileOriginDO> getMapByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<FileOriginDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FileOriginDO::getId, ids);
        List<FileOriginDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(FileOriginDO::getId, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public FileOriginDO getByFileOrigin(Long teamId, Long bookId, String docUid) {
        return lambdaQuery().eq(FileOriginDO::getTeamId, teamId).eq(FileOriginDO::getBookId, bookId)
            .eq(FileOriginDO::getDocUid, docUid).last("limit 1").one();
    }

    @Override
    public List<FileOriginDO> getListByDocUids(List<String> docUids) {
        if (CollUtil.isEmpty(docUids)) {
            return null;
        }
        LambdaQueryWrapper<FileOriginDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FileOriginDO::getDocUid, docUids);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteByDocUids(List<String> docUids) {
        if (CollUtil.isEmpty(docUids)) {
            return;
        }
        LambdaQueryWrapper<FileOriginDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FileOriginDO::getDocUid, docUids);
        this.remove(queryWrapper);
    }

    @Override
    public List<FileOriginDO> getListByBookId(Long bookId) {
        if (bookId == null) {
            return null;
        }
        LambdaQueryWrapper<FileOriginDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileOriginDO::getBookId, bookId);
        return this.list(queryWrapper);
    }

    @Override
    public List<String> findUidByFileOriginIds(List<Long> fileOriginIds) {
        List<FileOriginDO> list = lambdaQuery().in(FileOriginDO::getId, fileOriginIds).list();
        return list.stream().map(FileOriginDO::getDocUid).collect(Collectors.toList());
    }

    @Override
    public List<FileOriginDO> listByParentId(Long fileOriginId) {
        List<FileOriginDO> list = lambdaQuery().eq(FileOriginDO::getParentId, fileOriginId).list();
        return list.stream().collect(Collectors.toList());
    }
}
