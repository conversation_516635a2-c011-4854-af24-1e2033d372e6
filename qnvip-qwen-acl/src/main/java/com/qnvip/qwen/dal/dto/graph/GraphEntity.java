package com.qnvip.qwen.dal.dto.graph;

import java.util.List;

import lombok.Data;

/**
 * 实体
 *
 */
@Data
public class GraphEntity {
    /// 筛选过滤用 使用索引 多个追加成数组
    private String category = "entity";

    /// 实体名字
    private String entityName;

    /// 过滤用 更新时 追加数组去重
    private List<Long> bookId;
    /// 过滤用 更新时 追加数组去重
    private List<Long> fileOriginId;
    /// 过滤用 更新时 追加数组去重
    private List<String> fileChunkUid;

    /// 实体类型 ["组织", "人物", "位置", "事件","类别","技术","时间","任务","概念","项目","问题","解决方案","文档","业务线"]
    private String entityType;

    /// 冗余字段
    private Integer graphScore;
    private Float vdbScore;
}
