package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 对话记录表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Data
@TableName("qw_chat_history_data")
@NoArgsConstructor
public class ChatHistoryDataDO extends BaseDO {

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("内容")
    private String data;
}
