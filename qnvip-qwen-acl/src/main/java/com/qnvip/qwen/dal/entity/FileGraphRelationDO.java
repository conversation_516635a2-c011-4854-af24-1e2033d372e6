package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件图数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
@Data
@TableName("qw_file_graph_relation")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileGraphRelationDO extends BaseDO {

    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("文件块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("知识库id")
    private Long bookId;

    @ApiModelProperty("源实体")
    private String source;

    @ApiModelProperty("目标实体")
    private String target;

    @ApiModelProperty("实体和关系sort排序后值")
    private String linkSortSourceTarget;

    @ApiModelProperty("关系词")
    private String keyword;

    @ApiModelProperty("关系强度")
    private Integer strength;

    @ApiModelProperty("关系强度")
    private String description;
}
