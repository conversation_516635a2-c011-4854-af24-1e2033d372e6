package com.qnvip.qwen.dal.dto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.codehaus.plexus.util.CollectionUtils;

import lombok.Data;

/**
 * 混合召回数据传输对象 用于存储不同来源的召回结果及相关计算
 */
@Data
public class HybirdRecallDTO {

    /**
     * ES 召回切块ID列表
     */
    private List<String> esRecallChunkUids;

    /**
     * Milvus QA召回切块ID列表
     */
    private List<String> milvusQaRecallChunkUids;

    /**
     * Milvus Chunk召回切块ID列表
     */
    private List<String> milvusChunkRecallChunkUids;

    /**
     * 图数据库召回切块ID列表
     */
    private List<String> graphRecallChunkUids;

    /**
     * 重排序结果切块ID列表
     */
    private List<String> rerankRecallChunkUids;

    /**
     * 获取所有来源的切块ID集合
     *
     * @return 所有非空召回列表的ID合并结果
     */
    public List<String> toTotalChunkUids() {
        List<String> uids = new ArrayList<>();

        // 添加所有非空的召回列表
        addIfNotEmpty(uids, esRecallChunkUids);
        addIfNotEmpty(uids, milvusQaRecallChunkUids);
        addIfNotEmpty(uids, milvusChunkRecallChunkUids);
        addIfNotEmpty(uids, graphRecallChunkUids);

        return uids.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 计算并返回各来源召回结果在重排序结果中的命中率
     *
     * @return 命中率信息字符串，包含各来源的召回数量、命中率和命中元素，每条信息占一行
     */
    public String getCalculateHitRate() {
        // 重排序结果为空时，返回空字符串
        if (ObjectUtils.isEmpty(rerankRecallChunkUids)) {
            return "";
        }

        List<String> hitRateInfos = new ArrayList<>();

        // 处理所有召回来源的命中率计算，结果添加到列表
        appendHitRateInfo(hitRateInfos, "esRecall", esRecallChunkUids);
        appendHitRateInfo(hitRateInfos, "milvusQaRecall", milvusQaRecallChunkUids);
        appendHitRateInfo(hitRateInfos, "milvusChunkRecall", milvusChunkRecallChunkUids);
        appendHitRateInfo(hitRateInfos, "graphRecall", graphRecallChunkUids);
        appendHitRateInfo(hitRateInfos, "rerank", rerankRecallChunkUids);
        // 用换行符连接所有命中率信息
        return String.join("\n", hitRateInfos);
    }

    /**
     * 构建单条命中率信息并添加到列表
     *
     * @param hitRateInfos 命中率信息列表
     * @param sourceName 召回来源名称
     * @param sourceUids 召回来源的ID列表
     */
    private void appendHitRateInfo(List<String> hitRateInfos, String sourceName, List<String> sourceUids) {
        if (ObjectUtils.isEmpty(sourceUids)) {
            return;
        }

        // 计算交集（命中元素）
        Collection<String> hits = CollectionUtils.intersection(rerankRecallChunkUids, sourceUids);
        // 计算结果命中占比
        int hitRateGlobal = (hits.size() * 100) / rerankRecallChunkUids.size();

        // 计算自身命中率
        int hitRate = (hits.size() * 100) / sourceUids.size();

        // 构建单条命中率信息字符串
        String info = String.format("%s 召回 %d个 结果命中占比：%d%% 自身命中率：%d%% 命中元素：%s", sourceName, sourceUids.size(),
            hitRateGlobal, hitRate, hits);
        hitRateInfos.add(info);
    }

    /**
     * 将非空集合添加到目标集合中
     *
     * @param target 目标集合
     * @param source 源集合，可能为null
     */
    private void addIfNotEmpty(List<String> target, List<String> source) {
        if (ObjectUtils.isNotEmpty(source)) {
            target.addAll(source);
        }
    }
}
