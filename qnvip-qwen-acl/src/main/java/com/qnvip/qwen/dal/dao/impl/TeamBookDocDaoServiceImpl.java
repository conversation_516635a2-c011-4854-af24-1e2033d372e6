package com.qnvip.qwen.dal.dao.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qnvip.common.base.PageParam;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dto.TeamBookDocDTO;
import com.qnvip.qwen.dal.dto.TeamBookDocQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.dal.mapper.TeamBookDocMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamBookDocDaoServiceImpl extends BaseServiceImpl<TeamBookDocMapper, TeamBookDocDO>
    implements TeamBookDocDaoService {
    @Resource
    private TeamBookDocMapper teamBookDocMapper;

    @Override
    public IPage<TeamBookDocDTO> page(IPage<TeamBookDocDO> page, TeamBookDocQueryDTO query) {
        IPage<TeamBookDocDO> pageData =
            lambdaQuery().eq(query.getId() != null, TeamBookDocDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, TeamBookDocDTO.class));
    }

    @Override
    public Map<String, Long> getMapByBookIdAndUid(Long bookId, List<String> uidList) {
        if (bookId == null || CollUtil.isEmpty(uidList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamBookDocDO::getBookId, bookId);
        queryWrapper.in(TeamBookDocDO::getUid, uidList);
        List<TeamBookDocDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(TeamBookDocDO::getUid, TeamBookDocDO::getId, (k1, k2) -> k2));
    }

    @Override
    public TeamBookDocDO getByBookIdAndUid(Long bookId, String uid) {
        return lambdaQuery().eq(TeamBookDocDO::getBookId, bookId).eq(TeamBookDocDO::getUid, uid)
            .orderByDesc(TeamBookDocDO::getId).last(" limit 1").one();
    }

    @Override
    public List<TeamBookDocDO> getPageByType(Integer type, PageParam pageParam) {
        if (type == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamBookDocDO::getType, type);
        Page<TeamBookDocDO> page = this.page(queryWrapper, pageParam);
        return page.getRecords();
    }

    @Override
    public Map<String, TeamBookDocDO> getMapByBookIdAndMarkList(Long bookId, List<String> markList) {
        if (bookId == null || CollUtil.isEmpty(markList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDO::getMark, markList);
        queryWrapper.eq(TeamBookDocDO::getBookId, bookId);
        List<TeamBookDocDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(TeamBookDocDO::getMark, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 根据更新时间 获取被删除的文档
     * 
     * @param updateTime
     * @return
     */
    @Override
    public List<TeamBookDocDO> getDeleteListByUpdateTime(LocalDateTime updateTime) {
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(TeamBookDocDO::getUpdateTime, updateTime);
        return this.list(queryWrapper);

    }

    @Override
    public void deleteByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDO::getId, ids);
        this.remove(queryWrapper);
    }

    @Override
    public List<TeamBookDocDO> getListByBookIds(List<Long> bookIds) {
        if (CollUtil.isEmpty(bookIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TeamBookDocDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDO::getBookId, bookIds);
        return this.list(queryWrapper);
    }

    @Override
    public void renewExitsSubDoc() {
        teamBookDocMapper.loadHasChildrenDoc();
    }

    @Override
    public void deleteByDocUIds(List<String> docUids) {
        lambdaUpdate().in(TeamBookDocDO::getUid, docUids).remove();
    }
}
