package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 知识库表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("知识库表")
public class TeamBookDTO extends PageParam {

    @ApiModelProperty("知识库Id")
    private Long id;

    @ApiModelProperty("团队Id")
    private Long teamId;

    @ApiModelProperty("三方唯一标识")
    private String mark;

    @ApiModelProperty("知识库名称")
    private String name;

    @ApiModelProperty("")
    private String description;
}
