package com.qnvip.qwen.dal.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件任务进度表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FileTaskProgressJobQueryDTO {

    @ApiModelProperty("任务进度Id")
    private Long id;

    @ApiModelProperty("知识库id")
    private Long bookId;

    private Integer status;

    private Integer limit;

    @ApiModelProperty("步骤列表")
    private List<Integer> setpList;
}
