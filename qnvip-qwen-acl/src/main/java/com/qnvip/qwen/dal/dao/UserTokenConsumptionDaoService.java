package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionDTO;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConsumptionDO;

/**
 * 用户token消耗表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
public interface UserTokenConsumptionDaoService {

    IPage<UserTokenConsumptionDTO> page(IPage<UserTokenConsumptionDO> page, UserTokenConsumptionQueryDTO query);

}
