package com.qnvip.qwen.dal.redis;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.qnvip.redis.util.RedisUtils;

@Repository
public class ChunkCacheRedisService {

    private static final long EXPIRE_SECOND = 600;

    private static final String EXPIRE_EXCLUDED_CHUNK = "qnvip_qwen:recall:chunk:";

    public void addExcludedChunkIds(String workflowRunId, List<String> values) {
        RedisUtils.setCacheSet(EXPIRE_EXCLUDED_CHUNK + workflowRunId, new HashSet<>(values));
        RedisUtils.expire(EXPIRE_EXCLUDED_CHUNK + workflowRunId, EXPIRE_SECOND);
    }

    public List<String> getExcludedChunkIds(String workflowRunId) {
        return new ArrayList<>(RedisUtils.getCacheSet(EXPIRE_EXCLUDED_CHUNK + workflowRunId));
    }

}
