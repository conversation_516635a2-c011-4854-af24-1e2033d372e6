package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.UserCacheDaoService;
import com.qnvip.qwen.dal.dto.UserCacheDTO;
import com.qnvip.qwen.dal.dto.UserCacheQueryDTO;
import com.qnvip.qwen.dal.entity.UserCacheDO;
import com.qnvip.qwen.dal.mapper.UserCacheMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户缓存表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserCacheDaoServiceImpl extends BaseServiceImpl<UserCacheMapper, UserCacheDO>
    implements UserCacheDaoService {
    @Resource
    private UserCacheMapper userCacheMapper;

    @Override
    public IPage<UserCacheDTO> page(IPage<UserCacheDO> page, UserCacheQueryDTO query) {
        IPage<UserCacheDO> pageData = lambdaQuery().setEntity(CopierUtil.copy(query, UserCacheDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, UserCacheDTO.class));
    }
}
