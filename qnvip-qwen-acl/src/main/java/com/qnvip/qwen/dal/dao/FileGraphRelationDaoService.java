package com.qnvip.qwen.dal.dao;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileGraphRelationDTO;
import com.qnvip.qwen.dal.dto.FileGraphRelationQueryDTO;
import com.qnvip.qwen.dal.dto.graph.GraphRelationship;
import com.qnvip.qwen.dal.entity.FileGraphRelationDO;

/**
 * 文件图数据表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
public interface FileGraphRelationDaoService {

    IPage<FileGraphRelationDTO> page(IPage<FileGraphRelationDO> page, FileGraphRelationQueryDTO query);

    void deleteByFileOriginId(Long fileOriginId);

    void deleteByFileOriginIds(List<Long> fileOriginIds);

    List<FileGraphRelationDO> listSimpleBySourceNameOrTargetName(List<String> entityNames);

    List<Long> getDistinctOriginId(DataInitSearchDTO param);

    Map<String, Integer> mapEdgeRelationshipStrength(List<Long> bookIds, List<String> excludedChunkIds,
        Collection<String> allEdges);


    Map<String, GraphRelationship> mapEdgeRelationship(List<Long> bookIds, List<String> excludedChunkIds, Set<String> allEdges);
}
