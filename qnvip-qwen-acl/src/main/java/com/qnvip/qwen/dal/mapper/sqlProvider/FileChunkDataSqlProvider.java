package com.qnvip.qwen.dal.mapper.sqlProvider;


import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 文件切块数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
public class FileChunkDataSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_file_chunk_data where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
