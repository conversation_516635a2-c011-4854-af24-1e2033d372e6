package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户token消耗表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
@Data
@TableName("qw_user_token_consumption")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserTokenConsumptionDO extends BaseDO {



    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("用户输入预览")
    private String userInputPrefix;

    @ApiModelProperty("应用id")
    private String appId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("消耗总token")
    private Long totalConsumedToken;

    @ApiModelProperty("消耗输入token")
    private Long inputConsumedToken;

    @ApiModelProperty("消耗输出token")
    private Long outputConsumedToken;
}

