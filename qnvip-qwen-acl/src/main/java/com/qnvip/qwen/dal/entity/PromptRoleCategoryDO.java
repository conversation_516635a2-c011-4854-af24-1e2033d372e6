package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 角色提示词分类表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
@Data
@TableName("qw_prompt_role_category")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromptRoleCategoryDO extends BaseDO {

    @ApiModelProperty("主键Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("分类名称")
    private String name;
}
