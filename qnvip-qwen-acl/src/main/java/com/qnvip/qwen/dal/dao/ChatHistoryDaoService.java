package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.ChatHistoryDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDO;

/**
 * 对话记录表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
public interface ChatHistoryDaoService {

    IPage<ChatHistoryDTO> page(IPage<ChatHistoryDO> page, ChatHistoryQueryDTO query);

    List<ChatHistoryDO> loadLatestLimit(ChatHistoryQueryDTO copy);
}
