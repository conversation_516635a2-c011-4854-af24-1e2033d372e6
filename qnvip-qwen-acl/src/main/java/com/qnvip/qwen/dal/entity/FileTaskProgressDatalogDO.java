package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 文件任务进度的产出数据日志表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
@Getter
@Setter
@TableName("qw_file_task_progress_datalog")
public class FileTaskProgressDatalogDO extends BaseDO {


    @ApiModelProperty("任务步骤id")
    private Long taskProgressId;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70图谱结构化 80完结")
    private Integer nowStep;

    @ApiModelProperty("数据")
    private String data;
}
