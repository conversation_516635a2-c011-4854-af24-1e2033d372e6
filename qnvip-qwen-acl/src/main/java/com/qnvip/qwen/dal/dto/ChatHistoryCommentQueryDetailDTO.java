package com.qnvip.qwen.dal.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 聊天记录评论表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("聊天记录评论表")
public class ChatHistoryCommentQueryDetailDTO extends ChatHistoryCommentQueryDTO {


    @ApiModelProperty("消息id")
    private String messageId;

    @ApiModelProperty("是否点赞，1:赞，0:否 -1踩")
    private Integer likeFlag;

}
