package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户token用量配置表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("用户token用量配置表")
public class UserTokenConfigQueryDTO extends PageParam {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("权限授权token")
    private Long permissionAuthorizedToken;

    @ApiModelProperty("月度已使用的token")
    private Long usedToken;
}
