package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件切块数据表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件切块数据表")
public class FileChunkDataQueryDTO extends PageParam {

    @ApiModelProperty("切块ID")
    private Long id;

    @ApiModelProperty("文件切块唯一标识")
    private String chunkUid;

    @ApiModelProperty("文件切块内容")
    private String data;
}

