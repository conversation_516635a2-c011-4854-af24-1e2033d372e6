package com.qnvip.qwen.dal.dao.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.dto.TeamDTO;
import com.qnvip.qwen.dal.dto.TeamQueryDTO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.dal.mapper.TeamMapper;
import com.qnvip.qwen.enums.TeamTypeEnum;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 团队表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamDaoServiceImpl extends BaseServiceImpl<TeamMapper, TeamDO> implements TeamDaoService {
    @Resource
    private TeamMapper teamMapper;

    @Override
    public IPage<TeamDTO> page(IPage<TeamDO> page, TeamQueryDTO query) {
        IPage<TeamDO> pageData = lambdaQuery().eq(query.getId() != null, TeamDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, TeamDTO.class));
    }

    @Override
    public List<TeamDO> getAll(TeamTypeEnum teamType) {
        LambdaQueryWrapper<TeamDO> queryWrapper = new LambdaQueryWrapper<>(TeamDO.class);
        queryWrapper.eq(TeamDO::getType, teamType.getCode());

        return this.list(queryWrapper);
    }

    @Override
    public List<Long> getAllTeamIds() {
        List<TeamDO> list = lambdaQuery().select(TeamDO::getId).list();
        return list.stream().map(TeamDO::getId).collect(Collectors.toList());
    }
}
