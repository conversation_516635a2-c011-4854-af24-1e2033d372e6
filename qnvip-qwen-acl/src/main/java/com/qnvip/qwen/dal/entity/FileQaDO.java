package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档QA表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Data
@TableName("qw_file_qa")
public class FileQaDO extends BaseDO {

    @ApiModelProperty("文档QA Id")
    private Long id;

    @ApiModelProperty("唯一标识")
    private String uid;

    @ApiModelProperty("原文档Id")
    private Long fileOriginId;

    @ApiModelProperty("翻译文档Id")
    private Long fileTranslateId;

    @ApiModelProperty("文档分块Id")
    private String fileChunkUid;
}

