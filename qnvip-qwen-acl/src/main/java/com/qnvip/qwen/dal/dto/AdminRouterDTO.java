package com.qnvip.qwen.dal.dto;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月14日 10:11:00
 */
@Data
public class AdminRouterDTO {

    private Integer id;
    private Integer parentId;
    private String path;
    private String component;
    private String name;
    private String redirect;
    private Integer listOrder;
    private AdminRouterMeta meta;
    private Boolean hidden;

    private List<AdminRouterDTO> children;

    @Data
    public static class AdminRouterMeta {
        private String title;
        private String icon;

        public AdminRouterMeta(String title, String icon) {
            this.title = title;
            this.icon = icon;
        }
    }
}
