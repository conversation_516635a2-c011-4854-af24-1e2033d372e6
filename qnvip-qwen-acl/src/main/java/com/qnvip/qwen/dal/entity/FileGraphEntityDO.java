package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件图数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:44
 */
@Data
@TableName("qw_file_graph_entity")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileGraphEntityDO extends BaseDO {

    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("文件块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("知识库id")
    private Long bookId;

    @ApiModelProperty("实体名字")
    private String name;

    @ApiModelProperty("实体类型")
    private String type;

    @ApiModelProperty("实体描述")
    private String description;
}
