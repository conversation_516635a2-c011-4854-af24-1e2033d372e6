package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamDTO;
import com.qnvip.qwen.dal.dto.TeamQueryDTO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.enums.TeamTypeEnum;

/**
 * 团队表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
public interface TeamDaoService extends BaseService<TeamDO> {

    IPage<TeamDTO> page(IPage<TeamDO> page, TeamQueryDTO query);

    List<TeamDO> getAll(TeamTypeEnum teamType);

    List<Long> getAllTeamIds();
}
