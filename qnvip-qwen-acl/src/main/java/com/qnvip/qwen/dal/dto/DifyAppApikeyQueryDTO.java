package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * dify应用的apikey表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("dify应用的apikey表")
public class DifyAppApikeyQueryDTO extends PageParam {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("difyApiKey")
    private String apiKey;

    @ApiModelProperty("模型id")
    private Long modelId;

    @ApiModelProperty("url")
    private String url;

    @ApiModelProperty("@name:类型, @dicts:[chat]")
    private Integer type;
}
