package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 角色资源表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("角色资源表")
@Builder
public class PermRoleResourceDTO extends PageParam {

    @ApiModelProperty("自增主键Id")
    private Long id;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("资源id -1全部权限")
    private Long resourceId;

    @ApiModelProperty("@name:资源类型, @dicts:[1-知识库资源]")
    private Integer resourceType;
}
