package com.qnvip.qwen.dal.milvus;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import cn.hutool.http.HttpUtil;

public class FileChunkClient {
    public FileChunkClient(String url) {
        this.url = url;
    }

    /**
     * 127.0.0.1:8002/load_and_split
     */
    private String url;

    /**
     * 文本切块
     *
     * @param originFileUrl
     * @return
     */
    public List<String> chunkFile(String originFileUrl) {

        String str = "{\"file_url\": \"" + originFileUrl + "\"}";

        String jsonArrayStr = HttpUtil.post(url, str);
        return JSON.parseObject(jsonArrayStr, new TypeReference<List<String>>() {});
    }

}
