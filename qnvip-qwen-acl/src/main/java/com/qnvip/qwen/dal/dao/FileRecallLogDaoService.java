package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.FileRecallLogDTO;
import com.qnvip.qwen.dal.dto.FileRecallLogQueryDTO;
import com.qnvip.qwen.dal.entity.FileRecallLogDO;

/**
 * 文件召回日志表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
public interface FileRecallLogDaoService {

    IPage<FileRecallLogDTO> page(IPage<FileRecallLogDO> page, FileRecallLogQueryDTO query);

}
