package com.qnvip.qwen.dal.mapper.sqlProvider;

import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import com.qnvip.qwen.dal.dto.DataInitSearchDTO;

import cn.hutool.core.collection.CollUtil;

/**
 * 文件概括表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
public class FileSummarySqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_file_summary where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }

    public String esInitList(Map<String, Object> parameter) {
        DataInitSearchDTO param = (DataInitSearchDTO)parameter.get("param");

        StringBuilder sql = new StringBuilder();
        sql.append(
            "select s.origin_id as fileOriginId,s.data as summary,f.team_id as teamId,f.book_id as bookId,f.tocs,t.name as teamName \n"
                + " from qw_file_summary s \n" + " left join qw_file_origin f on f.id = s.origin_id \n"
                + " left join qw_team t on t.id = f.team_id ");
        sql.append(" where 1=1 and s.is_deleted = 0 and f.is_deleted = 0");

        if (CollUtil.isNotEmpty(param.getTeamIds())) {
            sql.append(" and f.team_id  in (").append(StringUtils.join(param.getTeamIds(), ",")).append(") ");
        }

        if (CollUtil.isNotEmpty(param.getBookIds())) {
            sql.append(" and f.book_id  in (").append(StringUtils.join(param.getBookIds(), ",")).append(") ");
        }
        sql.append(param.getLimitSql());
        return sql.toString();
    }
}
