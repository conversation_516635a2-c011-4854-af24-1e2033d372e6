package com.qnvip.qwen.dal.dao;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.SimpleTeamBookDTO;
import com.qnvip.qwen.dal.dto.TeamBookDTO;
import com.qnvip.qwen.dal.dto.TeamBookQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.enums.TeamTypeEnum;

/**
 * 知识库表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
public interface TeamBookDaoService extends BaseService<TeamBookDO>  {

    IPage<TeamBookDTO> page(IPage<TeamBookDO> page, TeamBookQueryDTO query);

    Map<String, TeamBookDO> getMapByTeamIdAndMark(Long teamId, List<String> mark);

    List<TeamBookDO> getAll(TeamTypeEnum typeEnum);

    Map<Long, List<SimpleTeamBookDTO>> mapAllDto();

    Map<Long, TeamBookDO> getMapByIdList(List<Long> ids);

    List<TeamBookDO> getDeleteListByUpdateTime(LocalDateTime updateTime);

    void deleteByIds(List<Long> ids);

    List<TeamBookDO> getListByTeamId(Long teamId);

}
