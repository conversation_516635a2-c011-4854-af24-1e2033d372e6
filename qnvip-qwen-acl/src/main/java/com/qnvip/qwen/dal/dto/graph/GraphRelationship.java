package com.qnvip.qwen.dal.dto.graph;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 关系 sourceEntity + targetEntity 是唯一存在的
 */
@Data
public class GraphRelationship {
    private String category = "relationship";

    /// 源实体 Entity.entityName
    private String sourceEntity;
    /// 目标实体 Entity.entityName
    private String targetEntity;

    /// 过滤用 更新时 追加数组去重
    private List<Long> bookId;
    /// 过滤用 更新时 追加数组去重
    private List<Long> fileOriginId;
    /// 过滤用 更新时 追加数组去重
    private List<String> fileChunkUid;
    /// 过滤用 更新时 追加数组去重
    private List<String> relationshipKeyword;

    /// 冗余字段
    private Integer rank;
    /// 冗余字段
    private Integer relationshipStrength;


}