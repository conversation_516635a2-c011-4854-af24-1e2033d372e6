package com.qnvip.qwen.dal.dao.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.TeamBookDaoService;
import com.qnvip.qwen.dal.dto.SimpleTeamBookDTO;
import com.qnvip.qwen.dal.dto.TeamBookDTO;
import com.qnvip.qwen.dal.dto.TeamBookQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.dal.mapper.TeamBookMapper;
import com.qnvip.qwen.enums.StatusEnum;
import com.qnvip.qwen.enums.TeamTypeEnum;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 知识库表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamBookDaoServiceImpl extends BaseServiceImpl<TeamBookMapper, TeamBookDO> implements TeamBookDaoService {
    @Resource
    private TeamBookMapper teamBookMapper;

    @Override
    public IPage<TeamBookDTO> page(IPage<TeamBookDO> page, TeamBookQueryDTO query) {
        IPage<TeamBookDO> pageData =
            lambdaQuery().eq(query.getId() != null, TeamBookDO::getId, query.getId())
                .in(query.getIds() != null, TeamBookDO::getId, query.getIds())
                .like(!ObjectUtils.isEmpty(query.getName()), TeamBookDO::getName, query.getName()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, TeamBookDTO.class));
    }

    @Override
    public Map<String, TeamBookDO> getMapByTeamIdAndMark(Long teamId, List<String> markList) {
        if (teamId == null || CollUtil.isEmpty(markList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<TeamBookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamBookDO::getTeamId, teamId);
        queryWrapper.in(TeamBookDO::getMark, markList);
        List<TeamBookDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(TeamBookDO::getMark, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public List<TeamBookDO> getAll(TeamTypeEnum typeEnum) {
        LambdaQueryWrapper<TeamBookDO> queryWrapper = new LambdaQueryWrapper<>(TeamBookDO.class);
        queryWrapper.eq(TeamBookDO::getType, typeEnum.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public Map<Long, List<SimpleTeamBookDTO>> mapAllDto() {
        List<SimpleTeamBookDTO> teamBookDTOS =
            CopierUtil.copyList(this.list(new LambdaQueryWrapper<>(TeamBookDO.class)), SimpleTeamBookDTO.class);
        return teamBookDTOS.stream().collect(Collectors.groupingBy(SimpleTeamBookDTO::getTeamId));
    }

    @Override
    public Map<Long, TeamBookDO> getMapByIdList(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<TeamBookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDO::getId, ids);
        List<TeamBookDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(TeamBookDO::getId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 根据更新时间 获取被删除的知识库
     * 
     * @param updateTime
     * @return
     */
    @Override
    public List<TeamBookDO> getDeleteListByUpdateTime(LocalDateTime updateTime) {
        LambdaQueryWrapper<TeamBookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(TeamBookDO::getUpdateTime, updateTime);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        LambdaUpdateWrapper<TeamBookDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TeamBookDO::getId, ids);
        updateWrapper.set(TeamBookDO::getIsDeleted, StatusEnum.YES.getCode());
        this.update(updateWrapper);
    }

    @Override
    public List<TeamBookDO> getListByTeamId(Long teamId) {
        if (teamId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TeamBookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamBookDO::getTeamId, teamId);
        return this.list(queryWrapper);
    }

}
