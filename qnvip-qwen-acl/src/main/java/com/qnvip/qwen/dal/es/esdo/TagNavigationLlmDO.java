package com.qnvip.qwen.dal.es.esdo;

import java.util.List;

import org.springframework.data.annotation.Id;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * {"summary":"一段话摘要","tags":["文章标签1","文章标签2","文章标签3"]}
 */
@Data
public class TagNavigationLlmDO {

    @Id
    @ApiModelProperty("原始文件id")
    private Long fileOriginId;

    @ApiModelProperty("团队id")
    private Long teamId;
    @ApiModelProperty("知识库Id")
    private Long bookId;
    @ApiModelProperty("目录")
    private List<String> tocs;
    @ApiModelProperty("概要")
    private String summary;
    @ApiModelProperty("文件标签")
    private List<String> tags;

}
