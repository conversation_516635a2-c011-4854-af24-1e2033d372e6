package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.ChatHistoryDataDaoService;
import com.qnvip.qwen.dal.dto.ChatHistoryDataDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryDataQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDataDO;
import com.qnvip.qwen.dal.mapper.ChatHistoryDataMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对话记录表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatHistoryDataDaoServiceImpl extends BaseServiceImpl<ChatHistoryDataMapper, ChatHistoryDataDO>
    implements ChatHistoryDataDaoService {
    @Resource
    private ChatHistoryDataMapper chatHistoryDataMapper;

    @Override
    public IPage<ChatHistoryDataDTO> page(IPage<ChatHistoryDataDO> page, ChatHistoryDataQueryDTO query) {
        IPage<ChatHistoryDataDO> pageData =
            lambdaQuery().eq(query.getId() != null, ChatHistoryDataDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, ChatHistoryDataDTO.class));
    }
}
