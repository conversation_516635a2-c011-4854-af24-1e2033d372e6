package com.qnvip.qwen.dal.dto;


import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件切块数据表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件切块数据表")
public class FileChunkDataDTO extends PageParam {

    @ApiModelProperty("文件切块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("文件切块内容")
    private String data;

    @ApiModelProperty("排序")
    private Integer sort;

    private Long chunkId;
}

