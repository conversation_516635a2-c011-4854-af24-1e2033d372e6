package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 聊天记录评论表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("聊天记录评论表")
@Builder
public class ChatHistoryCommentDTO extends PageParam {

    @ApiModelProperty("评论Id")
    private Long id;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("会话Id")
    private String conversationId;

    @ApiModelProperty("工作流运行Id")
    private String workflowRunId;

    @ApiModelProperty("是否点赞，1:是，0:否")
    private Integer likeFlag;

    @ApiModelProperty("评论内容")
    private String content;
}
