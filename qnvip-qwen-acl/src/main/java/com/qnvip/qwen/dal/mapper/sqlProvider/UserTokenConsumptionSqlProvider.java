package com.qnvip.qwen.dal.mapper.sqlProvider;


import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 用户token消耗表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
public class UserTokenConsumptionSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_user_token_consumption where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
