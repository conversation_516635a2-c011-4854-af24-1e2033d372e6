package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 团队表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("团队表")
public class TeamQueryDTO extends PageParam {

    @ApiModelProperty("团队Id")
    private Long id;

    @ApiModelProperty("团队名称")
    private String name;

    @ApiModelProperty("团队值")
    private String mark;
}
