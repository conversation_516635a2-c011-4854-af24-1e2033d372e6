package com.qnvip.qwen.dal.mapper.sqlProvider;


import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.collection.CollUtil;

/**
 * 文档QA表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
public class FileQaSqlProvider {



    public String milvusInitList(Map<String, Object> parameter) {
        List<Long> fileOriginIds = (List<Long>)parameter.get("fileOriginIds");

        StringBuilder sql = new StringBuilder();
        sql.append(
            "select f.book_id as bookId,q.file_origin_id as fileOriginId,q.uid as qaUid,q.file_chunk_uid as chunkUid,"
                + "qd.question,qd.answer,f.tocs \n"
                + " from qw_file_qa q \n" + " left join qw_file_origin f on f.id = q.file_origin_id \n"
                + " left join qw_file_qa_data qd on qd.qa_uid=q.uid ");
        sql.append(" where  q.is_deleted = 0 and qd.is_deleted = 0 and f.is_deleted = 0 ");

        if (CollUtil.isNotEmpty(fileOriginIds)) {
            sql.append(" and f.id  in (").append(StringUtils.join(fileOriginIds, ",")).append(") ");
        }

        return sql.toString();
    }
}
