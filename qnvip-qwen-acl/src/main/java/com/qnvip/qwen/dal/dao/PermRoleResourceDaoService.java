package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.PermRoleResourceDTO;
import com.qnvip.qwen.dal.dto.PermRoleResourceQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleResourceDO;

/**
 * 角色资源表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public interface PermRoleResourceDaoService {

    IPage<PermRoleResourceDTO> page(IPage<PermRoleResourceDO> page, PermRoleResourceQueryDTO query);

}
