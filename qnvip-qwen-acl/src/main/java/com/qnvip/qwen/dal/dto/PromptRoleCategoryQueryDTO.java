package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 角色提示词分类表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("角色提示词分类表")
public class PromptRoleCategoryQueryDTO extends PageParam {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("分类名称")
    private String name;
}
