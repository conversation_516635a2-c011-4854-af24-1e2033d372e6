package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.UserTokenConfigDaoService;
import com.qnvip.qwen.dal.dto.UserTokenConfigDTO;
import com.qnvip.qwen.dal.dto.UserTokenConfigQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConfigDO;
import com.qnvip.qwen.dal.mapper.UserTokenConfigMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户token用量配置表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTokenConfigDaoServiceImpl extends BaseServiceImpl<UserTokenConfigMapper, UserTokenConfigDO>
    implements UserTokenConfigDaoService {
    @Resource
    private UserTokenConfigMapper userTokenConfigMapper;

    @Override
    public IPage<UserTokenConfigDTO> page(IPage<UserTokenConfigDO> page, UserTokenConfigQueryDTO query) {
        IPage<UserTokenConfigDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, UserTokenConfigDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, UserTokenConfigDTO.class));
    }
}
