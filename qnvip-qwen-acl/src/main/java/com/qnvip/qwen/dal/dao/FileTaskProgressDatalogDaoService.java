package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.FileTaskProgressDatalogDTO;
import com.qnvip.qwen.dal.dto.FileTaskProgressDatalogQueryDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;

/**
 * 文件任务进度的产出数据日志表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
public interface FileTaskProgressDatalogDaoService {

    IPage<FileTaskProgressDatalogDTO> page(IPage<FileTaskProgressDatalogDO> page,
        FileTaskProgressDatalogQueryDTO query);

}
