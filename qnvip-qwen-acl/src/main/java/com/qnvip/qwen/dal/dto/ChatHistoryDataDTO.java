package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 对话记录表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("对话记录表")
@Builder
public class ChatHistoryDataDTO extends PageParam {

    @ApiModelProperty("对话记录Id")
    private Long id;

    @ApiModelProperty("问题Id")
    private Long chatHistoryId;

    @ApiModelProperty("内容")
    private String data;
}
