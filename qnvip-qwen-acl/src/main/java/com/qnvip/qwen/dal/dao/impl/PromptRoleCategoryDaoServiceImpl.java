package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.PromptRoleCategoryDaoService;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryDTO;
import com.qnvip.qwen.dal.dto.PromptRoleCategoryQueryDTO;
import com.qnvip.qwen.dal.entity.PromptRoleCategoryDO;
import com.qnvip.qwen.dal.mapper.PromptRoleCategoryMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色提示词分类表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 14:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PromptRoleCategoryDaoServiceImpl extends BaseServiceImpl<PromptRoleCategoryMapper, PromptRoleCategoryDO>
    implements PromptRoleCategoryDaoService {
    @Resource
    private PromptRoleCategoryMapper promptRoleCategoryMapper;

    @Override
    public IPage<PromptRoleCategoryDTO> page(IPage<PromptRoleCategoryDO> page, PromptRoleCategoryQueryDTO query) {
        IPage<PromptRoleCategoryDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, PromptRoleCategoryDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, PromptRoleCategoryDTO.class));
    }
}
