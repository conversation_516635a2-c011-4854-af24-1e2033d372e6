package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileTaskProgressDatalogDaoService;
import com.qnvip.qwen.dal.dto.FileTaskProgressDatalogDTO;
import com.qnvip.qwen.dal.dto.FileTaskProgressDatalogQueryDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.dal.mapper.FileTaskProgressDatalogMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件任务进度的产出数据日志表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileTaskProgressDatalogDaoServiceImpl
    extends BaseServiceImpl<FileTaskProgressDatalogMapper, FileTaskProgressDatalogDO>
    implements FileTaskProgressDatalogDaoService {
    @Resource
    private FileTaskProgressDatalogMapper fileTaskProgressDatalogMapper;

    @Override
    public IPage<FileTaskProgressDatalogDTO> page(IPage<FileTaskProgressDatalogDO> page,
        FileTaskProgressDatalogQueryDTO query) {
        IPage<FileTaskProgressDatalogDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileTaskProgressDatalogDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileTaskProgressDatalogDTO.class));
    }
}
