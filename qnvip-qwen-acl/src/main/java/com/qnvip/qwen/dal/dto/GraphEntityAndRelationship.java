package com.qnvip.qwen.dal.dto;

import java.util.List;

import lombok.Data;

@Data
public class GraphEntityAndRelationship {

    /// 筛选过滤用 使用索引 多个追加成数组
    private String category;
    /// 筛选过滤用 多个追加成数组
    private List<Long> bookId;
    /// 筛选过滤用 多个追加成数组
    private List<Long> fileOriginId;
    /// 筛选过滤用 多个追加成数组
    private List<String> fileChunkUid;
    /// 实体名字
    private String entityName;
    /// 实体类型 ["组织", "人物", "位置", "事件","类别","技术","时间","任务","概念","项目","问题","解决方案","文档","业务线"]
    private String entityType;
    /// 实体描述
    private String entityDescription;

    /// 源实体 Entity.entityName
    private String sourceEntity;
    /// 目标实体 Entity.entityName
    private String targetEntity;
    /// 关系关键词 Relationship
    private String relationshipKeyword;
    /// 关系描述
    private String relationshipDescription;
    /// 关系强度
    private Integer relationshipStrength;

    /// 冗余字段
    private Integer rank;
}