package com.qnvip.qwen.dal.dto.wechat;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc 微信企业部门信息
 * @createTime 2025年01月27日 10:00:00
 */
@Data
public class WechatDepartmentDTO {

    @JSONField(name = "id")
    private Integer id;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "parentid")
    private Integer parentId;

    @JSONField(name = "order")
    private Integer order;

    @JSONField(name = "department_leader")
    private List<String> departmentLeader;

}