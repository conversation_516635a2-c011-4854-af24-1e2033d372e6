package com.qnvip.qwen.dal.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dto.TeamBookDocDataDTO;
import com.qnvip.qwen.dal.dto.TeamBookDocDataQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.dal.mapper.TeamBookDocDataMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamBookDocDataDaoServiceImpl extends BaseServiceImpl<TeamBookDocDataMapper, TeamBookDocDataDO>
    implements TeamBookDocDataDaoService {
    @Resource
    private TeamBookDocDataMapper teamBookDocDataMapper;

    @Override
    public IPage<TeamBookDocDataDTO> page(IPage<TeamBookDocDataDO> page, TeamBookDocDataQueryDTO query) {
        IPage<TeamBookDocDataDO> pageData =
            lambdaQuery().eq(query.getId() != null, TeamBookDocDataDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, TeamBookDocDataDTO.class));
    }

    @Override
    public Map<String, TeamBookDocDataDO> getMapByDocUIdList(List<String> docUIds, Long bookId) {
        if (CollUtil.isEmpty(docUIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<TeamBookDocDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDataDO::getDocUid, docUIds);
        queryWrapper.in(TeamBookDocDataDO::getBookId, bookId);
        List<TeamBookDocDataDO> list = this.list(queryWrapper);
        return list.stream()
            .collect(Collectors.toMap(TeamBookDocDataDO::getDocUid, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public Map<String, TeamBookDocDataDO> getMapByBookIdAndMarkList(List<String> markList, Long bookId) {
        if (CollUtil.isEmpty(markList) || bookId == null) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<TeamBookDocDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDataDO::getMark, markList);
        queryWrapper.eq(TeamBookDocDataDO::getBookId, bookId);
        List<TeamBookDocDataDO> list = this.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(TeamBookDocDataDO::getMark, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public void deleteByDocUIds(List<String> docUIds) {
        if (CollUtil.isEmpty(docUIds)) {
            return;
        }
        LambdaQueryWrapper<TeamBookDocDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeamBookDocDataDO::getDocUid, docUIds);
        this.remove(queryWrapper);
    }

    @Override
    public TeamBookDocDataDO getByBookIdAndUid(Long bookId, String docUid) {
        return lambdaQuery().eq(TeamBookDocDataDO::getBookId, bookId).eq(TeamBookDocDataDO::getDocUid, docUid)
            .orderByDesc(TeamBookDocDataDO::getId).last(" limit 1").one();
    }
}
