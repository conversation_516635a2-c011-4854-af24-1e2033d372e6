package com.qnvip.qwen.dal.dto.ai;

import java.time.LocalDateTime;
import java.util.Optional;

import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;
import com.qnvip.qwen.enums.TaskStatusEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件处理任务进度 - 核心领域模型
 *
 * <p>
 * 封装文件处理任务的状态转换规则和业务逻辑，维护任务处理的生命周期完整性
 * </p>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskProgressDomainModelDTO {
    // 最大重试次数
    public static final int MAX_RETRIES = 3;
    // 领域标识
    private Long id;
    private Long bookId;
    private Long fileOriginId;
    private FileOriginDO fileOriginDO;
    private Long beforeProgressId;

    // 领域状态
    private TaskStepsEnum nowStep;
    private TaskStatusEnum status;
    // 领域时间记录
    private LocalDateTime processStartTime;
    private LocalDateTime processFinishTime;
    private LocalDateTime errTime;
    // 领域诊断信息
    private int retryTimes;
    private int maxRetryTimes;
    private String errMsg;

    /**
     * 构造函数
     *
     * @param fileOriginId 文件原始ID
     * @param nowStep 当前步骤
     * @param status 当前状态
     */
    public TaskProgressDomainModelDTO(Long bookId, Long fileOriginId, TaskStepsEnum nowStep, TaskStatusEnum status) {
        this.bookId = bookId;
        this.fileOriginId = fileOriginId;
        this.nowStep = nowStep;
        this.status = status;
        this.maxRetryTimes = MAX_RETRIES;
    }

    /**
     * 将DO对象转换为领域模型
     *
     * @param progressDO FileTaskProgressDO对象
     * @return TaskProgressDomainModel对象
     */
    public static TaskProgressDomainModelDTO toDomainModel(FileTaskProgressDO progressDO) {
        TaskProgressDomainModelDTO model = new TaskProgressDomainModelDTO();
        model.setId(progressDO.getId());
        model.setBookId(progressDO.getBookId());
        model.setFileOriginId(progressDO.getFileOriginId());
        model.setNowStep(TaskStepsEnum.getByCode(progressDO.getNowStep()));
        model.setStatus(TaskStatusEnum.getByCode(progressDO.getStatus()));
        model.setProcessStartTime(progressDO.getProcessStartTime());
        model.setProcessFinishTime(progressDO.getProcessFinishTime());
        model.setErrTime(progressDO.getErrTime());
        model.setRetryTimes(progressDO.getRetryTimes());
        model.setMaxRetryTimes(progressDO.getMaxRetryTimes());
        model.setErrMsg(progressDO.getErrMsg());
        model.setBeforeProgressId(progressDO.getBeforeProgressId());
        return model;
    }

    /**
     * 启动任务
     */
    public void start() {
        validateStateTransition(TaskStatusEnum.PENDING);
        this.status = TaskStatusEnum.PROCESSING;
        this.processStartTime = LocalDateTime.now();
    }

    /**
     * 完成任务
     *
     * @return 下一个步骤的任务模型（如果存在）
     */
    public Optional<TaskProgressDomainModelDTO> complete() {
        validateStateTransition(TaskStatusEnum.PROCESSING);
        this.status = TaskStatusEnum.COMPLETED;
        this.processFinishTime = LocalDateTime.now();
        return createNextStep();
    }

    /**
     * 标记任务为失败状态
     *
     * @param errorMessage 错误信息
     */
    public void markAsFailed(String errorMessage) {
        this.status = TaskStatusEnum.FAILED;
        this.errMsg = errorMessage;
        this.errTime = LocalDateTime.now();
    }

    /**
     * 重试任务
     */
    public void retry(String errorMessage) {
        this.status = TaskStatusEnum.PENDING;
        this.retryTimes++;
        this.errMsg = errorMessage;
        this.errTime = LocalDateTime.now();
    }

    /**
     * 创建下一个步骤的任务模型
     *
     * @return 下一个步骤的任务模型（如果存在）
     */
    private Optional<TaskProgressDomainModelDTO> createNextStep() {
        return Optional.ofNullable(this.nowStep.getNextStep())
            .map(nextStep -> new TaskProgressDomainModelDTO(this.getBookId(), this.fileOriginId, nextStep,
                TaskStatusEnum.PENDING));
    }

    /**
     * 验证状态转换是否合法
     *
     * @param expectedCurrentStatus 期望的当前状态
     */
    private void validateStateTransition(TaskStatusEnum expectedCurrentStatus) {
        if (this.status != expectedCurrentStatus) {
            throw new IllegalStateException(
                String.format("无效的状态转换。当前状态: %s, 期望状态: %s", this.status, expectedCurrentStatus));
        }
    }

    /**
     * 检查是否允许重试
     *
     * @return 是否允许重试
     */
    public boolean isRetryAllowed() {
        return this.retryTimes < maxRetryTimes;
    }

    /**
     * 将领域模型转换为DO对象
     *
     * @return FileTaskProgressDO对象
     */
    public FileTaskProgressDO toDo() {
        FileTaskProgressDO model = new FileTaskProgressDO();
        model.setId(this.getId());
        model.setBookId(this.getBookId());
        model.setFileOriginId(this.getFileOriginId());
        model.setNowStep(this.getNowStep().getCode());
        model.setStatus(this.getStatus().getCode());
        model.setProcessStartTime(this.getProcessStartTime());
        model.setProcessFinishTime(this.getProcessFinishTime());
        model.setErrTime(this.getErrTime());
        model.setRetryTimes(this.getRetryTimes());
        model.setMaxRetryTimes(this.getMaxRetryTimes());
        model.setErrMsg(this.getErrMsg());
        model.setBeforeProgressId(this.getBeforeProgressId());
        return model;
    }
}