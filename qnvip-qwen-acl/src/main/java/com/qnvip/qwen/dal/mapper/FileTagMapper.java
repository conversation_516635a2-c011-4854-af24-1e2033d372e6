package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.FileTagDO;

/**
 * 文件标签表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Mapper
public interface FileTagMapper extends CustomBaseMapper<FileTagDO> {

    @Delete("delete from qw_file_tag where origin_id in (${fileOriginIds})")
    void deleteByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);

    @Delete("delete from qw_file_tag where origin_id = #{originId}")
    void deleteByOriginId(@Param("originId") Long originId);

}