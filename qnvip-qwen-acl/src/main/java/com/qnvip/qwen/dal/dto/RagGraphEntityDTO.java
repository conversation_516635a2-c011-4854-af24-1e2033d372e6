package com.qnvip.qwen.dal.dto;

import lombok.Data;

@Data
public class RagGraphEntityDTO {

    private Long bookId;
    private Long fileOriginId;
    private String chunkUid;
    private String entityName;
    private String entityType;
    private String description;
    private Float vdbScore;
    private Float graphScore;

    public String getCompleteChunk() {
        return entityName + "\n" + description;
    }
}
