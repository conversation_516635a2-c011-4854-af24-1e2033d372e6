package com.qnvip.qwen.dal.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileTagDaoService;
import com.qnvip.qwen.dal.dto.FileTagDTO;
import com.qnvip.qwen.dal.dto.FileTagQueryDTO;
import com.qnvip.qwen.dal.entity.FileTagDO;
import com.qnvip.qwen.dal.mapper.FileTagMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件标签表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileTagDaoServiceImpl extends BaseServiceImpl<FileTagMapper, FileTagDO> implements FileTagDaoService {
    @Resource
    private FileTagMapper fileTagMapper;

    @Override
    public IPage<FileTagDTO> page(IPage<FileTagDO> page, FileTagQueryDTO query) {
        IPage<FileTagDO> pageData = lambdaQuery().eq(query.getId() != null, FileTagDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileTagDTO.class));
    }

    @Override
    public List<FileTagDO> getListByFileOriginId(Long fileOriginId) {
        if (fileOriginId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FileTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileTagDO::getOriginId, fileOriginId);
        List<FileTagDO> list = list(queryWrapper);
        if (ObjectUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<FileTagDO> getListByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FileTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FileTagDO::getOriginId, fileOriginIds);
        return list(queryWrapper);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileTagMapper.deleteByFileOriginIds(StringUtils.join(fileOriginIds, ","));
    }
}
