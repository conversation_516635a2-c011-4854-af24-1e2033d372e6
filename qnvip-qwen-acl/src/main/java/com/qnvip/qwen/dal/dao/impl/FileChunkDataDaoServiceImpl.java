package com.qnvip.qwen.dal.dao.impl;


import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dto.FileChunkDataDTO;
import com.qnvip.qwen.dal.dto.FileChunkDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.mapper.FileChunkDataMapper;
import com.qnvip.qwen.util.CopierUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 文件切块数据表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileChunkDataDaoServiceImpl extends BaseServiceImpl<FileChunkDataMapper, FileChunkDataDO>
    implements FileChunkDataDaoService {
    @Resource
    private FileChunkDataMapper fileChunkDataMapper;


    @Override
    public IPage<FileChunkDataDTO> page(IPage<FileChunkDataDO> page, FileChunkDataQueryDTO query) {
        IPage<FileChunkDataDO> pageData =
            lambdaQuery().eq(query.getId() != null, FileChunkDataDO::getId, query.getId()).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, FileChunkDataDTO.class));
    }

    @Override
    public void deleteByFileOriginId(Long fileOriginId) {
        fileChunkDataMapper.deleteByFileOriginId(fileOriginId);
    }

    @Override
    public List<FileChunkDataDO> getListByFileOriginId(Long fileOriginId) {
        if (fileOriginId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FileChunkDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileChunkDataDO::getFileOriginId, fileOriginId).last("limit 100");
        return list(queryWrapper);
    }

    @Override
    public List<FileChunkDataDO> getListByChunkUIds(List<String> chunkUIds) {
        if (CollUtil.isEmpty(chunkUIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FileChunkDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FileChunkDataDO::getChunkUid, chunkUIds);
        return list(queryWrapper);
    }

    @Override
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        if (CollUtil.isEmpty(fileOriginIds)) {
            return;
        }
        fileChunkDataMapper.deleteByFileOriginIds(StringUtils.join(fileOriginIds, ","));
    }
}
