package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.qnvip.qwen.dal.dto.FileTaskProgressJobQueryDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;

/**
 * 文件任务进度表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/26 09:06
 */
public interface FileTaskProgressDaoService {

    List<Long> findByStatus(Long aLong, Integer status, Integer limit);

    FileTaskProgressDO getById(Long id);

    boolean saveOrUpdate(FileTaskProgressDO entity);

    List<Long> findByStatusAndStepList(FileTaskProgressJobQueryDTO query);
}
