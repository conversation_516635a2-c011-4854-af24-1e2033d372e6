package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Data
@TableName("qw_team_book_doc")
public class TeamBookDocDO extends BaseDO {

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("所属团队Id")
    private Long teamId;

    @ApiModelProperty("父id")
    private String parentUid;

    @ApiModelProperty("知识库Id")
    private Long bookId;

    @ApiModelProperty("类型 1文章 2目录")
    private Integer type;

    @ApiModelProperty("三方唯一标识")
    private String mark;

    @ApiModelProperty("三方uid")
    private String uid;

    @ApiModelProperty("文章或者目录名字")
    private String name;

    @ApiModelProperty("来源类型 0飞书语雀 1接口同步 2本地上传")
    private Integer fromType;

}
