package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileQaDataDTO;
import com.qnvip.qwen.dal.dto.FileQaDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileQaDataDO;

/**
 * 文件问答数据表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
public interface FileQaDataDaoService extends BaseService<FileQaDataDO> {

    IPage<FileQaDataDTO> page(IPage<FileQaDataDO> page, FileQaDataQueryDTO query);

    void deleteByFileOriginId(Long fileOriginId);

    void deleteByFileOriginIds(List<Long> fileOriginIds);
}
