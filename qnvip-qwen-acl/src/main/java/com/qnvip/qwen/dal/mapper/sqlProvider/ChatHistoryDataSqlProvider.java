package com.qnvip.qwen.dal.mapper.sqlProvider;

import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 对话记录表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
public class ChatHistoryDataSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_chat_history_data where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
