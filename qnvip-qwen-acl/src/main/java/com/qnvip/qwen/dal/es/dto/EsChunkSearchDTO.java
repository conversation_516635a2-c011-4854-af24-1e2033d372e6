package com.qnvip.qwen.dal.es.dto;

import java.util.List;

import com.qnvip.qwen.dal.dto.FilterOptionsDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月01日 10:25:00
 */
@Data
public class EsChunkSearchDTO {
    public List<Long> bookIds;
    private String userInput;
    /**
     * chunk全文检索过滤条件
     */
    private FilterOptionsDTO chunkFullText;

    @ApiModelProperty("排除的问题id的集合")
    private List<String> excludedChunkIds;
    private List<Long> fileOriginIds;
}
