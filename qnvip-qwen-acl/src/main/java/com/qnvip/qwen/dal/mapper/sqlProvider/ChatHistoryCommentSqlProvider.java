package com.qnvip.qwen.dal.mapper.sqlProvider;

import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 聊天记录评论表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
public class ChatHistoryCommentSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_chat_history_comment where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
