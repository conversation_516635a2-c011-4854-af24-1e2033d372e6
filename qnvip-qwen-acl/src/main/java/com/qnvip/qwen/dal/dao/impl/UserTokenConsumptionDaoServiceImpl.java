package com.qnvip.qwen.dal.dao.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.UserTokenConsumptionDaoService;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionDTO;
import com.qnvip.qwen.dal.dto.UserTokenConsumptionQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConsumptionDO;
import com.qnvip.qwen.dal.mapper.UserTokenConsumptionMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 用户token消耗表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/08 10:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTokenConsumptionDaoServiceImpl extends
    BaseServiceImpl<UserTokenConsumptionMapper, UserTokenConsumptionDO> implements UserTokenConsumptionDaoService {
    @Resource
    private UserTokenConsumptionMapper userTokenConsumptionMapper;


    @Override
    public IPage<UserTokenConsumptionDTO> page(IPage<UserTokenConsumptionDO> page, UserTokenConsumptionQueryDTO query) {
        IPage<UserTokenConsumptionDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, UserTokenConsumptionDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, UserTokenConsumptionDTO.class));
    }
}
