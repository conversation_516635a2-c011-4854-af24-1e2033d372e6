package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.PermUserRoleDaoService;
import com.qnvip.qwen.dal.dto.PermUserRoleDTO;
import com.qnvip.qwen.dal.dto.PermUserRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermUserRoleDO;
import com.qnvip.qwen.dal.mapper.PermUserRoleMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户角色表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermUserRoleDaoServiceImpl extends BaseServiceImpl<PermUserRoleMapper, PermUserRoleDO>
    implements PermUserRoleDaoService {
    @Resource
    private PermUserRoleMapper permUserRoleMapper;

    @Override
    public IPage<PermUserRoleDTO> page(IPage<PermUserRoleDO> page, PermUserRoleQueryDTO query) {
        IPage<PermUserRoleDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, PermUserRoleDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, PermUserRoleDTO.class));
    }
}
