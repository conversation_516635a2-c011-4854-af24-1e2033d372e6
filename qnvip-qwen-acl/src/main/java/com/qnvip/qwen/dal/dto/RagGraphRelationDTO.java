package com.qnvip.qwen.dal.dto;

import lombok.Data;

@Data
public class RagGraphRelationDTO {

    private Long bookId;
    private Long fileOriginId;
    private String chunkUid;

    private String srcId;
    private String tgtId;
    private String keywords;
    private String description;
    private Float vdbScore;

    public String getCompleteChunk() {
        return srcId + "\n" + tgtId + "\n" + keywords + "\n" + description;
    }
}
