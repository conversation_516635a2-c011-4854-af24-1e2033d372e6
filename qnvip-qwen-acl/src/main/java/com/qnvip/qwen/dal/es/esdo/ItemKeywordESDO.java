package com.qnvip.qwen.dal.es.esdo;

import org.springframework.data.annotation.Id;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * {"summary":"一段话摘要","tags":["文章标签1","文章标签2","文章标签3"]}
 */
@Data
public class ItemKeywordESDO {

    @Id
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("平台id")
    private Long platformId;

    @ApiModelProperty("商品名称")
    private String name;

}
