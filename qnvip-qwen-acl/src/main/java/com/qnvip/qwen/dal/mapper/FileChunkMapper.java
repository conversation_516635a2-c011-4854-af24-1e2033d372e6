package com.qnvip.qwen.dal.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.dal.es.dto.EsChunkInitDTO;
import com.qnvip.qwen.dal.mapper.sqlProvider.FileChunkSqlProvider;

/**
 * 文件切块表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
@Mapper
public interface FileChunkMapper extends CustomBaseMapper<FileChunkDO> {

    @Delete("delete from qw_file_chunk where file_origin_id = #{fileOriginId}")
    int deleteByFileOriginId(@Param("fileOriginId") long fileOriginId);

    @Select("select id from qw_file_chunk where uid in (${uIds})")
    List<Long> getIdListByUIds(@Param("uIds") String uIds);

    @Select("SELECT uid FROM qw_file_chunk\n" + "WHERE uid IN (${uIds})\n" + "AND is_deleted = 0\n" + "\n" + "UNION\n"
        + "\n" + "SELECT q.uid FROM qw_file_chunk q\n" + "JOIN (\n" + "    SELECT file_origin_id, sort\n"
        + "    FROM qw_file_chunk\n" + "    WHERE uid IN (${uIds})\n" + ") t ON q.file_origin_id = t.file_origin_id\n"
        + "WHERE (q.sort = t.sort - 1 OR q.sort = t.sort + 1)\n" + "AND q.is_deleted = 0")
    List<String> getSelfAndNearChunkUIdsByUIds(@Param("uIds") String uIds);


    @SelectProvider(type = FileChunkSqlProvider.class, method = "esInitList")
    List<EsChunkInitDTO> getEsInitList(@Param("param") DataInitSearchDTO param);

    @Delete("delete from qw_file_chunk where file_origin_id in (${fileOriginIds})")
    void deleteByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);

    @SelectProvider(type = FileChunkSqlProvider.class, method = "getMilvusInitList")
    List<RagChunkDTO> getMilvusInitList(@Param("fileOriginIds") List<Long> fileOriginIds);
}