package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户缓存表传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("用户缓存表")
@Builder
public class UserCacheDTO extends PageParam {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("模型ID")
    private Long modelId;

    @ApiModelProperty("@name:是否使用知识库, @dicts:[1-是,0-否]")
    private Integer useBookFlag;

    @ApiModelProperty("@name:是否使用联网搜索, @dicts:[1-是,0-否]")
    private Integer useSearchFlag;

    @ApiModelProperty("@name:是否使用深度搜索, @dicts:[1-是,0-否]")
    private Integer useDeepSearchFlag;

    @ApiModelProperty("知识库ID列表字符串")
    private String bookIdsStr;
}
