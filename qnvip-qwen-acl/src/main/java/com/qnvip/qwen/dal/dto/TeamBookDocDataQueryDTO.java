package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文档表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文档表")
public class TeamBookDocDataQueryDTO extends PageParam {

    @ApiModelProperty("文档Id")
    private Long id;

    @ApiModelProperty("所属团队Id")
    private Long docId;

    @ApiModelProperty("文章或者目录名字")
    private String data;
}
