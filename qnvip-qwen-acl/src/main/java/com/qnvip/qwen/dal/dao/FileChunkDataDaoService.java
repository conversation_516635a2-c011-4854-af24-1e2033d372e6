package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileChunkDataDTO;
import com.qnvip.qwen.dal.dto.FileChunkDataQueryDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;

/**
 * 文件切块数据表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:57
 */
public interface FileChunkDataDaoService extends BaseService<FileChunkDataDO> {

    IPage<FileChunkDataDTO> page(IPage<FileChunkDataDO> page, FileChunkDataQueryDTO query);

    void deleteByFileOriginId(Long fileOriginId);

    List<FileChunkDataDO> getListByFileOriginId(Long fileOriginId);

    List<FileChunkDataDO> getListByChunkUIds(List<String> chunkUIds);

    void deleteByFileOriginIds(List<Long> fileOriginIds);
}
