package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.FileQaDataDO;

/**
 * 文件问答数据表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
@Mapper
public interface FileQaDataMapper extends CustomBaseMapper<FileQaDataDO> {

    @Delete("delete from qw_file_qa_data where file_origin_id=#{fileOriginId}")
    int deleteByFileOriginId(@Param("fileOriginId") Long fileOriginId);

    @Delete("delete from qw_file_qa_data where file_origin_id in (${fileOriginIds})")
    void deleteByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);
}