package com.qnvip.qwen.dal.dto;

import java.util.ArrayList;
import java.util.List;

import com.qnvip.qwen.dal.dto.graph.GraphEntity;
import com.qnvip.qwen.dal.dto.graph.GraphRelationship;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RagGraphMutilDTO {
    private List<GraphEntity> entityDTOS = new ArrayList<>();
    private List<GraphRelationship> relationDTOS = new ArrayList<>();

}
