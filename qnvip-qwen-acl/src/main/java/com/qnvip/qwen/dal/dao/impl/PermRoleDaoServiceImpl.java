package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.PermRoleDaoService;
import com.qnvip.qwen.dal.dto.PermRoleDTO;
import com.qnvip.qwen.dal.dto.PermRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleDO;
import com.qnvip.qwen.dal.mapper.PermRoleMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermRoleDaoServiceImpl extends BaseServiceImpl<PermRoleMapper, PermRoleDO> implements PermRoleDaoService {
    @Resource
    private PermRoleMapper permRoleMapper;

    @Override
    public IPage<PermRoleDTO> page(IPage<PermRoleDO> page, PermRoleQueryDTO query) {
        IPage<PermRoleDO> pageData = lambdaQuery().setEntity(CopierUtil.copy(query, PermRoleDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, PermRoleDTO.class));
    }
}
