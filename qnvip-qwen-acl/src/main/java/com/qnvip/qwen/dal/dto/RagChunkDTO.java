package com.qnvip.qwen.dal.dto;

import com.qnvip.qwen.util.rag.ChunkUtil;

import lombok.Data;

@Data
public class RagChunkDTO {

    @Deprecated
    private Long teamId = 0L;

    private String qaUid;
    private Long bookId;
    private Long fileOriginId;
    private String chunkUid;
    private String tocs;
    private String qaLink;
    private String question;
    private String answer;
    private String chunk;


    /**
     * 带目录的切块
     * 
     * @return
     */
    public String getCompleteChunk() {
        return ChunkUtil.removeFirstToc(tocs) + " " + chunk;
    }

    /**
     * 完整的qa链
     * 
     * @return
     */
    public String getCompleteQaLink() {
        if (qaLink == null) {
            qaLink = question + " " + answer;
        }
        return ChunkUtil.removeFirstToc(tocs) + " " + qaLink;
    }

    public Long getTeamId() {
        return 0L;
    }
}
