package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.DifyAppApikeyDaoService;
import com.qnvip.qwen.dal.dto.DifyAppApikeyDTO;
import com.qnvip.qwen.dal.dto.DifyAppApikeyQueryDTO;
import com.qnvip.qwen.dal.entity.DifyAppApikeyDO;
import com.qnvip.qwen.dal.mapper.DifyAppApikeyMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * dify应用的apikey表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DifyAppApikeyDaoServiceImpl extends BaseServiceImpl<DifyAppApikeyMapper, DifyAppApikeyDO>
    implements DifyAppApikeyDaoService {
    @Resource
    private DifyAppApikeyMapper difyAppApikeyMapper;

    @Override
    public IPage<DifyAppApikeyDTO> page(IPage<DifyAppApikeyDO> page, DifyAppApikeyQueryDTO query) {
        IPage<DifyAppApikeyDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, DifyAppApikeyDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, DifyAppApikeyDTO.class));
    }
}
