package com.qnvip.qwen.dal.dto.wechat;

import java.util.List;

import lombok.Data;

/**
 * 微信部门层级结构DTO
 * 
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月27日 16:59:00
 */
@Data
public class WechatDepartmentHierarchyDTO {

    /**
     * 部门ID
     */
    private Integer id;

    /**
     * 父部门ID，第1层为0
     */
    private Integer parent;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 员工姓名列表
     */
    private List<String> employeeNames;

    /**
     * 子部门列表
     */
    private List<WechatDepartmentHierarchyDTO> children;
}