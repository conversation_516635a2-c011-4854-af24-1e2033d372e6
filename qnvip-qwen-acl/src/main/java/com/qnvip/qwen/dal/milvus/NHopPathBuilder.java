package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Data;

public class NHopPathBuilder {

    // N跳路径构建的核心方法
    public Map<EntityPair, PathInfo> buildNHopPaths(Map<String, EntityInfo> entitiesFromQuery) {
        Map<EntityPair, PathInfo> nhopPaths = new HashMap<>();

        // 遍历查询得到的实体
        for (Map.Entry<String, EntityInfo> entry : entitiesFromQuery.entrySet()) {
            EntityInfo entity = entry.getValue();
            List<NeighborInfo> nHopEntities = entity.getNHopEntities();

            if (nHopEntities == null || nHopEntities.isEmpty()) {
                continue;
            }

            // 处理每个邻居的路径信息
            for (NeighborInfo neighbor : nHopEntities) {
                List<String> path = neighbor.getPath();
                List<Double> weights = neighbor.getWeights();

                // 构建路径中的每一跳
                for (int i = 0; i < path.size() - 1; i++) {
                    String fromEntity = path.get(i);
                    String toEntity = path.get(i + 1);
                    EntityPair pair = new EntityPair(fromEntity, toEntity);

                    // 计算衰减权重：原实体相似度 / (2 + 跳数)
                    double decayedSimilarity = entity.getSimilarity() / (2.0 + i);

                    // 如果路径已存在，累加相似度分数
                    if (nhopPaths.containsKey(pair)) {
                        PathInfo existingPath = nhopPaths.get(pair);
                        existingPath.setSimilarity(existingPath.getSimilarity() + decayedSimilarity);
                    } else {
                        // 创建新的路径信息
                        PathInfo pathInfo = new PathInfo();
                        pathInfo.setSimilarity(decayedSimilarity);
                        pathInfo.setPageRank(weights.get(i));
                        nhopPaths.put(pair, pathInfo);
                    }
                }
            }
        }

        return nhopPaths;
    }

    // 融合N跳路径与直接查询的关系
    public void mergeWithDirectRelations(Map<EntityPair, PathInfo> nhopPaths,
        Map<EntityPair, RelationInfo> directRelations, Set<String> entitiesFromTypes) {

        // 处理直接查询得到的关系
        for (Map.Entry<EntityPair, RelationInfo> entry : directRelations.entrySet()) {
            EntityPair pair = entry.getKey();
            RelationInfo relation = entry.getValue();

            // 计算增强分数
            double enhancementScore = 0;

            // 如果N跳路径中存在相同的实体对，累加分数并移除
            EntityPair sortedPair = pair.getSorted();
            if (nhopPaths.containsKey(sortedPair)) {
                enhancementScore += nhopPaths.get(sortedPair).getSimilarity();
                nhopPaths.remove(sortedPair);
            }

            // 根据实体类型增强分数
            if (entitiesFromTypes.contains(pair.getFromEntity())) {
                enhancementScore += 1;
            }
            if (entitiesFromTypes.contains(pair.getToEntity())) {
                enhancementScore += 1;
            }

            // 应用增强分数
            relation.setSimilarity(relation.getSimilarity() * (enhancementScore + 1));
        }

        // 处理仅来自N跳但不在直接查询中的关系
        for (Map.Entry<EntityPair, PathInfo> entry : nhopPaths.entrySet()) {
            EntityPair pair = entry.getKey();
            PathInfo pathInfo = entry.getValue();

            double enhancementScore = 0;
            if (entitiesFromTypes.contains(pair.getFromEntity())) {
                enhancementScore += 1;
            }
            if (entitiesFromTypes.contains(pair.getToEntity())) {
                enhancementScore += 1;
            }

            // 将N跳关系添加到直接关系中
            RelationInfo newRelation = new RelationInfo();
            newRelation.setSimilarity(pathInfo.getSimilarity() * (enhancementScore + 1));
            newRelation.setPageRank(pathInfo.getPageRank());

            directRelations.put(pair, newRelation);
        }
    }

    // 数据结构定义
    @Data
    static class EntityPair {
        private String fromEntity;
        private String toEntity;

        public EntityPair(String from, String to) {
            this.fromEntity = from;
            this.toEntity = to;
        }

        public EntityPair getSorted() {
            if (fromEntity.compareTo(toEntity) <= 0) {
                return new EntityPair(fromEntity, toEntity);
            } else {
                return new EntityPair(toEntity, fromEntity);
            }
        }

        // getters, setters, equals, hashCode...
    }

    @Data
    static class EntityInfo {
        private double similarity;
        private double pageRank;
        private List<NeighborInfo> nHopEntities;

        // getters, setters...
    }

    @Data
    static class NeighborInfo {
        private List<String> path;
        private List<Double> weights;

        // getters, setters...
    }

    @Data
    static class PathInfo {
        private double similarity;
        private double pageRank;

        // getters, setters...
    }

    @Data
    static class RelationInfo {
        private double similarity;
        private double pageRank;
        private String description;

        // getters, setters...
    }

    public static void main(String[] args) {
        // 创建NHopPathBuilder实例
        NHopPathBuilder pathBuilder = new NHopPathBuilder();

        // 1. 模拟从查询中获取的实体数据（对应ents_from_query）
        Map<String, EntityInfo> entitiesFromQuery = createSampleEntities();

        // 2. 模拟直接查询得到的关系（对应rels_from_txt）
        Map<EntityPair, RelationInfo> directRelations = createSampleRelations();

        // 3. 模拟从类型检索得到的实体集合（对应ents_from_types）
        Set<String> entitiesFromTypes = createSampleEntityTypes();

        // 4. 构建N跳路径
        System.out.println("=== 步骤1: 构建N跳路径 ===");
        Map<EntityPair, PathInfo> nhopPaths = pathBuilder.buildNHopPaths(entitiesFromQuery);

        for (Map.Entry<EntityPair, PathInfo> entry : nhopPaths.entrySet()) {
            EntityPair pair = entry.getKey();
            PathInfo pathInfo = entry.getValue();
            System.out.printf("N跳路径: %s -> %s, 衰减相似度: %.3f, PageRank: %.3f%n", pair.getFromEntity(), pair.getToEntity(),
                pathInfo.getSimilarity(), pathInfo.getPageRank());
        }

        // 5. 融合N跳路径与直接关系
        System.out.println("\n=== 步骤2: 融合N跳路径与直接关系 ===");
        pathBuilder.mergeWithDirectRelations(nhopPaths, directRelations, entitiesFromTypes);

        for (Map.Entry<EntityPair, RelationInfo> entry : directRelations.entrySet()) {
            EntityPair pair = entry.getKey();
            RelationInfo relation = entry.getValue();
            System.out.printf("融合关系: %s -> %s, 增强分数: %.3f, PageRank: %.3f%n", pair.getFromEntity(), pair.getToEntity(),
                relation.getSimilarity(), relation.getPageRank());
        }

        // 6. 按综合分数排序（sim * pagerank）
        System.out.println("\n=== 步骤3: 最终排序结果 ===");
        List<Map.Entry<EntityPair, RelationInfo>> sortedRelations = directRelations.entrySet().stream()
            .sorted((a, b) -> Double.compare(b.getValue().getSimilarity() * b.getValue().getPageRank(),
                a.getValue().getSimilarity() * a.getValue().getPageRank()))
            .limit(6) // 对应rel_topn=6
            .collect(Collectors.toList());

        for (Map.Entry<EntityPair, RelationInfo> entry : sortedRelations) {
            EntityPair pair = entry.getKey();
            RelationInfo relation = entry.getValue();
            double finalScore = relation.getSimilarity() * relation.getPageRank();
            System.out.printf("Top关系: %s -> %s, 综合分数: %.3f%n", pair.getFromEntity(), pair.getToEntity(), finalScore);
        }
    }

    // 创建示例实体数据，模拟包含n_hop_ents的实体
    private static Map<String, EntityInfo> createSampleEntities() {
        Map<String, EntityInfo> entities = new HashMap<>();

        // 实体1: "深度学习" - 包含多跳邻居
        EntityInfo deepLearning = new EntityInfo();
        deepLearning.setSimilarity(0.85); // 对应ent["sim"]
        deepLearning.setPageRank(0.12);

        List<NeighborInfo> neighbors = new ArrayList<>();

        // 2跳路径: 深度学习 -> 神经网络 -> CNN
        NeighborInfo neighbor1 = new NeighborInfo();
        neighbor1.setPath(Arrays.asList("深度学习", "神经网络", "CNN"));
        neighbor1.setWeights(Arrays.asList(0.08, 0.06)); // 对应wts[i]
        neighbors.add(neighbor1);

        // 3跳路径: 深度学习 -> 机器学习 -> 监督学习 -> 分类
        NeighborInfo neighbor2 = new NeighborInfo();
        neighbor2.setPath(Arrays.asList("深度学习", "机器学习", "监督学习", "分类"));
        neighbor2.setWeights(Arrays.asList(0.09, 0.07, 0.05));
        neighbors.add(neighbor2);

        deepLearning.setNHopEntities(neighbors);
        entities.put("深度学习", deepLearning);

        // 实体2: "机器学习"
        EntityInfo machineLearning = new EntityInfo();
        machineLearning.setSimilarity(0.75);
        machineLearning.setPageRank(0.10);

        List<NeighborInfo> mlNeighbors = new ArrayList<>();
        NeighborInfo mlNeighbor = new NeighborInfo();
        mlNeighbor.setPath(Arrays.asList("机器学习", "强化学习", "Q学习"));
        mlNeighbor.setWeights(Arrays.asList(0.07, 0.04));
        mlNeighbors.add(mlNeighbor);

        machineLearning.setNHopEntities(mlNeighbors);
        entities.put("机器学习", machineLearning);

        return entities;
    }

    // 创建示例直接关系数据
    private static Map<EntityPair, RelationInfo> createSampleRelations() {
        Map<EntityPair, RelationInfo> relations = new HashMap<>();

        // 直接关系1: 深度学习 -> 神经网络
        EntityPair pair1 = new EntityPair("深度学习", "神经网络");
        RelationInfo relation1 = new RelationInfo();
        relation1.setSimilarity(0.80);
        relation1.setPageRank(0.08);
        relation1.setDescription("深度学习基于神经网络架构");
        relations.put(pair1, relation1);

        return relations;
    }

    // 创建示例实体类型数据
    private static Set<String> createSampleEntityTypes() {
        return new HashSet<>(Arrays.asList("深度学习", "机器学习", "神经网络", "CNN"));
    }
}