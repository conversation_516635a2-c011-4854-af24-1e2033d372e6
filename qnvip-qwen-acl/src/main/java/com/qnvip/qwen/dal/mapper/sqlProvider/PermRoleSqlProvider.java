package com.qnvip.qwen.dal.mapper.sqlProvider;

import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;

/**
 * 角色表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public class PermRoleSqlProvider {

    public String listByQuery(Map<String, Object> parameter) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from qw_perm_role where 1=1");
        if (ObjectUtils.isNotEmpty(parameter.get("id"))) {
            sql.append(" and id = #{param.id}");
        }
        return sql.toString();
    }
}
