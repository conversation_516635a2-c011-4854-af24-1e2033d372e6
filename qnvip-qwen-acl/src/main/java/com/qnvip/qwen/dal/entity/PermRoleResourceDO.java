package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 角色资源表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Data
@TableName("qw_perm_role_resource")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PermRoleResourceDO extends BaseDO {


    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("资源id -1全部权限")
    private Long resourceId;

    @ApiModelProperty("@name:资源类型, @dicts:[1-知识库资源]")
    private Integer resourceType;
}
