package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.LlmSupportModelDTO;
import com.qnvip.qwen.dal.dto.LlmSupportModelQueryDTO;
import com.qnvip.qwen.dal.entity.LlmSupportModelDO;

/**
 * 支持的模型表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
public interface LlmSupportModelDaoService {

    IPage<LlmSupportModelDTO> page(IPage<LlmSupportModelDO> page, LlmSupportModelQueryDTO query);

}
