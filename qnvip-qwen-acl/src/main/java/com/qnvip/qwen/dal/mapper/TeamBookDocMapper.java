package com.qnvip.qwen.dal.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;

/**
 * 文档表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Mapper
public interface TeamBookDocMapper extends CustomBaseMapper<TeamBookDocDO> {

    @Select("WITH RECURSIVE ParentsCTE AS (\n" + "    SELECT \n" + "        id,\n" + "        team_id,\n"
        + "        parent_uid,\n" + "        book_id,\n" + "        type,\n" + "        mark,\n" + "        uid,\n"
        + "        name,\n" + "        version,\n" + "        create_time,\n" + "        update_time,\n"
        + "        is_deleted\n" + "    FROM \n" + "        qw_team_book_doc\n" + "    WHERE \n"
        + "        book_id = #{bookId}\n" + "        AND uid = #{uId}\n" + "        AND is_deleted = 0\n"
        + "    UNION ALL\n" + "    SELECT \n" + "        t.id,\n" + "        t.team_id,\n" + "        t.parent_uid,\n"
        + "        t.book_id,\n" + "        t.type,\n" + "        t.mark,\n" + "        t.uid,\n" + "        t.name,\n"
        + "        t.version,\n" + "        t.create_time,\n" + "        t.update_time,\n" + "        t.is_deleted\n"
        + "    FROM \n" + "        qw_team_book_doc t\n" + "    JOIN \n"
        + "        ParentsCTE p ON t.uid = p.parent_uid\n" + "    WHERE\n" + "        t.is_deleted = 0 -- 添加额外过滤条件\n"
        + ")\n" + "SELECT \n" + "    *\n" + "FROM \n" + "    ParentsCTE;\n" + "    ")
    List<TeamBookDocDO> loadFileTocsByCte(@Param("bookId") Long bookId, @Param("uId") String docUid);

    @Update("UPDATE qw_team_book_doc AS child\n" +
            "INNER JOIN qw_team_book_doc AS parent \n" +
            "ON child.parent_id = parent.id \n" +
            "SET child.update_time = NOW(4)\n" +
            "WHERE \n" +
            "    parent.is_deleted = 0 \n" +
            "    AND child.is_deleted = 0\n" +
            "    AND child.parent_id > 0;\n")
    void loadHasChildrenDoc();
}