package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件图数据表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件图数据表")
public class FileGraphEntityQueryDTO extends PageParam {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("文件块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("知识库id")
    private Long bookId;

    @ApiModelProperty("实体名字")
    private String name;

    @ApiModelProperty("实体类型")
    private String type;

    @ApiModelProperty("实体描述")
    private String description;
}
