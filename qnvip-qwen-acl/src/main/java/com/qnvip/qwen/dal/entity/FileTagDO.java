package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 文件标签表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Data
@TableName("qw_file_tag")
@Builder
public class FileTagDO extends BaseDO {

    @ApiModelProperty("文件Id")
    private Long originId;

    @ApiModelProperty("标签Id")
    private String name;
}
