package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件任务进度的产出数据日志表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/27 13:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件任务进度的产出数据日志表")
public class FileTaskProgressDatalogQueryDTO extends PageParam {

    @ApiModelProperty("任务进度Id")
    private Long id;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("任务步骤id")
    private Long taskProgressId;

    @ApiModelProperty("数据")
    private String data;

    @ApiModelProperty("任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70图谱结构化 80完结")
    private Integer nowStep;
}
