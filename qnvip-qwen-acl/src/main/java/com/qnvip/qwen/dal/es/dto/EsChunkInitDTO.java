package com.qnvip.qwen.dal.es.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月15日 11:15:00
 */
@Data
public class EsChunkInitDTO {
    @ApiModelProperty("切块uid")
    private String chunkUId;

    @ApiModelProperty("文档uid")
    private String docId;

    @ApiModelProperty("切块内容")
    private String content;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("标签")
    private String tags;

    @ApiModelProperty("文件名")
    private Long fileOriginId;

    @ApiModelProperty("团队名/目录")
    private String tocs;

    private String teamName;

    private Long teamId;

    private Long bookId;
}
