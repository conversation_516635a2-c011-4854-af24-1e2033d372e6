package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.ChatHistoryDataDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryDataQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryDataDO;

/**
 * 对话记录表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/04/08 10:04
 */
public interface ChatHistoryDataDaoService {

    IPage<ChatHistoryDataDTO> page(IPage<ChatHistoryDataDO> page, ChatHistoryDataQueryDTO query);

}
