package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户角色表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Data
@TableName("qw_perm_user_role")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PermUserRoleDO extends BaseDO {


    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("角色id")
    private Long roleId;
}
