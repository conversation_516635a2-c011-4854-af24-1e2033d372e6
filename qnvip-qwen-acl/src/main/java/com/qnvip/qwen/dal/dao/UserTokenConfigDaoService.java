package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.UserTokenConfigDTO;
import com.qnvip.qwen.dal.dto.UserTokenConfigQueryDTO;
import com.qnvip.qwen.dal.entity.UserTokenConfigDO;

/**
 * 用户token用量配置表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
public interface UserTokenConfigDaoService {

    IPage<UserTokenConfigDTO> page(IPage<UserTokenConfigDO> page, UserTokenConfigQueryDTO query);

}
