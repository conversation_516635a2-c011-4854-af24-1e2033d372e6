package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.UserTokenConfigDO;

/**
 * 用户token用量配置表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:42
 */
@Mapper
public interface UserTokenConfigMapper extends CustomBaseMapper<UserTokenConfigDO> {

    @InterceptorIgnore(blockAttack = "true")
    @Update("UPDATE qw_user_token_config SET used_token = 0")
    Boolean resetAllUsedToken();

    @Update("UPDATE qw_user_token_config SET used_token = used_token + #{totalConsumedToken} WHERE id = #{userId} ")
    void increaseUsedToken(@Param("userId") Long userId, @Param("totalConsumedToken") Long totalConsumedToken);
}