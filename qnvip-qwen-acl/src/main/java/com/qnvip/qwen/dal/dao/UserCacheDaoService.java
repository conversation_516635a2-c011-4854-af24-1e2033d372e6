package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.UserCacheDTO;
import com.qnvip.qwen.dal.dto.UserCacheQueryDTO;
import com.qnvip.qwen.dal.entity.UserCacheDO;

/**
 * 用户缓存表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/28 11:08
 */
public interface UserCacheDaoService {

    IPage<UserCacheDTO> page(IPage<UserCacheDO> page, UserCacheQueryDTO query);

}
