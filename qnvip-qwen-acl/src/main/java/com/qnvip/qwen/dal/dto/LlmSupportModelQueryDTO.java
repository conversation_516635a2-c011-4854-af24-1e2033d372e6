package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 支持的模型表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/17 15:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("支持的模型表")
public class LlmSupportModelQueryDTO extends PageParam {

    @ApiModelProperty("主键Id")
    private Long id;

    @ApiModelProperty("模型名称")
    private String name;

    @ApiModelProperty("模型图标")
    private String icon;

    @ApiModelProperty("模型描述")
    private String description;
}
