package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.dal.dto.open.ItemKeywordDTO;

import cn.hutool.core.collection.CollUtil;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.DataType;
import io.milvus.grpc.MutationResult;
import io.milvus.grpc.SearchResults;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import io.milvus.param.R;
import io.milvus.param.RpcStatus;
import io.milvus.param.collection.CreateCollectionParam;
import io.milvus.param.collection.FieldType;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.collection.LoadCollectionParam;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.index.CreateIndexParam;
import lombok.Getter;

@Getter
@Service

public class MilvusItemKeywordService {

    @Resource
    private MilvusServiceClient milvusServiceClient;

    @Resource
    private EmbeddingClient embeddingClientItemKeyWord;

    private final boolean collectionIsInit = false;



    public String getCollectionName() {
        return "item_keyword";
    }

    public String getExpr(Long platformId) {
        if (platformId == null) {
            return "";
        }
        // 动态构建删除条件表达式
        List<String> conditions = new ArrayList<>();

        if (platformId != null) {
            conditions.add(String.format("platform_id == %d", platformId));
        }
        return StringUtils.join(conditions, " && ");
    }

    public boolean hasCollection() {
        return milvusServiceClient
            .hasCollection(HasCollectionParam.newBuilder().withCollectionName(getCollectionName()).build()).getData();
    }

    public void createDefaultCollection() {
        String collectionName = getCollectionName();

        R<Boolean> booleanR = milvusServiceClient
            .hasCollection(HasCollectionParam.newBuilder().withCollectionName(collectionName).build());
        if (booleanR.getData()) {
            // collectionIsInit = true;
            return;
        }

        CreateCollectionParam createCollectionReq = CreateCollectionParam.newBuilder()
            .withCollectionName(collectionName)
            .addFieldType(
                FieldType.newBuilder().withName("id").withDataType(DataType.Int64).withPrimaryKey(true).build())
            .addFieldType(
                FieldType.newBuilder().withName("keyword").withDataType(DataType.VarChar).withMaxLength(1024).build())
            .addFieldType(FieldType.newBuilder().withName("keyword_vector").withDataType(DataType.FloatVector)
                .withDimension(768).build())
            .addFieldType(FieldType.newBuilder().withName("platform_id").withDataType(DataType.Int64).build()).build();
        milvusServiceClient.createCollection(createCollectionReq);

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("id").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("keyword_vector").withIndexType(IndexType.IVF_FLAT).withMetricType(MetricType.IP)
            .withSyncMode(Boolean.FALSE).withExtraParam("{\"nlist\":1024}").build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("platform_id").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.loadCollection(LoadCollectionParam.newBuilder().withCollectionName(collectionName).build());

        hasCollection();
    }

    public void loadCollection() {
        R<RpcStatus> response = milvusServiceClient
            .loadCollection(LoadCollectionParam.newBuilder().withCollectionName(getCollectionName()).build());
    }

    public List<Long> search(Long platformId, String keyword, Integer topK) {
        List<List<Float>> string = embeddingClientItemKeyWord.doEmbedding(keyword);
        return search(platformId, string, topK);
    }

    private List<Long> search(Long platformId, List<List<Float>> questionVector, Integer topK) {
        // loadCollection(teamId);

        List<Long> lists = new ArrayList<>();
        List<Long> idsByQuestion = searchContent(platformId, "keyword_vector", questionVector, topK);
        lists.addAll(idsByQuestion);
        lists.removeIf(Objects::isNull);
        return lists;
    }

    private List<Long> searchContent(Long platformId, String vectorFieldName, List<List<Float>> vectors, Integer topK) {
        int nprobeVectorSize = vectors.get(0).size();
        String paramsInJson = "{\"nprobe\": " + nprobeVectorSize + "}";
        SearchParam.Builder contentVectorBuilder =
            SearchParam.newBuilder().withCollectionName(getCollectionName()).withParams(paramsInJson)
                .withMetricType(MetricType.IP).withVectors(vectors).withVectorFieldName(vectorFieldName).withTopK(topK);

        String expr = getExpr(platformId);
        if (!ObjectUtils.isEmpty(expr)) {
            contentVectorBuilder.withExpr(expr);
        }

        SearchParam searchParam = contentVectorBuilder.build();

        R<SearchResults> searchResultsR = trySearch(searchParam);

        SearchResults searchResultsRData = searchResultsR.getData();
        if (searchResultsRData == null) {
            return new ArrayList<>();
        }
        return searchResultsRData.getResults().getIds().getIntId().getDataList();
    }

    private R<SearchResults> trySearch(SearchParam searchParam) {
        R<SearchResults> searchResultsR = milvusServiceClient.search(searchParam);
        if (searchResultsR.getException() != null
            && searchResultsR.getException().getMessage().contains("failed to search: collection not loaded")) {
            loadCollection();
            searchResultsR = milvusServiceClient.search(searchParam);
        }
        return searchResultsR;
    }

    public void insert(List<ItemKeywordDTO> list) {
        // createDefaultCollection(teamId);
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<String> keywords = list.stream().map(ItemKeywordDTO::getKeyword).collect(Collectors.toList());
        List<List<Float>> nameVector = embeddingClientItemKeyWord.doEmbedding(keywords);

        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(new InsertParam.Field("id", list.stream().map(ItemKeywordDTO::getId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("platform_id",
            list.stream().map(ItemKeywordDTO::getPlatformId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("keyword", keywords));
        fields.add(new InsertParam.Field("keyword_vector", nameVector));

        InsertParam insertParam =
            InsertParam.newBuilder().withCollectionName(getCollectionName()).withFields(fields).build();
        milvusServiceClient.insert(insertParam);
    }

    public void deleteByIds(List<Long> ids) {
        // 1. 构建删除表达式
        String deleteExpr = String.format("id in [%s]", StringUtils.join(ids, ","));

        // 2. 创建删除参数
        DeleteParam deleteParam =
            DeleteParam.newBuilder().withCollectionName(getCollectionName()).withExpr(deleteExpr).build();

        // 3. 执行删除
        R<MutationResult> response = milvusServiceClient.delete(deleteParam);

        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("删除Milvus数据失败: " + response.getMessage());
        }
    }
}
