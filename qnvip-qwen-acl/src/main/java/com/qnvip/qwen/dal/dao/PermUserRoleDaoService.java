package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.PermUserRoleDTO;
import com.qnvip.qwen.dal.dto.PermUserRoleQueryDTO;
import com.qnvip.qwen.dal.entity.PermUserRoleDO;

/**
 * 用户角色表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
public interface PermUserRoleDaoService {

    IPage<PermUserRoleDTO> page(IPage<PermUserRoleDO> page, PermUserRoleQueryDTO query);

}
