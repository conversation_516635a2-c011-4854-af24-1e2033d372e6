package com.qnvip.qwen.dal.dao;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.common.base.PageParam;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.TeamBookDocDTO;
import com.qnvip.qwen.dal.dto.TeamBookDocQueryDTO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;

/**
 * 文档表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
public interface TeamBookDocDaoService extends BaseService<TeamBookDocDO> {

    IPage<TeamBookDocDTO> page(IPage<TeamBookDocDO> page, TeamBookDocQueryDTO query);

    Map<String, Long> getMapByBookIdAndUid(Long bookId, List<String> mark);

    TeamBookDocDO getByBookIdAndUid(Long bookId, String uid);

    List<TeamBookDocDO> getPageByType(Integer type, PageParam pageParam);

    Map<String, TeamBookDocDO> getMapByBookIdAndMarkList(Long bookId, List<String> markList);

    List<TeamBookDocDO> getDeleteListByUpdateTime(LocalDateTime updateTime);

    void deleteByIds(List<Long> ids);

    List<TeamBookDocDO> getListByBookIds(List<Long> bookIds);

    void renewExitsSubDoc();

    void deleteByDocUIds(List<String> docUids);
}
