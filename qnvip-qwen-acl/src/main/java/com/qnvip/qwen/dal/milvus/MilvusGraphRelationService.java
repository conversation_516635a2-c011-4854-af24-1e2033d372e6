package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.qnvip.qwen.dal.dto.FilterOptionsDTO;
import com.qnvip.qwen.dal.dto.RagGraphRelationDTO;
import com.qnvip.qwen.util.rag.GraphUtil;

import cn.hutool.core.collection.CollUtil;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.DataType;
import io.milvus.grpc.FieldData;
import io.milvus.grpc.MutationResult;
import io.milvus.grpc.SearchResults;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import io.milvus.param.R;
import io.milvus.param.RpcStatus;
import io.milvus.param.collection.CreateCollectionParam;
import io.milvus.param.collection.FieldType;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.collection.LoadCollectionParam;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.index.CreateIndexParam;
import lombok.Getter;

@Getter
@Repository
public class MilvusGraphRelationService {

    private final MilvusServiceClient milvusServiceClient;

    private final EmbeddingClient embeddingClient;

    private final boolean collectionIsInit = false;

    public MilvusGraphRelationService(MilvusServiceClient client, EmbeddingClient embeddingClient) {
        this.milvusServiceClient = client;
        this.embeddingClient = embeddingClient;
    }

    public String getCollectionName() {
        return "graph_relation_v1";
    }

    public String getExpr(List<Long> bookIds, List<Long> fileOriginIds, List<String> excludedChunkIds) {
        if (CollUtil.isEmpty(bookIds) && CollUtil.isEmpty(fileOriginIds) && CollUtil.isEmpty(excludedChunkIds)) {
            return "";
        }

        List<String> conditions = new ArrayList<>();

        if (CollUtil.isNotEmpty(bookIds)) {
            String bookInExpr = String.format("book_id in [%s]",
                String.join(",", bookIds.stream().map(Object::toString).toArray(String[]::new)));
            conditions.add(bookInExpr);
        }

        if (CollUtil.isNotEmpty(fileOriginIds)) {
            String docInExpr = String.format("file_origin_id in [%s]",
                String.join(",", fileOriginIds.stream().map(Object::toString).toArray(String[]::new)));
            conditions.add(docInExpr);
        }

        if (CollUtil.isNotEmpty(excludedChunkIds)) {
            String docInExpr = String.format("chunk_uid not in [%s]",
                String.join(",", excludedChunkIds.stream().map(e -> "'" + e + "'").toArray(String[]::new)));
            conditions.add(docInExpr);
        }

        return StringUtils.join(conditions, " && ");
    }

    public void createDefaultCollection() {
        String collectionName = getCollectionName();
        R<Boolean> booleanR = milvusServiceClient
            .hasCollection(HasCollectionParam.newBuilder().withCollectionName(collectionName).build());
        if (booleanR.getData()) {
            return;
        }

        CreateCollectionParam createCollectionReq = CreateCollectionParam.newBuilder()
            .withCollectionName(collectionName)
            .addFieldType(FieldType.newBuilder().withName("id").withDataType(DataType.VarChar).withMaxLength(64)
                .withPrimaryKey(true).build())
            .addFieldType(
                FieldType.newBuilder().withName("content").withDataType(DataType.VarChar).withMaxLength(4096).build())
            .addFieldType(
                FieldType.newBuilder().withName("src_id").withDataType(DataType.VarChar).withMaxLength(128).build())
            .addFieldType(
                FieldType.newBuilder().withName("tgt_id").withDataType(DataType.VarChar).withMaxLength(128).build())
            .addFieldType(
                FieldType.newBuilder().withName("keywords").withDataType(DataType.VarChar).withMaxLength(128).build())

            .addFieldType(FieldType.newBuilder().withName("content_vector_chunk").withDataType(DataType.FloatVector)
                .withDimension(768).build())

            .addFieldType(FieldType.newBuilder().withName("book_id").withDataType(DataType.Int64).build())
            .addFieldType(FieldType.newBuilder().withName("file_origin_id").withDataType(DataType.Int64).build())
            .addFieldType(
                FieldType.newBuilder().withName("chunk_uid").withDataType(DataType.VarChar).withMaxLength(64).build())
            .build();
        milvusServiceClient.createCollection(createCollectionReq);

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("id").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("src_id").withIndexType(IndexType.TRIE).build());
        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("tgt_id").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("content_vector_chunk").withIndexType(IndexType.IVF_FLAT).withMetricType(MetricType.IP)
            .withSyncMode(Boolean.FALSE).withExtraParam("{\"nlist\":1024}").build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("book_id").withIndexType(IndexType.STL_SORT).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("file_origin_id").withIndexType(IndexType.STL_SORT).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("chunk_uid").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.loadCollection(LoadCollectionParam.newBuilder().withCollectionName(collectionName).build());
    }

    public void loadCollection() {
        R<RpcStatus> response = milvusServiceClient
            .loadCollection(LoadCollectionParam.newBuilder().withCollectionName(getCollectionName()).build());
    }

    public List<RagGraphRelationDTO> search(List<Long> bookIds, List<Long> fileOriginIds, String question,
        FilterOptionsDTO filter, List<String> excludedChunkIds) {
        List<List<Float>> string = embeddingClient.doEmbedding(question);
        return searchContent(bookIds, fileOriginIds, string, filter, excludedChunkIds);
    }

    private List<RagGraphRelationDTO> searchContent(List<Long> bookIds, List<Long> fileOriginIds,
        List<List<Float>> vectors, FilterOptionsDTO filter, List<String> excludedChunkIds) {
        int nprobeVectorSize = vectors.get(0).size();
        String paramsInJson = "{\"nprobe\": " + nprobeVectorSize + "}";
        SearchParam.Builder contentVectorBuilder = SearchParam.newBuilder().withCollectionName(getCollectionName())
            .withParams(paramsInJson).withMetricType(MetricType.IP).withVectors(vectors)
            .withVectorFieldName("content_vector_chunk").withTopK(filter.getTopN()).addOutField("book_id")
            .addOutField("tgt_id").addOutField("src_id");

        String expr = getExpr(bookIds, fileOriginIds, excludedChunkIds);
        if (!ObjectUtils.isEmpty(expr)) {
            contentVectorBuilder.withExpr(expr);
        }

        SearchParam searchParam = contentVectorBuilder.build();

        R<SearchResults> searchResultsR = trySearch(searchParam);
        List<FieldData> fieldsDataList = searchResultsR.getData().getResults().getFieldsDataList();
        List<String> tgtIds = MilvusResultUtil.getFieldValues(fieldsDataList, "tgt_id");
        List<String> srcIds = MilvusResultUtil.getFieldValues(fieldsDataList, "src_id");
        List<Float> scores = searchResultsR.getData().getResults().getScoresList();

        List<RagGraphRelationDTO> dtoList = new ArrayList<>();
        Set<String> entityNameSet = new HashSet<>();
        for (int i = 0; i < tgtIds.size(); i++) {
            String tgt = tgtIds.get(i);
            String src = srcIds.get(i);
            String edgeKey = GraphUtil.generateEdgeKey(src, tgt);

            if (entityNameSet.contains(edgeKey)) {
                continue;
            }
            entityNameSet.add(edgeKey);
            RagGraphRelationDTO dto = new RagGraphRelationDTO();
            dto.setSrcId(src);
            dto.setTgtId(tgt);
            dto.setVdbScore(scores.get(i));
            dtoList.add(dto);
        }

        return dtoList.stream().filter(e -> e.getVdbScore() > filter.getMinScore()).limit(filter.getTopN())
            .collect(Collectors.toList());
    }


    private R<SearchResults> trySearch(SearchParam searchParam) {
        R<SearchResults> searchResultsR = milvusServiceClient.search(searchParam);
        if (searchResultsR.getException() != null
            && searchResultsR.getException().getMessage().contains("failed to search: collection not loaded")) {
            loadCollection();
            searchResultsR = milvusServiceClient.search(searchParam);
        }
        return searchResultsR;
    }

    public void insert(List<RagGraphRelationDTO> chunks) {
        List<String> chunkStr = chunks.stream().map(RagGraphRelationDTO::getCompleteChunk).collect(Collectors.toList());
        List<List<Float>> chunkVectors = embeddingClient.doEmbedding(chunkStr);

        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(new InsertParam.Field("id",
            chunks.stream().map(RagGraphRelationDTO::getChunkUid).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("content",
            chunkStr.stream().map(a -> a.substring(0, Math.min(a.length(), 100))).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("book_id",
            chunks.stream().map(RagGraphRelationDTO::getBookId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("file_origin_id",
            chunks.stream().map(RagGraphRelationDTO::getFileOriginId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("chunk_uid",
            chunks.stream().map(RagGraphRelationDTO::getChunkUid).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("tgt_id",
            chunks.stream().map(RagGraphRelationDTO::getTgtId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("src_id",
            chunks.stream().map(RagGraphRelationDTO::getSrcId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("keywords", chunks.stream().map(RagGraphRelationDTO::getKeywords)
            .map(a -> a.substring(0, Math.min(a.length(), 100))).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("content_vector_chunk", chunkVectors));

        InsertParam insertParam =
            InsertParam.newBuilder().withCollectionName(getCollectionName()).withFields(fields).build();
        milvusServiceClient.insert(insertParam);
    }

    public void deleteByFileOriginId(Long fileOriginId) {
        R<MutationResult> response =
            milvusServiceClient.delete(DeleteParam.newBuilder().withCollectionName(getCollectionName())
                .withExpr(getExpr(null, Collections.singletonList(fileOriginId), null)).build());

    }

    /**
     * 根据fileOriginIds删除数据
     * 
     * @param fileOriginIds 要删除的文件原始ID列表
     */
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        // 1. 构建删除表达式
        String deleteExpr = String.format("file_origin_id in [%s]", StringUtils.join(fileOriginIds, ","));

        // 2. 创建删除参数
        DeleteParam deleteParam =
            DeleteParam.newBuilder().withCollectionName(getCollectionName()).withExpr(deleteExpr).build();

        // 3. 执行删除
        R<MutationResult> response = milvusServiceClient.delete(deleteParam);

        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("删除Milvus数据失败: " + response.getMessage());
        }
    }

}
