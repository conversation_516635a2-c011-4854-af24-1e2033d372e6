package com.qnvip.qwen.dal.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentDTO;
import com.qnvip.qwen.dal.dto.ChatHistoryCommentQueryDTO;
import com.qnvip.qwen.dal.entity.ChatHistoryCommentDO;

/**
 * 聊天记录评论表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/04 11:37
 */
public interface ChatHistoryCommentDaoService {

    IPage<ChatHistoryCommentDTO> page(IPage<ChatHistoryCommentDO> page, ChatHistoryCommentQueryDTO query);

}
