package com.qnvip.qwen.dal.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.entity.FileSummaryDO;
import com.qnvip.qwen.dal.es.dto.EsNavigationInitDTO;
import com.qnvip.qwen.dal.mapper.sqlProvider.FileSummarySqlProvider;

/**
 * 文件概括表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:55
 */
@Mapper
public interface FileSummaryMapper extends CustomBaseMapper<FileSummaryDO> {

    @SelectProvider(type = FileSummarySqlProvider.class, method = "esInitList")
    List<EsNavigationInitDTO> getEsInitList(@Param("param") DataInitSearchDTO param);

    @Delete("delete from qw_file_summary where origin_id in (${fileOriginIds})")
    void deleteByFileOriginIds(@Param("fileOriginIds") String fileOriginIds);

    @Delete("delete from qw_file_summary where origin_id = #{originId}")
    void deleteByOriginId(@Param("originId") Long originId);

}