package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对话记录表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/24 16:34
 */
@Data
@TableName("qw_chat_history")
public class ChatHistoryDO extends BaseDO {


    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("会话id")
    private String conversationId;


    @ApiModelProperty("是否来自某一方（1:是，0:否）")
    private Integer fromUser;

    @ApiModelProperty("机器人Id")
    private Long agentId;

    @ApiModelProperty("工作流运行id")
    private String workflowRunId;

    @ApiModelProperty("true 使用知识库")
    private String useBook;

    @ApiModelProperty("true 使用搜索")
    private String useSearch;
}
