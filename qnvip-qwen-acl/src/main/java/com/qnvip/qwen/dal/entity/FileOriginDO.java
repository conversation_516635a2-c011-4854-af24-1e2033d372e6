package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 原始文件表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:58
 */
@Data
@TableName("qw_file_origin")
public class FileOriginDO extends BaseDO {

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("文件校验码")
    private String checksum;

    @ApiModelProperty("语雀原始文件URL")
    private String targetOriginUrl;

    @ApiModelProperty("文件类型 1:本地文件 2:语雀文件")
    private Integer type;

    @ApiModelProperty("目录")
    private String tocs;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件扩展名")
    private String fileExtension;

    @ApiModelProperty("文件大小（字节）")
    private Long fileSize;

    @ApiModelProperty("团队Id")
    private Long teamId;

    @ApiModelProperty("知识库Id")
    private Long bookId;

    @ApiModelProperty("文档的目录uid")
    private String docTableUid;

    @ApiModelProperty("文档uid")
    private String docUid;
}

