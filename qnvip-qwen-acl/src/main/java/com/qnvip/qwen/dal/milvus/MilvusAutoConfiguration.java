package com.qnvip.qwen.dal.milvus;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;



/**
 * https://milvus.io/docs/zh/install-java.md
 */
@Configuration
@EnableConfigurationProperties({MilvusProperties.class})
public class MilvusAutoConfiguration {

    @Resource
    private MilvusProperties milvusProperties;

    @Value("${itemKeyWordEmbeddingClientUrl:http://*************:8000/get_text_embedding}")
    private String itemKeyWordEmbeddingClientUrl;

    @Bean
    public MilvusServiceClient milvusServiceClient() {
        return new MilvusServiceClient(ConnectParam.newBuilder().withHost(milvusProperties.getIpAddr())
            .withPort(milvusProperties.getPort())
            .withAuthorization(milvusProperties.getUserName(), milvusProperties.getPassword()).build());
    }

    @Bean
    public EmbeddingClient embeddingClient() {
        return new EmbeddingClient(milvusProperties.getEmbeddingClientUrl());
    }

    @Bean
    public EmbeddingClient embeddingClientItemKeyWord() {
        return new EmbeddingClient(itemKeyWordEmbeddingClientUrl);
    }

    @Bean
    public MilvusQaLinkService milvusQaLinkService() {
        EmbeddingClient embeddingClient = new EmbeddingClient(milvusProperties.getEmbeddingClientUrl());
        MilvusServiceClient milvusServiceClient = new MilvusServiceClient(
            ConnectParam.newBuilder().withHost(milvusProperties.getIpAddr()).withPort(milvusProperties.getPort())
                .withAuthorization(milvusProperties.getUserName(), milvusProperties.getPassword()).build());
        return new MilvusQaLinkService(milvusServiceClient, embeddingClient);
    }

    @Bean
    public MilvusChunkDocService milvusChunkDocService() {
        EmbeddingClient embeddingClient = new EmbeddingClient(milvusProperties.getEmbeddingClientUrl());
        MilvusServiceClient milvusServiceClient = new MilvusServiceClient(ConnectParam.newBuilder()
            .withHost(milvusProperties.getIpAddr()).withPort(milvusProperties.getPort())
            .withAuthorization(milvusProperties.getUserName(), milvusProperties.getPassword()).build());
        return new MilvusChunkDocService(milvusServiceClient, embeddingClient);
    }

    // @Bean
    // public MilvusNavigationService milvusNavigationService() {
    // EmbeddingClient embeddingClient = new EmbeddingClient(milvusProperties.getEmbeddingClientUrl());
    // MilvusServiceClient milvusServiceClient = new MilvusServiceClient(
    // ConnectParam.newBuilder().withHost(milvusProperties.getIpAddr()).withPort(milvusProperties.getPort())
    // .withAuthorization(milvusProperties.getUserName(), milvusProperties.getPassword()).build());
    // return new MilvusNavigationService(milvusServiceClient, embeddingClient);
    // }
}
