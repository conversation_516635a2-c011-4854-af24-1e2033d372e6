package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件切块表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 10:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件切块表")
public class FileChunkQueryDTO extends PageParam {

    @ApiModelProperty("切块Id")
    private Long id;

    @ApiModelProperty("唯一标识")
    private String uid;

    @ApiModelProperty("原始文件Id")
    private Long fileOriginId;

    @ApiModelProperty("翻译文件Id")
    private Long fileTranslateId;

    @ApiModelProperty("排序")
    private Integer sort;
}

