package com.qnvip.qwen.dal.dto;

import com.qnvip.common.base.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文件图数据表 查询传输模型
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/07/23 20:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("文件图数据表")
public class FileGraphRelationQueryDTO extends PageParam {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("文件块唯一标识")
    private String chunkUid;

    @ApiModelProperty("原始文件ID")
    private Long fileOriginId;

    @ApiModelProperty("知识库id")
    private Long bookId;

    @ApiModelProperty("源实体")
    private String source;

    @ApiModelProperty("目标实体")
    private String target;

    @ApiModelProperty("关系词")
    private String keyword;

    @ApiModelProperty("关系强度")
    private Integer strength;

    @ApiModelProperty("关系强度")
    private String description;
}
