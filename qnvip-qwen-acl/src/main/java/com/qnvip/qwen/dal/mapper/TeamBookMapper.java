package com.qnvip.qwen.dal.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qnvip.common.base.Pageable;
import com.qnvip.orm.base.CustomBaseMapper;
import com.qnvip.qwen.dal.entity.TeamBookDO;

/**
 * 知识库表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Mapper
public interface TeamBookMapper extends CustomBaseMapper<TeamBookDO> {

    @Select("SELECT * FROM `qw_team_book` AS book " + "LEFT JOIN `qw_team_book_doc` AS doc ON book.id = doc.book_id "
        + "WHERE book.team_id = #{teamId} AND doc.parent_uid = #{parentUid}")
    Pageable<TeamBookDO> pageLoadToc(Page<TeamBookDO> page, @Param("teamId") Integer teamId,
        @Param("parentUid") String parentUid);

}