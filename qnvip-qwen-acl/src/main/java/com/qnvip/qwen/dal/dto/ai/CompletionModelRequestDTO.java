package com.qnvip.qwen.dal.dto.ai;

import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;

@Data
public class CompletionModelRequestDTO {

    /**
     * Model ID, 可以通过 List Models 获取
     */
    private String model;

    /**
     * 包含迄今为止对话的消息列表
     */
    private List<AiMessageDTO> messages;



    /**
     * 存在惩罚，介于-2.0到2.0之间的数字。正值会根据新生成的词汇是否出现在文本中来进行惩罚，增加模型讨论新话题的可能性
     */
    @JSONField(name = "presence_penalty")
    private Integer presencePenalty = 0;

    /**
     * 频率惩罚，介于-2.0到2.0之间的数字。正值会根据新生成的词汇在文本中现有的频率来进行惩罚，减少模型一字不差重复同样话语的可能性
     */
    @JSONField(name = "frequency_penalty")
    private Integer frequencyPenalty = 0;

    /**
     * 是否流式输出
     */
    private Boolean stream = false;

    @JSONField(name = "max_tokens")
    private Long maxTokens = 12288L;

    private Float temperature = 0f;

}