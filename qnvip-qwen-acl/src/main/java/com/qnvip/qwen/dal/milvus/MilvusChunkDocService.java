package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import com.qnvip.qwen.dal.dto.FilterOptionsDTO;
import com.qnvip.qwen.dal.dto.IdScoreDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;

import cn.hutool.core.collection.CollUtil;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.DataType;
import io.milvus.grpc.MutationResult;
import io.milvus.grpc.SearchResults;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import io.milvus.param.R;
import io.milvus.param.RpcStatus;
import io.milvus.param.collection.CreateCollectionParam;
import io.milvus.param.collection.FieldType;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.collection.LoadCollectionParam;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.index.CreateIndexParam;
import lombok.Getter;

@Getter
public class MilvusChunkDocService {

    private final MilvusServiceClient milvusServiceClient;

    private final EmbeddingClient embeddingClient;

    private final boolean collectionIsInit = false;

    public MilvusChunkDocService(MilvusServiceClient client, EmbeddingClient embeddingClient) {
        this.milvusServiceClient = client;
        this.embeddingClient = embeddingClient;
    }

    public String getCollectionName() {
        return "book_chunks";
    }

    public String getExpr(List<Long> bookIds, List<Long> fileOriginIds, List<String> excludedChunkIds) {
        if (CollUtil.isEmpty(bookIds) && CollUtil.isEmpty(fileOriginIds) && CollUtil.isEmpty(excludedChunkIds)) {
            return "";
        }

        List<String> conditions = new ArrayList<>();

        if (CollUtil.isNotEmpty(bookIds)) {
            String bookInExpr = String.format("book_id in [%s]",
                String.join(",", bookIds.stream().map(Object::toString).toArray(String[]::new)));
            conditions.add(bookInExpr);
        }

        if (CollUtil.isNotEmpty(fileOriginIds)) {
            String docInExpr = String.format("file_origin_id in [%s]",
                String.join(",", fileOriginIds.stream().map(Object::toString).toArray(String[]::new)));
            conditions.add(docInExpr);
        }

        if (CollUtil.isNotEmpty(excludedChunkIds)) {
            String docInExpr = String.format("chunk_uid not in [%s]",
                String.join(",", excludedChunkIds.stream().map(e -> "'" + e + "'").toArray(String[]::new)));
            conditions.add(docInExpr);
        }

        return StringUtils.join(conditions, " && ");
    }

    public void createDefaultCollection() {
        String collectionName = getCollectionName();
        R<Boolean> booleanR = milvusServiceClient
            .hasCollection(HasCollectionParam.newBuilder().withCollectionName(collectionName).build());
        if (booleanR.getData()) {
            return;
        }

        CreateCollectionParam createCollectionReq = CreateCollectionParam.newBuilder()
            .withCollectionName(collectionName)
            .addFieldType(FieldType.newBuilder().withName("id").withDataType(DataType.VarChar).withMaxLength(64)
                .withPrimaryKey(true).build())
            .addFieldType(
                FieldType.newBuilder().withName("content").withDataType(DataType.VarChar).withMaxLength(4096).build())
            .addFieldType(FieldType.newBuilder().withName("content_vector_chunk").withDataType(DataType.FloatVector)
                .withDimension(768).build())
            .addFieldType(FieldType.newBuilder().withName("book_id").withDataType(DataType.Int64).build())
            .addFieldType(FieldType.newBuilder().withName("team_id").withDataType(DataType.Int64).build())
            .addFieldType(FieldType.newBuilder().withName("file_origin_id").withDataType(DataType.Int64).build())
            .addFieldType(
                FieldType.newBuilder().withName("chunk_uid").withDataType(DataType.VarChar).withMaxLength(64).build())
            .build();
        milvusServiceClient.createCollection(createCollectionReq);

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("id").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("content_vector_chunk").withIndexType(IndexType.IVF_FLAT).withMetricType(MetricType.IP)
            .withSyncMode(Boolean.FALSE).withExtraParam("{\"nlist\":1024}").build());


        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("book_id").withIndexType(IndexType.STL_SORT).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("file_origin_id").withIndexType(IndexType.STL_SORT).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("chunk_uid").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.loadCollection(LoadCollectionParam.newBuilder().withCollectionName(collectionName).build());
    }

    public void loadCollection() {
        R<RpcStatus> response = milvusServiceClient
            .loadCollection(LoadCollectionParam.newBuilder().withCollectionName(getCollectionName()).build());
    }

    public List<String> search(List<Long> bookIds, List<Long> fileOriginIds, String question, FilterOptionsDTO filter,
        List<String> excludedChunkIds) {
        List<List<Float>> string = embeddingClient.doEmbedding(question);
        return search(bookIds, fileOriginIds, string, filter, excludedChunkIds);
    }

    public List<String> search(List<Long> bookIds, List<Long> fileOriginIds, List<List<Float>> questionVector,
        FilterOptionsDTO filter, List<String> excludedChunkIds) {

        List<IdScoreDTO<String>> idsByQuestion =
            searchContent(bookIds, fileOriginIds, "content_vector_chunk", questionVector, filter, excludedChunkIds);

        List<IdScoreDTO<String>> mergeByScore = idsByQuestion.stream().filter(Objects::nonNull)
            .sorted(Comparator.comparing(IdScoreDTO<String>::getScore).reversed()).collect(Collectors.toList());

        return mergeByScore.stream().map(IdScoreDTO::getId).distinct().collect(Collectors.toList());
    }

    private List<IdScoreDTO<String>> searchContent(List<Long> bookIds, List<Long> fileOriginIds, String vectorFieldName,
        List<List<Float>> vectors, FilterOptionsDTO filter, List<String> excludedChunkIds) {
        int nprobeVectorSize = vectors.get(0).size();
        String paramsInJson = "{\"nprobe\": " + nprobeVectorSize + "}";
        SearchParam.Builder contentVectorBuilder = SearchParam.newBuilder().withCollectionName(getCollectionName())
            .withParams(paramsInJson).withMetricType(MetricType.IP).withVectors(vectors)
            .withVectorFieldName(vectorFieldName).withTopK(filter.getTopN());

        String expr = getExpr(bookIds, fileOriginIds, excludedChunkIds);
        if (!ObjectUtils.isEmpty(expr)) {
            contentVectorBuilder.withExpr(expr);
        }

        SearchParam searchParam = contentVectorBuilder.build();

        R<SearchResults> searchResultsR = trySearch(searchParam);
        SearchResults searchResultsRData = searchResultsR.getData();
        if (ObjectUtils.isEmpty(searchResultsRData)) {
            return new ArrayList<>();
        }

        return MilvusResultUtil.filterScoresGetStringId(searchResultsRData, filter.getMinScore());
    }

    private R<SearchResults> trySearch(SearchParam searchParam) {
        R<SearchResults> searchResultsR = milvusServiceClient.search(searchParam);
        if (searchResultsR.getException() != null
            && searchResultsR.getException().getMessage().contains("failed to search: collection not loaded")) {
            loadCollection();
            searchResultsR = milvusServiceClient.search(searchParam);
        }
        return searchResultsR;
    }


    public void insert(List<RagChunkDTO> chunks) {
        List<String> chunkStr = chunks.stream().map(RagChunkDTO::getCompleteChunk).collect(Collectors.toList());
        List<List<Float>> chunkVectors = embeddingClient.doEmbedding(chunkStr);

        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(
            new InsertParam.Field("id", chunks.stream().map(RagChunkDTO::getChunkUid).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("content",
            chunkStr.stream().map(a -> a.substring(0, Math.min(a.length(), 100))).collect(Collectors.toList())));
        fields.add(
            new InsertParam.Field("book_id", chunks.stream().map(RagChunkDTO::getBookId).collect(Collectors.toList())));
        fields.add(
            new InsertParam.Field("team_id", chunks.stream().map(RagChunkDTO::getTeamId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("file_origin_id",
            chunks.stream().map(RagChunkDTO::getFileOriginId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("chunk_uid",
            chunks.stream().map(RagChunkDTO::getChunkUid).collect(Collectors.toList())));

        fields.add(new InsertParam.Field("content_vector_chunk", chunkVectors));

        InsertParam insertParam =
            InsertParam.newBuilder().withCollectionName(getCollectionName()).withFields(fields).build();
        milvusServiceClient.insert(insertParam);
    }

    /**
     * 根据fileOriginIds删除数据
     * 
     * @param fileOriginIds 要删除的文件原始ID列表
     */
    public void deleteByFileOriginIds(Collection<Long> fileOriginIds) {
        // 1. 构建删除表达式
        String deleteExpr = String.format("file_origin_id in [%s]", StringUtils.join(fileOriginIds, ","));

        // 2. 创建删除参数
        DeleteParam deleteParam =
            DeleteParam.newBuilder().withCollectionName(getCollectionName()).withExpr(deleteExpr).build();

        // 3. 执行删除
        R<MutationResult> response = milvusServiceClient.delete(deleteParam);

        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("删除Milvus数据失败: " + response.getMessage());
        }
    }
}
