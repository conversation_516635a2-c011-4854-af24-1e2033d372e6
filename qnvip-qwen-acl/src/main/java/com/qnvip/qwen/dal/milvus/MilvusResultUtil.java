package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.List;

import com.qnvip.qwen.dal.dto.IdScoreDTO;

import io.milvus.grpc.FieldData;
import io.milvus.grpc.SearchResults;

public class MilvusResultUtil {

    public static List<IdScoreDTO<String>> filterScoresGetStringId(SearchResults searchResults, float threshold) {
        List<IdScoreDTO<String>> filtered = new ArrayList<>();

        List<Float> scores = searchResults.getResults().getScoresList();
        List<String> ids = searchResults.getResults().getIds().getStrId().getDataList();

        for (int i = 0; i < ids.size(); i++) {
            if (scores.get(i) >= threshold) {
                filtered.add(IdScoreDTO.<String>builder().id(ids.get(i)).score(scores.get(i)).build());
            }
        }

        return filtered;
    }

    public static List<IdScoreDTO<Long>> filterScoresGetLongId(SearchResults searchResults, float threshold) {
        List<IdScoreDTO<Long>> filtered = new ArrayList<>();

        List<Float> scores = searchResults.getResults().getScoresList();
        List<Long> ids = searchResults.getResults().getIds().getIntId().getDataList();

        for (int i = 0; i < ids.size(); i++) {
            if (scores.get(i) >= threshold) {
                filtered.add(IdScoreDTO.<Long>builder().id(ids.get(i)).score(scores.get(i)).build());
            }
        }

        return filtered;
    }

    public static List<String> getFieldValues(List<FieldData> fieldsDataList, String fieldName) {
        for (FieldData fieldData : fieldsDataList) {
            if (fieldName.equals(fieldData.getFieldName())) {
                return fieldData.getScalars().getStringData().getDataList();

            }
        }
        return new ArrayList<>();
    }

}
