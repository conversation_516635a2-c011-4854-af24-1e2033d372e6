package com.qnvip.qwen.dal.milvus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.qnvip.qwen.dal.dto.FilterOptionsDTO;
import com.qnvip.qwen.dal.dto.RagGraphEntityDTO;

import cn.hutool.core.collection.CollUtil;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.DataType;
import io.milvus.grpc.FieldData;
import io.milvus.grpc.MutationResult;
import io.milvus.grpc.SearchResults;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import io.milvus.param.R;
import io.milvus.param.RpcStatus;
import io.milvus.param.collection.CreateCollectionParam;
import io.milvus.param.collection.FieldType;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.collection.LoadCollectionParam;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.index.CreateIndexParam;
import lombok.Getter;

@Getter
@Repository
public class MilvusGraphEntityService {

    private final MilvusServiceClient milvusServiceClient;

    private final EmbeddingClient embeddingClient;

    private final boolean collectionIsInit = false;

    public MilvusGraphEntityService(MilvusServiceClient client, EmbeddingClient embeddingClient) {
        this.milvusServiceClient = client;
        this.embeddingClient = embeddingClient;
    }


    public String getCollectionName() {
        return "graph_entity_v1";
    }

    public String getExpr(List<Long> bookIds, List<Long> fileOriginIds, List<String> excludedChunkIds) {
        if (CollUtil.isEmpty(bookIds) && CollUtil.isEmpty(fileOriginIds) && CollUtil.isEmpty(excludedChunkIds)) {
            return "";
        }

        List<String> conditions = new ArrayList<>();

        if (CollUtil.isNotEmpty(bookIds)) {
            String bookInExpr = String.format("book_id in [%s]",
                String.join(",", bookIds.stream().map(Object::toString).toArray(String[]::new)));
            conditions.add(bookInExpr);
        }

        if (CollUtil.isNotEmpty(fileOriginIds)) {
            String docInExpr = String.format("file_origin_id in [%s]",
                String.join(",", fileOriginIds.stream().map(Object::toString).toArray(String[]::new)));
            conditions.add(docInExpr);
        }

        if (CollUtil.isNotEmpty(excludedChunkIds)) {
            String docInExpr = String.format("chunk_uid not in [%s]",
                String.join(",", excludedChunkIds.stream().map(e -> "'" + e + "'").toArray(String[]::new)));
            conditions.add(docInExpr);
        }

        return StringUtils.join(conditions, " && ");
    }

    public void createDefaultCollection() {
        String collectionName = getCollectionName();
        R<Boolean> booleanR = milvusServiceClient
            .hasCollection(HasCollectionParam.newBuilder().withCollectionName(collectionName).build());
        if (booleanR.getData()) {
            return;
        }

        CreateCollectionParam createCollectionReq = CreateCollectionParam.newBuilder()
            .withCollectionName(collectionName)
            .addFieldType(FieldType.newBuilder().withName("id").withDataType(DataType.VarChar).withMaxLength(64)
                .withPrimaryKey(true).build())
            .addFieldType(
                FieldType.newBuilder().withName("content").withDataType(DataType.VarChar).withMaxLength(4096).build())
            .addFieldType(FieldType.newBuilder().withName("entity_type").withDataType(DataType.VarChar)
                .withMaxLength(128).build())
            .addFieldType(FieldType.newBuilder().withName("entity_name").withDataType(DataType.VarChar)
                .withMaxLength(128).build())
            .addFieldType(FieldType.newBuilder().withName("content_vector_chunk").withDataType(DataType.FloatVector)
                .withDimension(768).build())

            .addFieldType(FieldType.newBuilder().withName("book_id").withDataType(DataType.Int64).build())
            .addFieldType(FieldType.newBuilder().withName("file_origin_id").withDataType(DataType.Int64).build())
            .addFieldType(
                FieldType.newBuilder().withName("chunk_uid").withDataType(DataType.VarChar).withMaxLength(64).build())
            .build();
        milvusServiceClient.createCollection(createCollectionReq);

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("id").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("content_vector_chunk").withIndexType(IndexType.IVF_FLAT).withMetricType(MetricType.IP)
            .withSyncMode(Boolean.FALSE).withExtraParam("{\"nlist\":1024}").build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("book_id").withIndexType(IndexType.STL_SORT).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("file_origin_id").withIndexType(IndexType.STL_SORT).build());

        milvusServiceClient.createIndex(CreateIndexParam.newBuilder().withCollectionName(getCollectionName())
            .withFieldName("chunk_uid").withIndexType(IndexType.TRIE).build());

        milvusServiceClient.loadCollection(LoadCollectionParam.newBuilder().withCollectionName(collectionName).build());
    }

    public void loadCollection() {
        R<RpcStatus> response = milvusServiceClient
            .loadCollection(LoadCollectionParam.newBuilder().withCollectionName(getCollectionName()).build());
    }

    public List<RagGraphEntityDTO> search(List<Long> bookIds, List<Long> fileOriginIds, String question,
        FilterOptionsDTO filter, List<String> excludedChunkIds) {
        List<List<Float>> string = embeddingClient.doEmbedding(question);
        return searchContent(bookIds, fileOriginIds, string, filter, excludedChunkIds);
    }

    private List<RagGraphEntityDTO> searchContent(List<Long> bookIds, List<Long> fileOriginIds,
        List<List<Float>> vectors, FilterOptionsDTO filter, List<String> excludedChunkIds) {

        SearchParam.Builder contentVectorBuilder = SearchParam.newBuilder().withCollectionName(getCollectionName())
            .withParams("{\"nprobe\": " + vectors.get(0).size() + "}").withMetricType(MetricType.IP)
            .withVectors(vectors).withVectorFieldName("content_vector_chunk").withTopK(filter.getTopN())
            .addOutField("entity_name");

        String expr = getExpr(bookIds, fileOriginIds, excludedChunkIds);
        if (!ObjectUtils.isEmpty(expr)) {
            contentVectorBuilder.withExpr(expr);
        }

        SearchParam searchParam = contentVectorBuilder.build();

        R<SearchResults> searchResultsR = trySearch(searchParam);

        SearchResults searchResultsRData = searchResultsR.getData();
        if (ObjectUtils.isEmpty(searchResultsRData)) {
            return new ArrayList<>();
        }

        List<FieldData> fieldsDataList = searchResultsRData.getResults().getFieldsDataList();
        List<String> entityNames = MilvusResultUtil.getFieldValues(fieldsDataList, "entity_name");
        List<Float> scores = searchResultsRData.getResults().getScoresList();

        List<RagGraphEntityDTO> dtoList = new ArrayList<>();
        Set<String> entityNameSet = new HashSet<>();
        for (int i = 0; i < entityNames.size(); i++) {
            String entityName = entityNames.get(i);
            if (entityNameSet.contains(entityName)) {
                continue;
            }
            entityNameSet.add(entityName);
            RagGraphEntityDTO dto = new RagGraphEntityDTO();
            dto.setEntityName(entityName);
            dto.setVdbScore(scores.get(i));
            dtoList.add(dto);
        }

        return dtoList.stream().filter(e -> e.getVdbScore() > filter.getMinScore()).limit(filter.getTopN())
            .collect(Collectors.toList());
    }

    private R<SearchResults> trySearch(SearchParam searchParam) {
        R<SearchResults> searchResultsR = milvusServiceClient.search(searchParam);
        if (searchResultsR.getException() != null
            && searchResultsR.getException().getMessage().contains("failed to search: collection not loaded")) {
            loadCollection();
            searchResultsR = milvusServiceClient.search(searchParam);
        }
        return searchResultsR;
    }

    public void insert(List<RagGraphEntityDTO> chunks) {
        List<String> chunkStr = chunks.stream().map(RagGraphEntityDTO::getCompleteChunk).collect(Collectors.toList());
        List<List<Float>> chunkVectors = embeddingClient.doEmbedding(chunkStr);

        List<InsertParam.Field> fields = new ArrayList<>();
        fields.add(new InsertParam.Field("id",
            chunks.stream().map(RagGraphEntityDTO::getChunkUid).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("content",
            chunkStr.stream().map(a -> a.substring(0, Math.min(a.length(), 100))).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("book_id",
            chunks.stream().map(RagGraphEntityDTO::getBookId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("file_origin_id",
            chunks.stream().map(RagGraphEntityDTO::getFileOriginId).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("chunk_uid",
            chunks.stream().map(RagGraphEntityDTO::getChunkUid).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("entity_name",
            chunks.stream().map(RagGraphEntityDTO::getEntityName).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("entity_type",
            chunks.stream().map(RagGraphEntityDTO::getEntityType).collect(Collectors.toList())));
        fields.add(new InsertParam.Field("content_vector_chunk", chunkVectors));

        InsertParam insertParam =
            InsertParam.newBuilder().withCollectionName(getCollectionName()).withFields(fields).build();
        milvusServiceClient.insert(insertParam);
    }

    public void deleteByFileOriginId(Long fileOriginId) {
        R<MutationResult> response =
            milvusServiceClient.delete(DeleteParam.newBuilder().withCollectionName(getCollectionName())
                .withExpr(getExpr(null, Collections.singletonList(fileOriginId), null)).build());

    }

    /**
     * 根据fileOriginIds删除数据
     * 
     * @param fileOriginIds 要删除的文件原始ID列表
     */
    public void deleteByFileOriginIds(List<Long> fileOriginIds) {
        // 1. 构建删除表达式
        String deleteExpr = String.format("file_origin_id in [%s]", StringUtils.join(fileOriginIds, ","));

        // 2. 创建删除参数
        DeleteParam deleteParam =
            DeleteParam.newBuilder().withCollectionName(getCollectionName()).withExpr(deleteExpr).build();

        // 3. 执行删除
        R<MutationResult> response = milvusServiceClient.delete(deleteParam);

        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("删除Milvus数据失败: " + response.getMessage());
        }
    }
}
