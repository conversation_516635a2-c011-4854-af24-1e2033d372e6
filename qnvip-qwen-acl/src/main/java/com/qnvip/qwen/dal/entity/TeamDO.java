package com.qnvip.qwen.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 团队表
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/25 16:35
 */
@Data
@TableName("qw_team")
public class TeamDO extends BaseDO {


    @ApiModelProperty("团队类型 1语雀，2飞书")
    private Integer type;

    @ApiModelProperty("团队名称")
    private String name;

    @ApiModelProperty("团队值")
    private String mark;

    @ApiModelProperty("appId和secret")
    private String appIdSecretJson;

}
