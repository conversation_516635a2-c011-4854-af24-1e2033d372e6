package com.qnvip.qwen.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseService;
import com.qnvip.qwen.dal.dto.FileQaDTO;
import com.qnvip.qwen.dal.dto.FileQaQueryDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.entity.FileQaDO;

/**
 * 文档QA表 存储层
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/03/31 17:10
 */
public interface FileQaDaoService extends BaseService<FileQaDO> {

    IPage<FileQaDTO> page(IPage<FileQaDO> page, FileQaQueryDTO query);

    void deleteByFileOriginId(Long fileOriginId);

    List<FileQaDO> getSortedQaByUIds(List<String> uids);

    List<RagChunkDTO> getMilvusInitList(List<Long> fileOriginIds);

    void deleteByFileOriginIds(List<Long> fileOriginIds);
}
