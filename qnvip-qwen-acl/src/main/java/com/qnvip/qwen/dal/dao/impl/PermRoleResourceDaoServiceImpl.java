package com.qnvip.qwen.dal.dao.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qnvip.orm.base.BaseServiceImpl;
import com.qnvip.qwen.dal.dao.PermRoleResourceDaoService;
import com.qnvip.qwen.dal.dto.PermRoleResourceDTO;
import com.qnvip.qwen.dal.dto.PermRoleResourceQueryDTO;
import com.qnvip.qwen.dal.entity.PermRoleResourceDO;
import com.qnvip.qwen.dal.mapper.PermRoleResourceMapper;
import com.qnvip.qwen.util.CopierUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色资源表 存储层实现
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermRoleResourceDaoServiceImpl extends BaseServiceImpl<PermRoleResourceMapper, PermRoleResourceDO>
    implements PermRoleResourceDaoService {
    @Resource
    private PermRoleResourceMapper permRoleResourceMapper;

    @Override
    public IPage<PermRoleResourceDTO> page(IPage<PermRoleResourceDO> page, PermRoleResourceQueryDTO query) {
        IPage<PermRoleResourceDO> pageData =
            lambdaQuery().setEntity(CopierUtil.copy(query, PermRoleResourceDO.class)).page(page);
        return pageData.convert(e -> CopierUtil.copy(e, PermRoleResourceDTO.class));
    }
}
