from pymilvus import connections, CollectionSchema, FieldSchema, DataType, Collection

# 连接到 Milvus 服务
connections.connect("default", host="127.0.0.1", port="19530")

# 定义字段
fields = [
    FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=32, is_primary=True, description="主键"),
    FieldSchema(name="content_vector_question", dtype=DataType.FLOAT_VECTOR, dim=768, description="问题向量"),
    FieldSchema(name="content_vector_answer", dtype=DataType.FLOAT_VECTOR, dim=768, description="答案向量"),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=2048, description="文本内容"),
    FieldSchema(name="book_id", dtype=DataType.INT64, description="书籍ID"),
    FieldSchema(name="file_origin_id", dtype=DataType.INT64, description="文件来源ID"),
    FieldSchema(name="chunk_uid", dtype=DataType.VARCHAR, max_length=32, description="块唯一标识")
]

# 创建集合
schema = CollectionSchema(fields, description="book_doc_1 集合")
collection = Collection(name="book_doc_1", schema=schema)

# 为向量字段创建索引
index_params = {
    "metric_type": "IP",
    "index_type": "IVF_FLAT",
    "params": {"nlist": 1024}
}

collection.create_index(field_name="content_vector_question", index_params=index_params)
collection.create_index(field_name="content_vector_answer", index_params=index_params)

collection.create_index(
    field_name="id",
    index_params={"index_type": "AUTOINDEX"},
    index_name="id_idx"
)

collection.create_index(
    field_name="book_id",
    index_params={"index_type": "AUTOINDEX"},
    index_name="book_id_idx"
)

collection.create_index(
    field_name="file_origin_id",
    index_params={"index_type": "AUTOINDEX"},
    index_name="file_origin_id_idx"
)
collection.create_index(
    field_name="chunk_uid",
    index_params={"index_type": "AUTOINDEX"},
    index_name="chunk_uid_idx"
)

# 加载集合到内存
collection.load()

print("集合创建成功，向量字段索引已建立。")