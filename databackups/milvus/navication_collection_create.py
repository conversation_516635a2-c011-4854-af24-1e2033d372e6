from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility, Index

# 连接到 Milvus 服务
connections.connect(
    alias="default",
    host='localhost',
    port='19530'
)

# 定义集合的字段
fields = [
    FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
    FieldSchema(name="content_vector_tocssummary", dtype=DataType.FLOAT_VECTOR, dim=768),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=2048),
    FieldSchema(name="book_id", dtype=DataType.INT64),
    FieldSchema(name="file_origin_id", dtype=DataType.INT64)
]

# 创建集合的 Schema
schema = CollectionSchema(fields=fields, description="Navigation collection")

# 创建集合
collection_name = "navigation_1"
if utility.has_collection(collection_name):
    utility.drop_collection(collection_name)
collection = Collection(name=collection_name, schema=schema)

# 为向量字段创建索引
index_params = {
    "metric_type": "IP",  # 或者 "IP"，取决于你的需求
    "index_type": "IVF_FLAT",
    "params": {"nlist": 1024}  # 设置 nlist 为 1024
}
collection.create_index(
    field_name="content_vector_tocssummary",
    index_params=index_params,
    index_name="content_vector_tocssummary_idx"
)

# 为标量字段使用 autoindex
collection.create_index(
    field_name="id",
    index_params={"index_type": "AUTOINDEX"},
    index_name="id_idx"
)

collection.create_index(
    field_name="book_id",
    index_params={"index_type": "AUTOINDEX"},
    index_name="book_id_idx"
)

collection.create_index(
    field_name="file_origin_id",
    index_params={"index_type": "AUTOINDEX"},
    index_name="file_origin_id_idx"
)

# 加载集合到内存
collection.load()

print(f"Collection {collection_name} created and indexed successfully.")