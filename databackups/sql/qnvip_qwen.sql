/*
 Navicat Premium Dump SQL

 Source Server         : qnvip-qwen-test
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : rm-bp13dk19dcrn4mj55do.mysql.rds.aliyuncs.com:3306
 Source Schema         : qnvip_qwen

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 19/06/2025 17:21:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for db_dump
-- ----------------------------
DROP TABLE IF EXISTS `db_dump`;
CREATE TABLE `db_dump`  (
  `C1` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL
) ENGINE = InnoDB AUTO_INCREMENT = 1460898 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_account
-- ----------------------------
DROP TABLE IF EXISTS `qw_account`;
CREATE TABLE `qw_account`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账号Id',
  `user_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '用户Id',
  `type` int NULL DEFAULT 0 COMMENT '账号类型 1:qnvipoa, 2:账号密码',
  `account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '账号信息',
  `status` int NULL DEFAULT 0 COMMENT '账号状态 0正常 -1禁用',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '账号表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_api_config
-- ----------------------------
DROP TABLE IF EXISTS `qw_api_config`;
CREATE TABLE `qw_api_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置Id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '配置名称',
  `request_agent` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '请求代理类型 0:直连 1:dify',
  `model_provider` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '模型提供商 1:ollama 2:vllm 3:volcengine 4:localai',
  `model_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模型名称',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'API密钥',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'API地址',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'API配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_chat_history
-- ----------------------------
DROP TABLE IF EXISTS `qw_chat_history`;
CREATE TABLE `qw_chat_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话记录Id',
  `group_type` int NULL DEFAULT 0 COMMENT '智能体分组1qwen',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户Id',
  `from_user` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否来自某一方（1:是，0:否）',
  `agent_id` bigint NULL DEFAULT 0 COMMENT '机器人Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  `conversation_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '会话id',
  `workflow_run_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作流运行id',
  `use_book` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '使用知识库',
  `use_search` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '使用联网搜索',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `conversation_id_idx`(`conversation_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3145 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '对话记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_chat_history_comment
-- ----------------------------
DROP TABLE IF EXISTS `qw_chat_history_comment`;
CREATE TABLE `qw_chat_history_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户Id',
  `conversation_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会话Id',
  `message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息id',
  `like_flag` tinyint NOT NULL DEFAULT 0 COMMENT '是否点赞，1:是，0:否 -1:踩',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评论内容',
  `evaluate_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价标签',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id`(20) ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 45 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '聊天记录评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_chat_history_data
-- ----------------------------
DROP TABLE IF EXISTS `qw_chat_history_data`;
CREATE TABLE `qw_chat_history_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话记录Id',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3145 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '对话记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_dept
-- ----------------------------
DROP TABLE IF EXISTS `qw_dept`;
CREATE TABLE `qw_dept`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门Id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_dify_app_apikey
-- ----------------------------
DROP TABLE IF EXISTS `qw_dify_app_apikey`;
CREATE TABLE `qw_dify_app_apikey`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'difyApiKey',
  `model_id` bigint NOT NULL COMMENT '模型id',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'url',
  `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '@name:类型, @dicts:[chat]',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dify_api_key`(`api_key`(20) ASC) USING BTREE,
  INDEX `idx_url`(`url`(20) ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'dify应用的apikey表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_file_chunk
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_chunk`;
CREATE TABLE `qw_file_chunk`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '切块Id',
  `uid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '唯一标识',
  `file_origin_id` bigint NULL DEFAULT 0 COMMENT '原始文件Id',
  `file_translate_id` bigint NULL DEFAULT 0 COMMENT '翻译文件Id',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_fid_sort`(`file_origin_id` ASC, `sort` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 459382 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件切块表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_chunk_data
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_chunk_data`;
CREATE TABLE `qw_file_chunk_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '切块ID',
  `chunk_uid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文件切块唯一标识',
  `file_origin_id` bigint NOT NULL DEFAULT 0 COMMENT '文件ID',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文件切块内容',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_chunk_id`(`chunk_uid` ASC) USING BTREE,
  INDEX `idx_file_origin_id`(`file_origin_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 459382 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件切块数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_origin
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_origin`;
CREATE TABLE `qw_file_origin`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件Id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父id',
  `checksum` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '文件校验码',
  `target_origin_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '语雀原始文件URL',
  `type` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '文件类型 1:本地文件 2:语雀文件',
  `tocs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件的目录',
  `file_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '文件名称',
  `file_extension` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '文件扩展名',
  `file_size` bigint UNSIGNED NULL DEFAULT 0 COMMENT '文件大小（字节）',
  `team_id` bigint NULL DEFAULT 0 COMMENT '团队Id',
  `book_id` bigint NULL DEFAULT 0 COMMENT '知识库Id',
  `doc_table_uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文档的目录id 最后一级',
  `doc_uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文档Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_bookId_docuid`(`book_id` ASC, `doc_uid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10452 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '原始文件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_qa
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_qa`;
CREATE TABLE `qw_file_qa`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档QA Id',
  `uid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '唯一标识',
  `file_origin_id` bigint NULL DEFAULT 0 COMMENT '原文档Id',
  `file_translate_id` bigint NULL DEFAULT 0 COMMENT '翻译文档Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  `file_chunk_uid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '切块唯一标识',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1927475870001520651 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文档QA表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_qa_data
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_qa_data`;
CREATE TABLE `qw_file_qa_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '问答数据Id',
  `file_origin_id` bigint NOT NULL DEFAULT 0 COMMENT '原始文件ID',
  `qa_uid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '问答标识',
  `question` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '问题内容',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '答案内容',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_qa_uid`(`qa_uid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1927475870018297868 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件问答数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_recall_log
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_recall_log`;
CREATE TABLE `qw_file_recall_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志Id',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户Id',
  `question_id` bigint NULL DEFAULT 0 COMMENT '问题Id',
  `recall_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '召回类型hasPermit,noPermit',
  `recall_source` int NULL DEFAULT 0 COMMENT '召回来源1es 2milvus',
  `recall_from` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '召回源 navigation_milvus  navigation_es_first navi',
  `recall_stage` int NULL DEFAULT 0 COMMENT '召回阶段10导航召回 20内容召回 30周围分块召回 40重排序召回  50 结果封装',
  `book_id` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '库id',
  `file_origin_id` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '原始文件Id',
  `file_translate_id` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '翻译文件Id',
  `file_chunk_uid` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '分块文件Id',
  `file_qa_uid` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '问答文件Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1935626196465065986 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件召回日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_summary
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_summary`;
CREATE TABLE `qw_file_summary`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `origin_id` bigint NULL DEFAULT 0 COMMENT '文件Id',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '标签Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_origin_id`(`origin_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34777 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件概括表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_tag
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_tag`;
CREATE TABLE `qw_file_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `origin_id` bigint NULL DEFAULT 0 COMMENT '文件Id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '标签Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_origin_id`(`origin_id` ASC) USING BTREE,
  INDEX `idx_tag_id`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 229838 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_task_progress
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_task_progress`;
CREATE TABLE `qw_file_task_progress`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务进度Id',
  `before_progress_id` bigint NULL DEFAULT 0 COMMENT '上一任务进度id',
  `file_origin_id` bigint NULL DEFAULT 0 COMMENT '原始文件Id',
  `now_step` int NULL DEFAULT 0 COMMENT '任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70文章摘要 90完结',
  `status` int NULL DEFAULT 0 COMMENT '状态 0待开始 1进行中 2已结束 3异常',
  `process_start_time` datetime(4) NULL DEFAULT NULL COMMENT '处理开始时间',
  `process_finish_time` datetime(4) NULL DEFAULT NULL COMMENT '处理结束时间',
  `max_retry_times` int NULL DEFAULT 0 COMMENT '最大重试次数',
  `retry_times` int NULL DEFAULT 0 COMMENT '重试次数',
  `err_time` datetime(4) NULL DEFAULT NULL COMMENT '错误发生时间',
  `err_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  `book_id` bigint NOT NULL COMMENT '库id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_file_origin_id`(`file_origin_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 192693 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件任务进度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_task_progress_datalog
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_task_progress_datalog`;
CREATE TABLE `qw_file_task_progress_datalog`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务进度Id',
  `file_origin_id` bigint NULL DEFAULT 0 COMMENT '原始文件Id',
  `task_progress_id` bigint NULL DEFAULT 0 COMMENT '任务步骤id',
  `now_step` int NULL DEFAULT 0 COMMENT '任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70文章摘要 90完结',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '数据',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_file_origin_id`(`file_origin_id` ASC, `now_step` ASC) USING BTREE,
  INDEX `idx_task_progress_id`(`task_progress_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 157417 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件任务进度的产出数据日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_translate
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_translate`;
CREATE TABLE `qw_file_translate`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件转换Id',
  `file_origin_id` bigint NULL DEFAULT 0 COMMENT '文件原始Id',
  `translate_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '转换后的URL地址',
  `file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '文件名称',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件转换表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_file_translate_data
-- ----------------------------
DROP TABLE IF EXISTS `qw_file_translate_data`;
CREATE TABLE `qw_file_translate_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件转换数据Id',
  `translate_id` bigint NULL DEFAULT 0 COMMENT '翻译任务Id',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '转换后的文件内容',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_chunk_id`(`translate_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件转换数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_llm_support_model
-- ----------------------------
DROP TABLE IF EXISTS `qw_llm_support_model`;
CREATE TABLE `qw_llm_support_model`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模型名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模型图标',
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模型描述',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_name`(`name`(20) ASC) USING BTREE,
  INDEX `idx_icon`(`icon`(20) ASC) USING BTREE,
  INDEX `idx_description`(`description`(20) ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支持的模型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_open_item_keyword
-- ----------------------------
DROP TABLE IF EXISTS `qw_open_item_keyword`;
CREATE TABLE `qw_open_item_keyword`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品关键词Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `platform_id` int NOT NULL DEFAULT 0 COMMENT '平台id 1-租赁 2-分期',
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_keyword`(`keyword`(20) ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 82 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品关键词表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_perm_role
-- ----------------------------
DROP TABLE IF EXISTS `qw_perm_role`;
CREATE TABLE `qw_perm_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名字',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_role_name`(`role_name`(20) ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_perm_role_resource
-- ----------------------------
DROP TABLE IF EXISTS `qw_perm_role_resource`;
CREATE TABLE `qw_perm_role_resource`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `role_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `resource_id` bigint NOT NULL DEFAULT 0 COMMENT '资源id -1全部权限',
  `resource_type` int NOT NULL DEFAULT 0 COMMENT '@name:资源类型, @dicts:[1-知识库资源]',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE,
  INDEX `idx_resource_id`(`resource_id` ASC) USING BTREE,
  INDEX `idx_resource_type`(`resource_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 794 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色资源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_perm_user_role
-- ----------------------------
DROP TABLE IF EXISTS `qw_perm_user_role`;
CREATE TABLE `qw_perm_user_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户Id',
  `role_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_prompt
-- ----------------------------
DROP TABLE IF EXISTS `qw_prompt`;
CREATE TABLE `qw_prompt`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '提示词Id',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示词键',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '提示词内容',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '提示词表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_prompt_role_category
-- ----------------------------
DROP TABLE IF EXISTS `qw_prompt_role_category`;
CREATE TABLE `qw_prompt_role_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_name`(`name`(20) ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色提示词分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_prompt_role_template
-- ----------------------------
DROP TABLE IF EXISTS `qw_prompt_role_template`;
CREATE TABLE `qw_prompt_role_template`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `category_id` bigint NOT NULL DEFAULT 0 COMMENT '分类Id',
  `public_flag` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否公共标志，1为公共，0为个人',
  `creator_user_id` bigint NOT NULL DEFAULT 0 COMMENT '创建者用户Id，0为系统',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_creator_user_id`(`creator_user_id` ASC) USING BTREE,
  INDEX `idx_title`(`title`(20) ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色提示词模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_recall_intent_summary
-- ----------------------------
DROP TABLE IF EXISTS `qw_recall_intent_summary`;
CREATE TABLE `qw_recall_intent_summary`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `file_origin_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '源文档Id',
  `recall_type` int NULL DEFAULT 0 COMMENT '召回类型 1: SQL, 10: 其他',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '内容总结',
  `recall_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '召回值 (recall_type=1时为表名字，recall_type=10时为文档id)',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_file_origin_id`(`file_origin_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '召回意图概括表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_system_dict
-- ----------------------------
DROP TABLE IF EXISTS `qw_system_dict`;
CREATE TABLE `qw_system_dict`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '修改时间',
  `create_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `dict_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典类型，key值',
  `p_dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '父级字典key值',
  `dict_label` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典键值',
  `dict_sort` int NOT NULL DEFAULT 0 COMMENT '字典排序',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qw_team
-- ----------------------------
DROP TABLE IF EXISTS `qw_team`;
CREATE TABLE `qw_team`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '团队Id',
  `mark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '团队值',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  `type` int NOT NULL COMMENT '团队类型 1语雀，2飞书',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '团队名称',
  `app_id_secret_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '飞书团队的配置文件',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mark`(`mark` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '团队表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_team_book
-- ----------------------------
DROP TABLE IF EXISTS `qw_team_book`;
CREATE TABLE `qw_team_book`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '知识库Id',
  `team_id` bigint NULL DEFAULT 0 COMMENT '团队Id',
  `team_mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '团队唯一标识',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '三方唯一标识',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '知识库名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `content_update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '内容更新时间',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  `type` int NULL DEFAULT 0 COMMENT '团队类型 1语雀，2飞书',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teamId_mark`(`team_id` ASC, `mark` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '知识库表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_team_book_doc
-- ----------------------------
DROP TABLE IF EXISTS `qw_team_book_doc`;
CREATE TABLE `qw_team_book_doc`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文档Id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父id',
  `team_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '所属团队Id',
  `parent_uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '父id',
  `book_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '知识库Id',
  `type` int NOT NULL DEFAULT 0 COMMENT '类型 1文章 2目录',
  `mark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '三方唯一标识',
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '第三方uid',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '文章或者目录名字',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_bookId_mark`(`book_id` ASC, `mark` ASC) USING BTREE,
  INDEX `idx_bookId_uid`(`book_id` ASC, `uid` ASC) USING BTREE,
  INDEX `idx_parent_uid`(`parent_uid` ASC) USING BTREE,
  INDEX `idx_uid`(`uid` ASC) USING BTREE,
  INDEX `idx_update_time`(`update_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11603 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文档表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_team_book_doc_data
-- ----------------------------
DROP TABLE IF EXISTS `qw_team_book_doc_data`;
CREATE TABLE `qw_team_book_doc_data`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文档Id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父id',
  `doc_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '所属团队Id',
  `book_id` bigint NOT NULL DEFAULT 0 COMMENT '知识库id',
  `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '第三方唯一标识',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文档类型',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文章',
  `content_update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '内容更新时间',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_doc_uid`(`doc_uid` ASC) USING BTREE,
  INDEX `idx_mark`(`mark` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10442 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文档表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_user
-- ----------------------------
DROP TABLE IF EXISTS `qw_user`;
CREATE TABLE `qw_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户Id',
  `nickname` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户昵称',
  `status` int NULL DEFAULT 0 COMMENT '用户状态 0正常 -1禁用',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_user_dept_rela
-- ----------------------------
DROP TABLE IF EXISTS `qw_user_dept_rela`;
CREATE TABLE `qw_user_dept_rela`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联Id',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户Id',
  `dept_id` bigint NULL DEFAULT 0 COMMENT '部门Id',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_user_token_config
-- ----------------------------
DROP TABLE IF EXISTS `qw_user_token_config`;
CREATE TABLE `qw_user_token_config`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `permission_authorized_token` bigint NOT NULL DEFAULT 0 COMMENT '权限授权token',
  `used_token` bigint NOT NULL DEFAULT 0 COMMENT '月度已使用的token'
) ENGINE = InnoDB AUTO_INCREMENT = 637 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户token用量配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for qw_user_token_consumption
-- ----------------------------
DROP TABLE IF EXISTS `qw_user_token_consumption`;
CREATE TABLE `qw_user_token_consumption`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户Id',
  `user_input_prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户输入预览',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '应用id',
  `conversation_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会话id',
  `total_consumed_token` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗总token',
  `input_consumed_token` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗输入token',
  `output_consumed_token` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗输出token',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1001 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户token消耗表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户Id',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户手机号码',
  `birthplace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户出生地',
  `is_allow_login` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否允许登录标志',
  `type` int NOT NULL DEFAULT 0 COMMENT '@name:用户类型, @dicts:[1-普通用户,2-会员]',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_nickname`(`nickname`(20) ASC) USING BTREE,
  INDEX `idx_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_birthplace`(`birthplace`(20) ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
