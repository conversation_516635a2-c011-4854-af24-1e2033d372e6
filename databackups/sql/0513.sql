SET FOREIGN_KEY_CHECKS=0;


CREATE TABLE `qnvip_qwen`.`qw_perm_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名字',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_role_name`(`role_name`(20) ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

CREATE TABLE `qnvip_qwen`.`qw_perm_role_resource`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `role_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `resource_id` bigint NOT NULL DEFAULT 0 COMMENT '资源id -1全部权限',
  `resource_type` int NOT NULL DEFAULT 0 COMMENT '@name:资源类型, @dicts:[1-知识库资源]',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE,
  INDEX `idx_resource_id`(`resource_id` ASC) USING BTREE,
  INDEX `idx_resource_type`(`resource_type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色资源表' ROW_FORMAT = Dynamic;

CREATE TABLE `qnvip_qwen`.`qw_perm_user_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户Id',
  `role_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色表' ROW_FORMAT = Dynamic;

CREATE TABLE `qnvip_qwen`.`qw_user_token_config`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT '主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `permission_authorized_token` bigint NOT NULL DEFAULT 0 COMMENT '权限授权token',
  `used_token` bigint NOT NULL DEFAULT 0 COMMENT '月度已使用的token'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户token用量配置表' ROW_FORMAT = Dynamic;

CREATE TABLE `qnvip_qwen`.`qw_user_token_consumption`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户Id',
  `user_input_prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户输入预览',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '应用id',
  `conversation_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会话id',
  `total_consumed_token` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗总token',
  `input_consumed_token` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗输入token',
  `output_consumed_token` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗输出token',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户token消耗表' ROW_FORMAT = Dynamic;


INSERT INTO `qnvip_qwen`.`qw_perm_role` (`id`, `create_time`, `update_time`, `is_deleted`, `version`, `role_name`) VALUES (1, '2025-05-06 17:38:57.9399', '2025-05-06 17:38:57.9399', 0, 0, '超级管理员');
INSERT INTO `qnvip_qwen`.`qw_perm_role` (`id`, `create_time`, `update_time`, `is_deleted`, `version`, `role_name`) VALUES (2, '2025-05-06 17:39:09.4132', '2025-05-06 17:39:09.4132', 0, 0, '开发');
INSERT INTO `qnvip_qwen`.`qw_perm_role` (`id`, `create_time`, `update_time`, `is_deleted`, `version`, `role_name`) VALUES (3, '2025-05-06 17:39:14.6977', '2025-05-06 17:39:14.6977', 0, 0, '产品');
INSERT INTO `qnvip_qwen`.`qw_perm_role` (`id`, `create_time`, `update_time`, `is_deleted`, `version`, `role_name`) VALUES (4, '2025-05-06 17:39:19.6326', '2025-05-07 14:16:15.7190', 0, 0, '客服');


SET FOREIGN_KEY_CHECKS=1;