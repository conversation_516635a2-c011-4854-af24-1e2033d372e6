ALTER TABLE `qnvip_qwen`.`qw_chat_history`
ADD COLUMN `workflow_run_id` int NULL DEFAULT NULL COMMENT '工作流运行id' AFTER `conversation_id`;

ALTER TABLE qw_chat_history
DROP COLUMN question_id;


CREATE TABLE `qw_chat_history_comment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论Id',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除',
  `version` int NOT NULL DEFAULT '0' COMMENT '版本',
  `user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '用户Id',
  `conversation_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会话Id',
  `message_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息id',
  `like_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否点赞，1:是，0:否 -1:踩',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评论内容',
  `evaluate_tag` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '评价标签',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_conversation_id` (`conversation_id`(20))
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='聊天记录评论表';

ALTER TABLE `qnvip_qwen`.`qw_file_recall_log`
ADD COLUMN `recall_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '召回类型hasPermit,noPermit' AFTER `question_id`;

update  `qw_file_recall_log` set recall_type ='hasPermit' where  recall_type='';

ALTER TABLE `qw_file_recall_log`
ADD COLUMN `recall_from` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '召回源 navigation_milvus  navigation_es_first navi'
AFTER `recall_source`;

ALTER TABLE `qnvip_qwen`.`qw_file_recall_log`
MODIFY COLUMN `recall_stage` int NULL DEFAULT 0 COMMENT '召回阶段10导航召回 20内容召回 30周围分块召回 40重排序召回  50 结果封装' AFTER `recall_from`;
