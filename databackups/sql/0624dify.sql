-- 密码 <EMAIL>
psql -h localhost -p 5432 -U postgres -d  dify
\c dify

-- 备份表
CREATE TABLE "public"."conversations_back" AS
SELECT * FROM "public"."conversations";
-- DROP TABLE IF EXISTS public.conversations;
-- 还原表
CREATE TABLE "public"."conversations" AS
SELECT * FROM "public"."conversations_back";



-- 备份表
CREATE TABLE "public"."end_users_back" AS
SELECT * FROM "public"."end_users";
-- DROP TABLE IF EXISTS public.end_users;
-- 还原表
CREATE TABLE "public"."end_users" AS
SELECT * FROM "public"."end_users_back";

-- 转移前
SELECT count(*) FROM "public"."end_users" WHERE "app_id" =   'f25f7878-0293-4c53-9eed-dfe602c609bb';
SELECT count(*) FROM "public"."conversations" WHERE "app_id" =   'f25f7878-0293-4c53-9eed-dfe602c609bb';

UPDATE "public"."end_users" SET "app_id" = '12b79e24-d1d5-4fc3-8a4f-04669c6daedd' WHERE "app_id" = 'f25f7878-0293-4c53-9eed-dfe602c609bb';  -- 会话用户转移
UPDATE "public"."conversations" SET "app_id" = '12b79e24-d1d5-4fc3-8a4f-04669c6daedd' WHERE "app_id" = 'f25f7878-0293-4c53-9eed-dfe602c609bb'; -- 会话转移链接


SELECT count(*) FROM "public"."end_users" WHERE "app_id" =   '12b79e24-d1d5-4fc3-8a4f-04669c6daedd';
SELECT count(*) FROM "public"."conversations" WHERE "app_id" =   '12b79e24-d1d5-4fc3-8a4f-04669c6daedd';



-- 还原sql
UPDATE "public"."end_users" SET "app_id" ='f25f7878-0293-4c53-9eed-dfe602c609bb'  WHERE "app_id" = '12b79e24-d1d5-4fc3-8a4f-04669c6daedd';  -- 会话用户转移
UPDATE "public"."conversations" SET "app_id" =  'f25f7878-0293-4c53-9eed-dfe602c609bb' WHERE "app_id" = '12b79e24-d1d5-4fc3-8a4f-04669c6daedd' ; -- 会话转移链接
