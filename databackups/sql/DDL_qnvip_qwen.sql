/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_account   */
/******************************************/
CREATE TABLE `qw_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账号Id',
  `user_id` bigint unsigned DEFAULT '0' COMMENT '用户Id',
  `type` int DEFAULT '0' COMMENT '账号类型 1:qnvipoa, 2:账号密码',
  `account` varchar(255) DEFAULT '' COMMENT '账号信息',
  `status` int DEFAULT '0' COMMENT '账号状态 0正常 -1禁用',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='账号表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_api_config   */
/******************************************/
CREATE TABLE `qw_api_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置Id',
  `name` varchar(255) DEFAULT '' COMMENT '配置名称',
  `request_agent` tinyint unsigned DEFAULT '0' COMMENT '请求代理类型 0:直连 1:dify',
  `model_provider` tinyint unsigned DEFAULT '0' COMMENT '模型提供商 1:ollama 2:vllm 3:volcengine 4:localai',
  `model_name` varchar(255) DEFAULT '' COMMENT '模型名称',
  `api_key` varchar(255) DEFAULT '' COMMENT 'API密钥',
  `url` varchar(255) DEFAULT '' COMMENT 'API地址',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='API配置表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_chat_history   */
/******************************************/
CREATE TABLE `qw_chat_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话记录Id',
  `question_id` bigint DEFAULT '0' COMMENT '问题Id',
  `user_id` bigint DEFAULT '0' COMMENT '用户Id',
  `is_from_user` tinyint unsigned DEFAULT '0' COMMENT '是否来自某一方（1:是，0:否）',
  `agent_id` bigint DEFAULT '0' COMMENT '机器人Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='对话记录表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_chat_history_data   */
/******************************************/
CREATE TABLE `qw_chat_history_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话记录Id',
  `chat_history_id` bigint DEFAULT '0' COMMENT '问题Id',
  `data` text COMMENT '内容',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chat_history_id` (`chat_history_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='对话记录表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_dept   */
/******************************************/
CREATE TABLE `qw_dept` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门Id',
  `name` varchar(255) DEFAULT '' COMMENT '部门名称',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='部门表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_chunk   */
/******************************************/
CREATE TABLE `qw_file_chunk` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '切块Id',
  `uid` varchar(32) NOT NULL DEFAULT '' COMMENT '唯一标识',
  `file_origin_id` bigint DEFAULT '0' COMMENT '原始文件Id',
  `file_translate_id` bigint DEFAULT '0' COMMENT '翻译文件Id',
  `sort` int DEFAULT '0' COMMENT '排序',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_fid_sort` (`file_origin_id`,`sort`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=10964 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件切块表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_chunk_data   */
/******************************************/
CREATE TABLE `qw_file_chunk_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '切块ID',
  `chunk_uid` varchar(32) NOT NULL DEFAULT '' COMMENT '文件切块唯一标识',
  `file_origin_id` bigint NOT NULL DEFAULT '0' COMMENT '文件ID',
  `data` text COMMENT '文件切块内容',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chunk_id` (`chunk_uid`),
  KEY `idx_file_origin_id` (`file_origin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10964 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件切块数据表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_origin   */
/******************************************/
CREATE TABLE `qw_file_origin` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件Id',
  `checksum` varchar(256) DEFAULT '' COMMENT '文件校验码',
  `target_origin_url` varchar(1024) DEFAULT '' COMMENT '语雀原始文件URL',
  `type` tinyint unsigned DEFAULT '0' COMMENT '文件类型 1:本地文件 2:语雀文件',
  `tocs` varchar(255) DEFAULT NULL COMMENT '文件的目录',
  `file_name` varchar(256) DEFAULT '' COMMENT '文件名称',
  `file_extension` varchar(32) DEFAULT '' COMMENT '文件扩展名',
  `file_size` bigint unsigned DEFAULT '0' COMMENT '文件大小（字节）',
  `team_id` bigint DEFAULT '0' COMMENT '团队Id',
  `book_id` bigint DEFAULT '0' COMMENT '知识库Id',
  `doc_table_uid` varchar(255) NOT NULL DEFAULT '' COMMENT '文档的目录id 最后一级',
  `doc_uid` varchar(255) NOT NULL DEFAULT '' COMMENT '文档Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_bookId_docuid` (`book_id`,`doc_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=5534 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='原始文件表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_qa   */
/******************************************/
CREATE TABLE `qw_file_qa` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档QA Id',
  `uid` varchar(32) NOT NULL DEFAULT '' COMMENT '唯一标识',
  `file_origin_id` bigint DEFAULT '0' COMMENT '原文档Id',
  `file_translate_id` bigint DEFAULT '0' COMMENT '翻译文档Id',
  `file_chunk_id` bigint DEFAULT '0' COMMENT '文档分块Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=1909060138335813643 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文档QA表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_qa_data   */
/******************************************/
CREATE TABLE `qw_file_qa_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '问答数据Id',
  `file_origin_id` bigint NOT NULL DEFAULT '0' COMMENT '原始文件ID',
  `qa_uid` varchar(32) NOT NULL DEFAULT '' COMMENT '问答标识',
  `question` varchar(2550) NOT NULL DEFAULT '' COMMENT '问题内容',
  `answer` text COMMENT '答案内容',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_qa_uid` (`qa_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=1909060138356785163 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件问答数据表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_recall_log   */
/******************************************/
CREATE TABLE `qw_file_recall_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志Id',
  `user_id` bigint DEFAULT '0' COMMENT '用户Id',
  `question_id` bigint DEFAULT '0' COMMENT '问题Id',
  `file_origin_id` bigint DEFAULT '0' COMMENT '原始文件Id',
  `file_translate_id` bigint DEFAULT '0' COMMENT '翻译文件Id',
  `file_chunk_id` bigint DEFAULT '0' COMMENT '分块文件Id',
  `file_qa_id` bigint DEFAULT '0' COMMENT '问答文件Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件召回日志表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_summary   */
/******************************************/
CREATE TABLE `qw_file_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `origin_id` bigint DEFAULT '0' COMMENT '文件Id',
  `data` text COMMENT '标签Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_origin_id` (`origin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3329 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件概括表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_tag   */
/******************************************/
CREATE TABLE `qw_file_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `origin_id` bigint DEFAULT '0' COMMENT '文件Id',
  `name` varchar(255) DEFAULT '0' COMMENT '标签Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_origin_id` (`origin_id`),
  KEY `idx_tag_id` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=16390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件标签表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_task_progress   */
/******************************************/
CREATE TABLE `qw_file_task_progress` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务进度Id',
  `before_progress_id` bigint DEFAULT '0' COMMENT '上一任务进度id',
  `file_origin_id` bigint DEFAULT '0' COMMENT '原始文件Id',
  `now_step` int DEFAULT '0' COMMENT '任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70文章摘要 90完结',
  `status` int DEFAULT '0' COMMENT '状态 0待开始 1进行中 2已结束 3异常',
  `process_start_time` datetime(4) DEFAULT NULL COMMENT '处理开始时间',
  `process_finish_time` datetime(4) DEFAULT NULL COMMENT '处理结束时间',
  `max_retry_times` int DEFAULT '0' COMMENT '最大重试次数',
  `retry_times` int DEFAULT '0' COMMENT '重试次数',
  `err_time` datetime(4) DEFAULT NULL COMMENT '错误发生时间',
  `err_msg` text COMMENT '错误信息',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `book_id` bigint NOT NULL COMMENT '库id',
  PRIMARY KEY (`id`),
  KEY `idx_file_origin_id` (`file_origin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7414 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件任务进度表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_task_progress_datalog   */
/******************************************/
CREATE TABLE `qw_file_task_progress_datalog` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务进度Id',
  `file_origin_id` bigint DEFAULT '0' COMMENT '原始文件Id',
  `task_progress_id` bigint DEFAULT '0' COMMENT '任务步骤id',
  `now_step` int DEFAULT '0' COMMENT '任务步骤 10转格式 20数据清洗 30脱敏 40打tag 50切块 60Qa结构化 70文章摘要 90完结',
  `data` longtext COMMENT '数据',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_file_origin_id` (`file_origin_id`,`now_step`),
  KEY `idx_task_progress_id` (`task_progress_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6600 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件任务进度的产出数据日志表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_translate   */
/******************************************/
CREATE TABLE `qw_file_translate` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件转换Id',
  `file_origin_id` bigint DEFAULT '0' COMMENT '文件原始Id',
  `translate_url` varchar(255) DEFAULT '' COMMENT '转换后的URL地址',
  `file_name` varchar(128) DEFAULT '' COMMENT '文件名称',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件转换表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_file_translate_data   */
/******************************************/
CREATE TABLE `qw_file_translate_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件转换数据Id',
  `translate_id` bigint DEFAULT '0' COMMENT '翻译任务Id',
  `data` text COMMENT '转换后的文件内容',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chunk_id` (`translate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文件转换数据表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_prompt   */
/******************************************/
CREATE TABLE `qw_prompt` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '提示词Id',
  `key` varchar(255) DEFAULT '' COMMENT '提示词键',
  `content` text COMMENT '提示词内容',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='提示词表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_recall_intent_summary   */
/******************************************/
CREATE TABLE `qw_recall_intent_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `file_origin_id` bigint unsigned DEFAULT '0' COMMENT '源文档Id',
  `recall_type` int DEFAULT '0' COMMENT '召回类型 1: SQL, 10: 其他',
  `summary` varchar(255) DEFAULT '' COMMENT '内容总结',
  `recall_value` varchar(255) DEFAULT '' COMMENT '召回值 (recall_type=1时为表名字，recall_type=10时为文档id)',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_file_origin_id` (`file_origin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='召回意图概括表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_system_dict   */
/******************************************/
CREATE TABLE `qw_system_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否已删除',
  `update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '修改时间',
  `create_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `version` int NOT NULL DEFAULT '0' COMMENT '版本',
  `dict_name` varchar(32) NOT NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(50) NOT NULL DEFAULT '' COMMENT '字典类型，key值',
  `p_dict_type` varchar(50) NOT NULL DEFAULT '' COMMENT '父级字典key值',
  `dict_label` varchar(64) NOT NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(1024) NOT NULL DEFAULT '' COMMENT '字典键值',
  `dict_sort` int NOT NULL DEFAULT '0' COMMENT '字典排序',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态',
  `comment` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统字典表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_team   */
/******************************************/
CREATE TABLE `qw_team` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '团队Id',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '团队名称',
  `mark` varchar(32) NOT NULL DEFAULT '' COMMENT '团队值',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_mark` (`mark`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='团队表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_team_book   */
/******************************************/
CREATE TABLE `qw_team_book` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '知识库Id',
  `team_id` bigint DEFAULT '0' COMMENT '团队Id',
  `team_mark` varchar(255) NOT NULL DEFAULT '' COMMENT '团队唯一标识',
  `mark` varchar(255) NOT NULL DEFAULT '' COMMENT '三方唯一标识',
  `name` varchar(128) DEFAULT '' COMMENT '知识库名称',
  `description` varchar(255) NOT NULL DEFAULT '',
  `version` int DEFAULT '0' COMMENT '版本',
  `content_update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '内容更新时间',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_teamId_mark` (`team_id`,`mark`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='知识库表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_team_book_doc   */
/******************************************/
CREATE TABLE `qw_team_book_doc` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '文档Id',
  `team_id` bigint unsigned DEFAULT '0' COMMENT '所属团队Id',
  `parent_uid` varchar(255) NOT NULL DEFAULT '' COMMENT '父id',
  `book_id` bigint unsigned DEFAULT '0' COMMENT '知识库Id',
  `type` int NOT NULL DEFAULT '0' COMMENT '类型 1文章 2目录',
  `mark` varchar(128) NOT NULL DEFAULT '' COMMENT '三方唯一标识',
  `uid` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方uid',
  `name` varchar(255) DEFAULT '' COMMENT '文章或者目录名字',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_bookId_mark` (`book_id`,`mark`),
  KEY `idx_bookId_uid` (`book_id`,`uid`),
  KEY `idx_parent_uid` (`parent_uid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=6328 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文档表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_team_book_doc_data   */
/******************************************/
CREATE TABLE `qw_team_book_doc_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '文档Id',
  `doc_uid` varchar(64) NOT NULL DEFAULT '' COMMENT '所属团队Id',
  `mark` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方唯一标识',
  `type` varchar(100) NOT NULL DEFAULT '' COMMENT '文档类型',
  `data` longtext COMMENT '文章',
  `content_update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '内容更新时间',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `idx_doc_uid` (`doc_uid`),
  KEY `idx_mark` (`mark`)
) ENGINE=InnoDB AUTO_INCREMENT=5529 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='文档表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_user   */
/******************************************/
CREATE TABLE `qw_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户Id',
  `nickname` varchar(128) DEFAULT '' COMMENT '用户昵称',
  `status` int DEFAULT '0' COMMENT '用户状态 0正常 -1禁用',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户信息表'
;

/******************************************/
/*   DatabaseName = qnvip_qwen   */
/*   TableName = qw_user_dept_rela   */
/******************************************/
CREATE TABLE `qw_user_dept_rela` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联Id',
  `user_id` bigint DEFAULT '0' COMMENT '用户Id',
  `dept_id` bigint DEFAULT '0' COMMENT '部门Id',
  `version` int DEFAULT '0' COMMENT '版本',
  `create_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户部门关联表'
;
