
DROP TABLE IF EXISTS `qw_team`;
CREATE TABLE `qw_team`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '团队Id',
  `mark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '团队值',
  `version` int NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
  `update_time` datetime(4) NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
  `type` int NOT NULL COMMENT '团队类型 1语雀，2飞书',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '团队名称',
  `app_id_secret_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '飞书团队的配置文件',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mark`(`mark` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '团队表' ROW_FORMAT = DYNAMIC;


INSERT INTO `qw_team` VALUES (1, 'pokyqd', 0, '2025-03-25 10:38:08.5252', '2025-05-15 11:07:33.5687', 0, 1, '青年优品技术部', '');
INSERT INTO `qw_team` VALUES (2, 'tbli8d', 0, '2025-03-25 10:49:58.6579', '2025-05-15 11:07:33.6027', 0, 1, '青年优品产品部', '');
INSERT INTO `qw_team` VALUES (3, 'uu5e85', 0, '2025-04-16 17:00:03.6193', '2025-05-15 11:07:33.6371', 0, 1, '大数据', '');
INSERT INTO `qw_team` VALUES (4, 'mddqxg', 0, '2025-04-16 17:00:19.6393', '2025-05-15 11:07:33.6767', 0, 1, '优品金融', '');
INSERT INTO `qw_team` VALUES (5, 'mqtzyc', 0, '2025-04-21 11:05:57.9474', '2025-05-15 11:07:33.7243', 1, 1, '风控部', '');
INSERT INTO `qw_team` VALUES (6, 'w04q68pupay', 0, '2025-05-14 16:49:43.0994', '2025-05-15 15:07:54.8826', 0, 2, '客服中心', '{\"appId\":\"cli_a8a85dff32a1900e\",\"appSecret\":\"Pl1dXtqxumXRcs0yoQVf9b5kf3vgUbFM\"}');
INSERT INTO `qw_team` VALUES (7, 'fc7cdcim81', 0, '2025-05-14 16:49:43.0994', '2025-05-20 13:49:36.7406', 0, 2, '运营部', '{\"appId\":\"cli_a89002092f5b900d\",\"appSecret\":\"A8DpPJZp2e8vzA8EmbPHifkUXtVRHlFG\"}');




ALTER TABLE `qnvip_qwen`.`qw_team_book`
ADD COLUMN `type` int NULL DEFAULT 0 COMMENT '团队类型 1语雀，2飞书' AFTER `is_deleted`;
update qw_team_book set type =1

