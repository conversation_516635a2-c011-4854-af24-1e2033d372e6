CREATE TABLE `qw_file_graph_data`
(
    `id`            bigint unsigned   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `version`       int               NOT NULL DEFAULT '0' COMMENT '版本号',
    `create_time`   datetime(4)       NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
    `update_time`   datetime(4)       NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
    `is_deleted`    int unsigned      NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `chunk_uid`     varchar(64)       NOT NULL DEFAULT '' COMMENT '文件块唯一标识',
    `file_origin_id` bigint unsigned  NOT NULL DEFAULT '0' COMMENT '原始文件ID',
    `book_id` bigint unsigned  NOT NULL DEFAULT '0' COMMENT '知识库id',
    `data`          text              NULL COMMENT '图数据内容',
    PRIMARY KEY (`id`) COMMENT '主键索引',
    KEY `idx_chunk_uid` (`chunk_uid`) COMMENT '文件块唯一标识索引',
    KEY `idx_file_origin_id` (`file_origin_id`) COMMENT '原始文件ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '文件图数据表';