
  select *from qw_file_task_progress    where file_origin_id in (
  SELECT DISTINCT a.id
FROM qw_file_origin a
INNER JOIN qw_file_origin b
  ON a.id = b.parent_id
WHERE
  a.is_deleted = 0
  AND b.is_deleted = 0
  )
  and create_time > '2025-06-01'

  -- 重新更新sub excel 删除没有的checksum的数据，先发版scheduler在更新sql跑数据
     update qw_file_task_progress set status =0    where file_origin_id in (
  SELECT DISTINCT a.id
FROM qw_file_origin a
INNER JOIN qw_file_origin b
  ON a.id = b.parent_id
WHERE
  a.is_deleted = 0
  AND b.is_deleted = 0
  )
  and create_time > '2025-06-01' and status =2