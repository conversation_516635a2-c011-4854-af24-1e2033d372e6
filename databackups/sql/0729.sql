CREATE TABLE `qw_file_graph_data`
(
    `id`            bigint unsigned   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `version`       int               NOT NULL DEFAULT '0' COMMENT '版本号',
    `create_time`   datetime(4)       NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
    `update_time`   datetime(4)       NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
    `is_deleted`    int unsigned      NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `chunk_uid`     varchar(64)       NOT NULL DEFAULT '' COMMENT '文件块唯一标识',
    `file_origin_id` bigint unsigned  NOT NULL DEFAULT '0' COMMENT '原始文件ID',
    `book_id` bigint unsigned  NOT NULL DEFAULT '0' COMMENT '知识库id',
    `data`          text              NULL COMMENT '图数据内容',
    PRIMARY KEY (`id`) COMMENT '主键索引',
    KEY `idx_chunk_uid` (`chunk_uid`) COMMENT '文件块唯一标识索引',
    KEY `idx_file_origin_id` (`file_origin_id`) COMMENT '原始文件ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '文件图数据表';




-- 添加bookId 用来过滤知识库权限
  ALTER TABLE `qnvip_qwen`.`qw_file_chunk`
  ADD COLUMN `book_id` bigint NULL AFTER `is_deleted`;


  UPDATE qw_file_chunk fc
  JOIN qw_file_origin fo ON fc.file_origin_id = fo.id
  SET fc.book_id = fo.book_id;




  CREATE TABLE `qw_user_cache` (
    `id` bigint unsigned NOT NULL COMMENT '主键ID',
    `create_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
    `update_time` datetime(4) NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '更新时间',
    `is_deleted` int unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `version` int NOT NULL DEFAULT '0' COMMENT '版本号',
    `model_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '模型ID',
    `use_book_flag` int unsigned NOT NULL DEFAULT '0' COMMENT '@name:是否使用知识库, @dicts:[1-是,0-否]',
    `use_search_flag` int unsigned NOT NULL DEFAULT '0' COMMENT '@name:是否使用联网搜索, @dicts:[1-是,0-否]',
    `use_deep_search_flag` int unsigned NOT NULL DEFAULT '0' COMMENT '@name:是否使用深度搜索, @dicts:[1-是,0-否]',
    `book_ids_str` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '知识库ID列表字符串',
    PRIMARY KEY (`id`),
    KEY `idx_model_id` (`model_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户缓存表';


  ALTER TABLE `qnvip_qwen`.`qw_team_book_doc`
  ADD COLUMN `from_type` int NULL DEFAULT 0 COMMENT '来源类型 0飞书语雀 1接口同步 2本地上传' AFTER `is_deleted`;