UPDATE qw_file_origin o
JOIN qw_team_book_doc_data d ON o.doc_uid = d.doc_uid
SET o.CHECKSUM = MD5(d.DATA);


ALTER TABLE `qnvip_qwen`.`qw_file_origin` ADD COLUMN `parent_id` bigint NULL DEFAULT 0 COMMENT '父id' AFTER `id`;

ALTER TABLE `qnvip_qwen`.`qw_team_book_doc` ADD COLUMN `parent_id` bigint NULL DEFAULT 0 COMMENT '父id' AFTER `id`;

ALTER TABLE `qnvip_qwen`.`qw_team_book_doc_data` ADD COLUMN `parent_id` bigint NULL DEFAULT 0 COMMENT '父id' AFTER `id`;



select * from qw_file_task_progress where id in (
select max(id) from qw_file_task_progress where now_step=10 and file_origin_id in (select id from qw_file_origin where file_extension='exceljson' )   GROUP BY file_origin_id )




UPDATE qw_file_task_progress AS ftp
JOIN (
    SELECT MAX(id) AS max_id
    FROM qw_file_task_progress
    WHERE now_step = 10
    AND file_origin_id IN (
        SELECT id FROM qw_file_origin WHERE file_extension = 'exceljson'
    )
    GROUP BY file_origin_id
) AS subquery ON ftp.id = subquery.max_id
SET ftp.`status` = 0;