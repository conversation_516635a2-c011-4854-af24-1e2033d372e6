app:
  description: 入职流程双路召回，10万级物流数据召回问答
  icon: ocean
  icon_background: '#FFEAD5'
  mode: workflow
  name: 综合问答流程-dify内测版
  use_icon_as_answer_icon: false
dependencies:
  - current_identifier: null
    type: package
    value:
      plugin_unique_identifier: langgenius/ollama:0.0.3@9ded90ac00e8510119a24be7396ba77191c9610d5e1e29f59d68fa1229822fc7
kind: app
version: 0.1.5
workflow:
  conversation_variables: [ ]
  environment_variables: [ ]
  features:
    file_upload:
      allowed_file_extensions:
        - .JPG
        - .JPEG
        - .PNG
        - .GIF
        - .WEBP
        - .SVG
      allowed_file_types:
        - image
      allowed_file_upload_methods:
        - local_file
        - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
          - local_file
          - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: [ ]
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
      - data:
          isInLoop: false
          sourceType: http-request
          targetType: llm
        id: 1741934327233-source-1741934020641-target
        selected: false
        source: '1741934327233'
        sourceHandle: source
        target: '1741934020641'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: llm
          targetType: end
        id: 1741934020641-source-1741935933743-target
        selected: false
        source: '1741934020641'
        sourceHandle: source
        target: '1741935933743'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          isInLoop: false
          sourceType: http-request
          targetType: llm
        id: 1743149746964-source-1743149767719-target
        source: '1743149746964'
        sourceHandle: source
        target: '1743149767719'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: llm
          targetType: if-else
        id: 1743149767719-source-17431497900000-target
        source: '1743149767719'
        sourceHandle: source
        target: '17431497900000'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: http-request
          targetType: llm
        id: 17431497919590-source-17431497949280-target
        source: '17431497919590'
        sourceHandle: source
        target: '17431497949280'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: if-else
          targetType: http-request
        id: 17431497900000-false-17431497919590-target
        source: '17431497900000'
        sourceHandle: 'false'
        target: '17431497919590'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          isInLoop: false
          sourceType: llm
          targetType: if-else
        id: 17431497949280-source-1743149830217-target
        source: '17431497949280'
        sourceHandle: source
        target: '1743149830217'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: if-else
          targetType: http-request
        id: 1743149830217-true-1741934327233-target
        source: '1743149830217'
        sourceHandle: 'true'
        target: '1741934327233'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: if-else
          targetType: http-request
        id: 17431497900000-true-1741934327233-target
        source: '17431497900000'
        sourceHandle: 'true'
        target: '1741934327233'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: if-else
          targetType: http-request
        id: 1743149830217-false-17431498587470-target
        source: '1743149830217'
        sourceHandle: 'false'
        target: '17431498587470'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: http-request
          targetType: llm
        id: 17431498587470-source-17431498604890-target
        source: '17431498587470'
        sourceHandle: source
        target: '17431498604890'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: llm
          targetType: http-request
        id: 17431498604890-source-1741934327233-target
        source: '17431498604890'
        sourceHandle: source
        target: '1741934327233'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: start
          targetType: http-request
        id: 1741918590155-source-1743149746964-target
        source: '1741918590155'
        sourceHandle: source
        target: '1743149746964'
        targetHandle: target
        type: custom
        zIndex: 0
    nodes:
      - data:
          desc: ''
          selected: false
          title: 开始
          type: start
          variables:
            - allowed_file_extensions: [ ]
              allowed_file_types:
                - image
              allowed_file_upload_methods:
                - local_file
                - remote_url
              label: 用户id 1-20可用
              max_length: 48
              options: [ ]
              required: true
              type: number
              variable: user_id
            - label: 用户输入
              max_length: 1000
              options: [ ]
              required: true
              type: paragraph
              variable: userInput
            - label: 团队标识 1：青年优品技术部   2青年优品产品
              max_length: 48
              options:
                - '1'
                - '2'
              required: true
              type: select
              variable: teamMark
        height: 141
        id: '1741918590155'
        position:
          x: 32.71040996878634
          y: -36.60340107893609
        positionAbsolute:
          x: 32.71040996878634
          y: -36.60340107893609
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: qwen2.5:32b
            provider: langgenius/ollama/ollama
          prompt_template:
            - id: c510f53b-4103-4f19-ac0b-79e88ef7dceb
              role: system
              text: '## 角色

            你是一个公司文档管理员


            ## 任务

            1. 根据查询到的文档完成用户的要求

            2.筛选出最恰当的回答


            ## 查询到的文档

            {{#1741933510538.body#}}



            ## 用户当前问题

            {{#1741918590155.userInput#}}


            '
          selected: false
          title: 文档管理员
          type: llm
          variables: [ ]
          vision:
            enabled: false
        height: 89
        id: '1741934020641'
        position:
          x: 598.9838323931177
          y: 292.00696183034233
        positionAbsolute:
          x: 598.9838323931177
          y: 292.00696183034233
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          authorization:
            config: null
            type: no-auth
          body:
            data:
              - id: key-value-1664
                key: ''
                type: text
                value: '{"collectionName":"{{#1741933114017.text#}}","question":"{{#1741918590155.userInput#}}"}'
            type: json
          desc: ''
          headers: ''
          method: post
          params: ''
          retry_config:
            max_retries: 3
            retry_enabled: false
            retry_interval: 100
          selected: false
          timeout:
            max_connect_timeout: 0
            max_read_timeout: 0
            max_write_timeout: 0
          title: 多路召回，窗口搜索，重排序
          type: http-request
          url: http://10.60.195.20:8888/es/search
          variables: [ ]
        height: 109
        id: '1741934327233'
        position:
          x: 582.2781100623106
          y: 157.60961032472215
        positionAbsolute:
          x: 582.2781100623106
          y: 157.60961032472215
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          desc: ''
          outputs:
            - value_selector:
                - '1741934020641'
                - text
              variable: text
          selected: false
          title: 结束 4
          type: end
        height: 89
        id: '1741935933743'
        position:
          x: 613.3030229623812
          y: 387.2205137743943
        positionAbsolute:
          x: 613.3030229623812
          y: 387.2205137743943
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          authorization:
            config: null
            type: no-auth
          body:
            data: [ ]
            type: none
          desc: ''
          headers: ''
          method: get
          params: ''
          retry_config:
            max_retries: 3
            retry_enabled: true
            retry_interval: 100
          selected: false
          timeout:
            max_connect_timeout: 0
            max_read_timeout: 0
            max_write_timeout: 0
          title: 文档目录tag摘要动态导航
          type: http-request
          url: ''
          variables: [ ]
        height: 78
        id: '1743149746964'
        position:
          x: -341.27777716044966
          y: 34.735593314322955
        positionAbsolute:
          x: -341.27777716044966
          y: 34.735593314322955
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: deepseek-r1:32b
            provider: langgenius/ollama/ollama
          prompt_template:
            - id: 66e251aa-33f9-4aff-8768-51924f15b75d
              role: system
              text: ''
          selected: false
          title: 文档导航初筛
          type: llm
          variables: [ ]
          vision:
            enabled: false
        height: 89
        id: '1743149767719'
        position:
          x: -356.790233610485
          y: 122.79193596902414
        positionAbsolute:
          x: -356.790233610485
          y: 122.79193596902414
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          cases:
            - case_id: 'true'
              conditions: [ ]
              id: 'true'
              logical_operator: and
          desc: ''
          selected: false
          title: 定位是否完成？
          type: if-else
        height: 101
        id: '17431497900000'
        position:
          x: -116.19916623674004
          y: 229.30563228585788
        positionAbsolute:
          x: -116.19916623674004
          y: 229.30563228585788
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          authorization:
            config: null
            type: no-auth
          body:
            data: [ ]
            type: none
          desc: ''
          headers: ''
          method: get
          params: ''
          retry_config:
            max_retries: 3
            retry_enabled: true
            retry_interval: 100
          selected: false
          timeout:
            max_connect_timeout: 0
            max_read_timeout: 0
            max_write_timeout: 0
          title: 文档目录tag摘要动态导航 (1)
          type: http-request
          url: ''
          variables: [ ]
        height: 78
        id: '17431497919590'
        position:
          x: -110.23283683288028
          y: 342.4535315203178
        positionAbsolute:
          x: -110.23283683288028
          y: 342.4535315203178
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: deepseek-r1:32b
            provider: langgenius/ollama/ollama
          prompt_template:
            - id: 66e251aa-33f9-4aff-8768-51924f15b75d
              role: system
              text: ''
          selected: false
          title: 文档导航初筛 (1)
          type: llm
          variables: [ ]
          vision:
            enabled: false
        height: 89
        id: '17431497949280'
        position:
          x: -101.87997566747657
          y: 427.79155372064065
        positionAbsolute:
          x: -101.87997566747657
          y: 427.79155372064065
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          cases:
            - case_id: 'true'
              conditions: [ ]
              id: 'true'
              logical_operator: and
          desc: ''
          selected: false
          title: 定位是否完成？
          type: if-else
        height: 101
        id: '1743149830217'
        position:
          x: 205.07952773321176
          y: 415.85889491292113
        positionAbsolute:
          x: 205.07952773321176
          y: 415.85889491292113
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          authorization:
            config: null
            type: no-auth
          body:
            data: [ ]
            type: none
          desc: ''
          headers: ''
          method: get
          params: ''
          retry_config:
            max_retries: 3
            retry_enabled: true
            retry_interval: 100
          selected: false
          timeout:
            max_connect_timeout: 0
            max_read_timeout: 0
            max_write_timeout: 0
          title: 文档目录tag摘要动态导航 (2)
          type: http-request
          url: ''
          variables: [ ]
        height: 78
        id: '17431498587470'
        position:
          x: 205.07952773321176
          y: 524.8271268591241
        positionAbsolute:
          x: 205.07952773321176
          y: 524.8271268591241
        selected: true
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: deepseek-r1:32b
            provider: langgenius/ollama/ollama
          prompt_template:
            - id: 66e251aa-33f9-4aff-8768-51924f15b75d
              role: system
              text: ''
          selected: false
          title: 文档导航初筛 (2)
          type: llm
          variables: [ ]
          vision:
            enabled: false
        height: 89
        id: '17431498604890'
        position:
          x: 213.4323888986155
          y: 609.1679675979777
        positionAbsolute:
          x: 213.4323888986155
          y: 609.1679675979777
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 243
    viewport:
      x: 402.8178491618688
      y: 102.12908947913058
      zoom: 0.8380361963865709
