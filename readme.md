# 项目结构

```
${rootArtifactId}
├─${rootArtifactId}-acl           // 防腐层（持久层、mongo、es）
├─${rootArtifactId}-biz           // 业务聚合层
├─${rootArtifactId}-client        // 提供对外接口
├─${rootArtifactId}-common        // 公共模块
├─${rootArtifactId}-app           // 应用层（controller、facade）
└─${rootArtifactId}-service       // 业务逻辑层（service）
```

# 项目初始化必须事项

1. 找运维申请应用端口(按需申请：http端口、dubbo端口、监控端口、xxljob端口)
2. 找运维创建数据库
3. 创建nacos配置

# 项目初始化配置

1. 创建nacos配置,nacos配置参考如下
    ```yml
    server:
      port: 8099           # 应用端口
      shutdown: graceful
      
    spring:
      datasource:
        url: jdbc:mysql://[ip:port]/[database]?serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=UTF-8
        username: username
        password: password
        hikari:
          maximum-pool-size: 50            # 最大连接数，按应用实际情况设置
          minimum-idle: 10                 # 最小连接数，按应用实际情况设置
          connection-test-query: SELECT 1
    ```
2. 修改bootstrap-qa.yml和bootstrap-prod.yml配置属性nacos.config-namespace
3. 修改bootstrap.yml配置属性management.server.port
4. 检查bootstrao.yml配置属性spring.application.name

# 项目构建

1. 应用部署打包：
   - 测试：`make package-qa-${rootArtifactId}`
   - 生产：`make package-prod-${rootArtifactId}`
2. client包上传maven仓库：
   - 测试：`make deploy-qa-${rootArtifactId}-client`
   - 生产：`make deploy-prod-${rootArtifactId}-client`

# 规范

|   规范    |                                       文档                                        |   
|:-------:|:-------------------------------------------------------------------------------:|
|  代码格式化  |        https://pokyqd.yuque.com/pokyqd/iqh08y/uty7an6fm7ifx0di?singleDoc        |                       
| git使用规范 |        https://pokyqd.yuque.com/pokyqd/iqh08y/bu2v4so1kh0wrwpy?singleDoc        |
|  开发规范   |              https://pokyqd.yuque.com/pokyqd/2024/agr24g91zd0k2f94              |

# 基础组件
|           组件           |                                                   说明文档                                                   |   
|:----------------------:|:--------------------------------------------------------------------------------------------------------:|
|          公共组件          |  https://git.qncentury.com/qnvip-architecture/qnvip-framework/-/blob/master/qnvip-common-core/readme.md  |  
|       dubbo集成组件        | https://git.qncentury.com/qnvip-architecture/qnvip-framework/-/blob/master/qnvip-dubbo-starter/readme.md |  
| orm框架集成组件（mybatisplus） |  https://git.qncentury.com/qnvip-architecture/qnvip-framework/-/blob/master/qnvip-orm-starter/readme.md  |  
|       redis集成组件        | https://git.qncentury.com/qnvip-architecture/qnvip-framework/-/blob/master/qnvip-redis-starter/readme.md |  
|        web集成组件         |  https://git.qncentury.com/qnvip-architecture/qnvip-framework/-/blob/master/qnvip-web-starter/readme.md  |  