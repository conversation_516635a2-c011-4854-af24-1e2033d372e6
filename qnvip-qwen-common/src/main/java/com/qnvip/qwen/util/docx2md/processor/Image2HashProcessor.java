package com.qnvip.qwen.util.docx2md.processor;

import java.util.stream.Collectors;
import java.util.stream.IntStream;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月14日 17:07:00
 */
@Slf4j
public class Image2HashProcessor {
    public static String calculateImageHash(byte[] imageData) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(imageData);

            // 使用Java 8流处理字节数组
            return IntStream.range(0, hash.length).mapToObj(i -> {
                String hex = Integer.toHexString(0xff & hash[i]);
                return hex.length() == 1 ? "0" + hex : hex;
            }).collect(Collectors.joining());
        } catch (Exception e) {
            log.error("计算图片哈希值出错: {}", e.getMessage());
            return "";
        }
    }
}
