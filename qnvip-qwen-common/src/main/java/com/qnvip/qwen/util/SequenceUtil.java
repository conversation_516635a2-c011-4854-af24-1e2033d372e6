package com.qnvip.qwen.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import lombok.experimental.UtilityClass;

/**
 * 序号生成工具类
 *
 * <AUTHOR>
 */
@UtilityClass
public class SequenceUtil {

    private static final String COMMON_DATETIME = "yyyyMMddHHmmss";
    private static final String COMMON_DATETIME2 = "yyyyMMddHHmmssSSS";

    /**
     * 产生唯一 24位的序列号
     */
    public static synchronized String getSerialNumber() {
        int hashCode = UUID.randomUUID().toString().hashCode();
        if (hashCode < 0) {
            hashCode = -hashCode;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(COMMON_DATETIME);
        LocalDateTime now = LocalDateTime.now();
        return formatter.format(now) + String.format("%010d", hashCode);
    }

    /**
     * 产生唯一 32位的序列号
     */
    public static synchronized String getSerialNumberPlus() {
        int hashCode = UUID.randomUUID().toString().hashCode();
        if (hashCode < 0) {
            hashCode = -hashCode;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(COMMON_DATETIME2);
        LocalDateTime now = LocalDateTime.now();
        return formatter.format(now) + getNumberRandom(5) + String.format("%010d", hashCode);
    }

    public static String getUuid() {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        return uuid;
    }

    public static String getUuidByLength(int length) {
        String uuid = getUuid();

        return uuid.substring(0, length).toLowerCase();
    }

    /**
     * 20 位订单号
     *
     * @return
     */
    public static String getOrderNo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");
        String time = formatter.format(LocalDateTime.now());
        return time + getNumberRandom(5);
    }

    /**
     * @description: 生成带有后缀的订单号
     * @params: orderNo: 订单号
     * @params: suffixLength: 后缀长租
     * @return: 返回订单号 + _ + 随机数
     **/
    public static String getOrderNo(String orderNo, Integer suffixLength) {

        if (StringUtils.isBlank(orderNo)) {
            return StringUtils.EMPTY;
        }

        suffixLength = suffixLength != null ? suffixLength : 3;
        return orderNo + "_" + getNumberRandom(suffixLength);

    }

    public static String getNumberRandom(int length) {
        StringBuffer buffer = new StringBuffer("0123456789");
        StringBuffer sb = new StringBuffer();
        Random r = new Random();
        int range = buffer.length();
        for (int i = 0; i < length; i++) {
            sb.append(buffer.charAt(r.nextInt(range)));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(getSerialNumberPlus());
    }
}
