package com.qnvip.qwen.util.rag;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ChunkUtil {

    public static String toScoreChunk(String tocs, String content) {
        return toScoreChunk(Arrays.stream(tocs.split("/")).collect(Collectors.toList()), content);
    }

    public static String toScoreChunk(List<String> tocs, String content) {
        tocs = tocs.stream().distinct().collect(Collectors.toList());
        return "\n" + "目录：" + String.join("/", tocs) + "\n" + content + String.join(" ");
    }

    public static String removeFirstToc(String tocs) {
        return Arrays.stream(tocs.split("/")).skip(1).collect(Collectors.joining(" "));
    }

}
