package com.qnvip.qwen.util.rag;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringEscapeUtils;

public class GraphUtil {
    // 预编译正则表达式，提高性能
    private static final Pattern CHINESE_SPACES = Pattern.compile("(?<=[\\u4e00-\\u9fa5])\\s+(?=[\\u4e00-\\u9fa5])");
    private static final Pattern CHINESE_ENGLISH_LEFT =
        Pattern.compile("(?<=[\\u4e00-\\u9fa5])\\s+(?=[a-zA-Z0-9\\(\\)\\[\\]@#$%!&\\*\\-=+_])");
    private static final Pattern CHINESE_ENGLISH_RIGHT =
        Pattern.compile("(?<=[a-zA-Z0-9\\(\\)\\[\\]@#$%!&\\*\\-=+_])\\s+(?=[\\u4e00-\\u9fa5])");
    private static final Pattern ENGLISH_QUOTES_BEFORE_CHINESE = Pattern.compile("['\"]+(?=[\\u4e00-\\u9fa5])");
    private static final Pattern ENGLISH_QUOTES_AFTER_CHINESE = Pattern.compile("(?<=[\\u4e00-\\u9fa5])['\"]+");
    // 预编译正则表达式，用于匹配控制字符（ASCII 0-31 和 127-159）
    private static final Pattern CONTROL_CHARS_PATTERN = Pattern.compile("[\\x00-\\x1f\\x7f-\\x9f]");

    /**
     * 清理输入字符串，移除HTML转义字符、控制字符和其他不需要的字符
     *
     * @param input 输入对象（可以是任意类型）
     * @return 如果是字符串则返回清理后的结果，否则返回原对象
     */
    public static String cleanStr(String input) {
        // 如果输入不是字符串类型，直接返回原对象
        if (!(input instanceof String)) {
            return input;
        }


        // 移除控制字符
        return CONTROL_CHARS_PATTERN.matcher(StringEscapeUtils.unescapeHtml4(input.trim())).replaceAll("");
    }

    /**
     * 规范化实体/关系名称和描述的文本
     * 
     * @param name 需要规范化的文本
     * @param isEntity 是否为实体（而非关系）
     * @return 规范化后的文本
     */
    public static String normalizeExtractedInfo(String name, boolean isEntity) {
        if (name == null) {
            return null;
        }

        // 将中文括号替换为英文括号
        name = name.replace("（", "(").replace("）", ")");

        // 将中文破折号替换为英文破折号
        name = name.replace("—", "-").replace("－", "-");

        // 移除中文字符之间的空格
        name = CHINESE_SPACES.matcher(name).replaceAll("");

        // 移除中文与英文/数字/符号之间的空格
        name = CHINESE_ENGLISH_LEFT.matcher(name).replaceAll("");
        name = CHINESE_ENGLISH_RIGHT.matcher(name).replaceAll("");

        // 移除开头和结尾的英文引号
        if (name.length() >= 2 && name.startsWith("\"") && name.endsWith("\"")) {
            name = name.substring(1, name.length() - 1);
        }
        if (name.length() >= 2 && name.startsWith("'") && name.endsWith("'")) {
            name = name.substring(1, name.length() - 1);
        }

        if (isEntity) {
            // 移除中文引号
            name = name.replace("“", "").replace("”", "").replace("‘", "").replace("’", "");
            // 移除中文内部及周围的英文引号
            name = ENGLISH_QUOTES_BEFORE_CHINESE.matcher(name).replaceAll("");
            name = ENGLISH_QUOTES_AFTER_CHINESE.matcher(name).replaceAll("");
        }

        return name;
    }

    // 重载方法，默认isEntity为false
    public static String normalizeExtractedInfo(String name) {
        return normalizeExtractedInfo(name, false);
    }

    public static String generateEdgeKey(String src, String tgt) {
        List<String> sortedKey = Arrays.asList(src, tgt);
        Collections.sort(sortedKey);
        return String.join("_", sortedKey);
    }

    public static String removeBracketsContent(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        // 正则表达式匹配：
        // [\\(（] 匹配左括号（英文或中文）
        // .*? 匹配任意字符（非贪婪模式）
        // [\\)）] 匹配右括号（英文或中文）
        String regex = "[\\(（].*?[\\)）]";
        // 替换匹配到的内容为空字符串
        return str.replaceAll(regex, "");
    }

    public static String removeNameSpace(String name) {
        return name.replace(" ", "");
    }
}
