package com.qnvip.qwen.util.docx2md.processor;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.poi.xwpf.usermodel.XWPFDocument;

import com.qnvip.qwen.util.FileSystemUtil;
import com.qnvip.qwen.util.docx2md.vo.ImageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * Word文档图片处理工具类
 * 
 * <p>
 * 此工具类用于处理Word文档中的图片，将其提取并保存到指定目录。
 * </p>
 */
@Slf4j
public class ImageProcessor {

    /**
     * 从Word文档中提取图片
     * 
     * <p>
     * 将文档中包含的所有图片提取出来，保存到指定目录，并返回图片信息列表。
     * </p>
     * 
     * @param doc Word文档对象
     * @return 图片信息列表
     * @throws IOException 如果图片提取或保存过程中发生错误
     */
    public static List<ImageInfo> extractImages(XWPFDocument doc)
        throws IOException {
        // 用于存储已保存图片的哈希值
        Set<String> savedImageHashes = new HashSet<>();

        // 使用Stream API处理图片
        return doc.getAllPictures().stream().map(picture -> {

            try {
                byte[] data = picture.getData();
                String imageHash = calculateHashAndSave(savedImageHashes, data);
                String ext =
                    Optional.ofNullable(picture.suggestFileExtension()).filter(e -> !e.isEmpty()).orElse("png");

                String fileName = "image_" + imageHash + "." + ext;
                String url = FileSystemUtil.upload(data, fileName);
                return new ImageInfo(picture.getParent().getRelationId(picture), url);
            } catch (Exception e) {
                log.error("处理图片时出错: {}", e.getMessage(), e);
                throw new RuntimeException("处理图片时出错，" + e.getMessage());
            }
        }).filter(img -> img != null) // 过滤掉处理失败的图片
            .collect(Collectors.toList());
    }

    public static String calculateHashAndSave(Set<String> savedImageHashes, byte[] data) {
        // 计算图片内容的哈希值
        String imageHash = Image2HashProcessor.calculateImageHash(data);

        // 如果图片已经保存过，跳过
        if (savedImageHashes.contains(imageHash)) {
            return null;
        }

        // 添加到已保存集合
        savedImageHashes.add(imageHash);
        return imageHash;
    }
}