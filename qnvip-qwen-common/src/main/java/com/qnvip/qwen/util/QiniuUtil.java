// package com.qnvip.qwen.util;
//
// import java.io.ByteArrayOutputStream;
// import java.io.File;
// import java.io.IOException;
// import java.io.InputStream;
// import java.io.UnsupportedEncodingException;
// import java.net.URLEncoder;
// import java.time.LocalDate;
//
// import org.apache.commons.lang3.StringUtils;
// import org.springframework.boot.context.properties.ConfigurationProperties;
// import org.springframework.stereotype.Component;
// import org.springframework.web.multipart.MultipartFile;
//
// import com.qiniu.common.QiniuException;
// import com.qiniu.storage.BucketManager;
// import com.qiniu.storage.Configuration;
// import com.qiniu.storage.Region;
// import com.qiniu.storage.UploadManager;
// import com.qiniu.util.Auth;
// import com.qnvip.common.exception.FrameworkException;
//
// import lombok.Data;
// import lombok.extern.slf4j.Slf4j;
//
// @Component
// @Slf4j
// @ConfigurationProperties("qiniu")
// @Data
// public class QiniuUtil {
//
// private String accessKey;
// private String secretKey;
// private String publicBucketName;
// private String publicDomain;
// private String privateBucketName;
// private String privateDomain;
//
// public QiniuUtil() {}
//
// public String upload(String filePath, Boolean isPrivate) {
// return upload(filePath, null, isPrivate);
// }
//
// public String upload(String filePath, String key, Boolean isPrivate) {
// String bucketName;
// if (isPrivate) {
// bucketName = privateBucketName;
// } else {
// bucketName = publicBucketName;
// }
//
// return upload(filePath, key, bucketName);
// }
//
// public String upload(String filePath, String key, String bucketName) {
// File file = new File(filePath);
// if (!file.exists()) {
// return null;
// } else {
// try {
// if (StringUtils.isEmpty(key)) {
// LocalDate localDate = LocalDate.now();
// key = localDate.getYear() + "/" + localDate.getMonthValue() + "/" + System.currentTimeMillis() + "/"
// + file.getName();
// }
// UploadManager uploadManager = new UploadManager(new Configuration(Region.region0()));
// String uptoken = getUptoken(accessKey, secretKey, bucketName);
// uploadManager.put(filePath, key, uptoken);
// return key;
// } catch (QiniuException var7) {
// QiniuException e = var7;
//
// try {
// log.error(e.response.bodyString());
// return null;
// } catch (QiniuException var6) {
// log.error("文件上传失败", e);
// throw FrameworkException.instance("文件上传失败");
// }
// }
// }
// }
//
// public String upload(byte[] data, String key, boolean isPublic) {
// return publicDomain + upload(data, key, isPublic ? publicBucketName : privateBucketName);
// }
//
// private String upload(byte[] data, String key, String bucketName) {
// try {
// if (StringUtils.isEmpty(key)) {
// throw FrameworkException.instance("文件上传key不能为空");
// }
// Configuration cfg = new Configuration(Region.region0());
// Auth auth = Auth.create(accessKey, secretKey);
// BucketManager bucketManager = new BucketManager(auth, cfg);
// try {
// bucketManager.stat(bucketName, key);
// return key;
// } catch (QiniuException e) {
// }
//
// UploadManager uploadManager = new UploadManager(cfg);
// String uptoken = getUptoken(accessKey, secretKey, bucketName);
// uploadManager.put(data, key, uptoken);
// return key;
//
// } catch (QiniuException var7) {
// QiniuException e = var7;
//
// try {
// log.error(e.response.bodyString());
// return null;
// } catch (QiniuException var6) {
// log.error("文件上传失败", e);
// throw FrameworkException.instance("文件上传失败");
// }
// }
// }
//
// public String upload(MultipartFile file, Boolean isPrivate) {
// LocalDate localDate = LocalDate.now();
// String key = localDate.getYear() + "/" + localDate.getMonthValue() + "/" + System.currentTimeMillis() + "/"
// + file.getOriginalFilename();
// return upload(file, key, isPrivate);
// }
//
// public String upload(MultipartFile file, String key, Boolean isPrivate) {
// String bucketName;
// if (isPrivate) {
// bucketName = privateBucketName;
// } else {
// bucketName = publicBucketName;
// }
//
// return upload(file, key, bucketName);
// }
//
// public String upload(MultipartFile file, String key, String bucketName) {
// try {
// InputStream is = file.getInputStream();
// ByteArrayOutputStream bos = new ByteArrayOutputStream();
// byte[] b = new byte[1024];
// int len = 0;
//
// while ((len = is.read(b)) != -1) {
// bos.write(b, 0, len);
// }
//
// byte[] uploadBytes = bos.toByteArray();
// UploadManager uploadManager = new UploadManager(new Configuration(Region.region0()));
// String uptoken = getUptoken(accessKey, secretKey, bucketName);
// uploadManager.put(uploadBytes, key, uptoken);
// return key;
// } catch (QiniuException var11) {
// QiniuException e = var11;
//
// try {
// log.error("文件上传失败: " + e.getMessage() + e.response.bodyString(), e);
// return null;
// } catch (QiniuException var10) {
// throw FrameworkException.instance("文件上传失败");
// }
// } catch (IOException var12) {
// IOException e = var12;
// log.error("文件上传失败" + e.getMessage(), e);
// throw FrameworkException.instance("文件上传失败");
// }
// }
//
// public String getUptoken(String accessKey, String secretKey, String bucketName) {
// Auth auth = Auth.create(accessKey, secretKey);
// return auth.uploadToken(bucketName, null, 3600L, null);
// }
//
// public String getUptoken(String bucketName) {
// return getUptoken(accessKey, secretKey, bucketName);
// }
//
// public String getFileUrl(String filePath, Boolean isPrivate) {
// return getFileUrl(filePath, null, isPrivate);
// }
//
// public String getFileUrl(String filePath, String domain, Boolean isPrivate) {
// return getFileUrl(filePath, domain, isPrivate, accessKey, secretKey);
// }
//
// public String getFileUrl(String filePath, String domain, Boolean isPrivate, String accessKey, String secretKey) {
// if (StringUtils.isBlank(filePath)) {
// return "";
// } else if (isPrivate) {
// if (StringUtils.isBlank(domain)) {
// domain = privateDomain;
// }
//
// String encodedFileName = null;
//
// try {
// encodedFileName = URLEncoder.encode(filePath, "utf-8");
// } catch (UnsupportedEncodingException var10) {
// UnsupportedEncodingException e = var10;
// log.error("文件url获取失败", e);
// throw FrameworkException.instance("文件url获取失败");
// }
//
// String publicUrl = domain + encodedFileName;
// Auth auth = Auth.create(accessKey, secretKey);
// long expireInSeconds = 3600L;
// return auth.privateDownloadUrl(publicUrl, expireInSeconds);
// } else {
// if (StringUtils.isBlank(domain)) {
// domain = publicDomain;
// }
//
// return domain + filePath;
// }
// }
//
// public void delete(String key, Boolean isPrivate) {
// Auth auth = Auth.create(accessKey, secretKey);
// Configuration cfg = new Configuration(Region.region0());
// BucketManager bucketManager = new BucketManager(auth, cfg);
//
// try {
// String bucketName;
// if (isPrivate) {
// bucketName = privateBucketName;
// } else {
// bucketName = publicBucketName;
// }
//
// bucketManager.delete(bucketName, key);
// } catch (QiniuException var5) {
// }
//
// }
//
// public void delete(String bucketName, String key) {
// Auth auth = Auth.create(accessKey, secretKey);
// Configuration cfg = new Configuration(Region.region0());
// BucketManager bucketManager = new BucketManager(auth, cfg);
//
// try {
// bucketManager.delete(bucketName, key);
// } catch (QiniuException var5) {
// }
//
// }
//
// }
