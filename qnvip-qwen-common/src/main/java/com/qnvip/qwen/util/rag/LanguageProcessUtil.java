package com.qnvip.qwen.util.rag;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.dictionary.CoreSynonymDictionary;
import com.hankcs.hanlp.dictionary.common.CommonSynonymDictionary;
import com.hankcs.hanlp.seg.common.Term;
import com.qnvip.qwen.util.Lists;

/**
 * {"title_tks^10", "title_sm_tks^5", "important_kwd^30", "important_tks^20", "question_tks^20", "content_ltks^2", *
 * "content_sm_ltks"}
 */

public class LanguageProcessUtil {

    // 通用词
    public static final List<String> STOP_WORDS =
        new ArrayList<>(Arrays.asList("请问", "您", "你", "我", "他", "是", "的", "就", "有", "于", "及", "即", "在", "为", "最", "有",
            "从", "以", "了", "将", "与", "吗", "吧", "中", "什么", "怎么", "哪个", "哪些", "啥", "相关"));
    // 标点符号
    public static final Nature[] ES_FILTER_NATURE = new Nature[] {Nature.w};
    private static final List<String> LOW_VALUE_WORDS = Arrays.asList("一个", "一种", "一些", "各种", "很多", "不少", "这个", "那个",
        "这些", "那些", "我们的", "大概", "大约", "可能", "也许", "似乎", "非常", "十分", "特别", "极其", "相当", "好的", "不错的", "一般的", "进行", "开始",
        "完成", "可以", "能够", "应该", "需要", "必须", "东西", "问题", "情况", "时间", "地方", "方式", "主要", "基本", "常见", "普通", "大", "小", "新",
        "旧", "多", "少", "看看", "瞧瞧", "告诉我", "看一下");

    /**
     * 相似度计算
     * 
     * @param words1
     * @param words2
     * @return
     */
    public static List<Double> computeSimilarityBatch(List<String> words1, List<List<String>> words2) {
        return words2.stream().map(e -> computeSimilarity(words1, e)).collect(Collectors.toList());
    }

    /**
     * 相似度计算
     * 
     * @param words1
     * @param words2
     * @return
     */
    public static Double computeSimilarity(List<String> words1, List<String> words2) {

        Set<String> allWords = new HashSet<>(words1);
        allWords.addAll(words2);

        Map<String, Integer> freq1 = getTermFreq(words1);
        Map<String, Integer> freq2 = getTermFreq(words2);

        // 向量构建
        List<Integer> vec1 = new ArrayList<>();
        List<Integer> vec2 = new ArrayList<>();
        for (String word : allWords) {
            vec1.add(freq1.getOrDefault(word, 0));
            vec2.add(freq2.getOrDefault(word, 0));
        }

        return cosineSimilarity(vec1, vec2);
    }

    private static Map<String, Integer> getTermFreq(List<String> words) {
        Map<String, Integer> freq = new HashMap<>();
        for (String word : words) {
            freq.put(word, freq.getOrDefault(word, 0) + 1);
        }
        return freq;
    }

    private static double cosineSimilarity(List<Integer> v1, List<Integer> v2) {
        int dotProduct = 0;
        int normA = 0;
        int normB = 0;
        for (int i = 0; i < v1.size(); i++) {
            int a = v1.get(i);
            int b = v2.get(i);
            dotProduct += a * b;
            normA += a * a;
            normB += b * b;
        }
        return normA == 0 || normB == 0 ? 0.0 : dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    /**
     * 相似度计算
     *
     * @param word1
     * @param word2
     * @return
     */
    public static double similarity(String word1, String word2) {
        return CoreSynonymDictionary.similarity(word1, word2);
    }

    private static String rmWWW(String txt) {
        String[][] patts = {{
            "是*(什么样的|哪家|一下|那家|请问|啥样|咋样了|什么时候|何时|何地|何人|是否|是不是|多少|哪里|怎么|哪儿|怎么样|如何|哪些|是啥|啥是|啊|吗|呢|吧|咋|什么|有没有|呀|谁|哪位|哪个)是*",
            ""}, {"(^| )(what|who|how|which|where|why)('re|'s)? ", " "},
            {"(^| )('s|'re|is|are|were|was|do|does|did|don't|doesn't|didn't|has|have|be|there|you|me|your|my|mine|just|please|may|i|should|would|wouldn't|will|won't|done|go|for|with|so|the|a|an|by|i'm|it's|he's|she's|they|they're|you're|as|by|on|in|at|up|out|down|of|to|or|and|if) ",
                " ",},};

        String otxt = txt;
        for (String[] patt : patts) {
            txt = txt.replaceAll(patt[0], patt[1]);
        }
        return txt.isEmpty() ? otxt : txt;
    }

    private static String removeStopWords(String txt) {
        if (txt == null || txt.trim().isEmpty()) {
            return txt;
        }
        for (String stopWord : STOP_WORDS) {
            txt = txt.replace(stopWord, " ");

        }
        return txt;
    }

    /**
     * 从文本字符串中移除高频低价值词
     * 
     * @param text 原始文本
     * @return 处理后的文本
     */
    public static String removeLowValueWordsFromText(String text) {
        for (String lowValueWord : LOW_VALUE_WORDS) {
            text = text.replace(lowValueWord, " ");

        }
        return text;
    }

    private static String addSpaceBetweenEngZh(String txt) {
        txt = txt.replaceAll("([A-Za-z]+[0-9]+)([\u4e00-\u9fa5]+)", "$1 $2");
        txt = txt.replaceAll("([A-Za-z])([\u4e00-\u9fa5]+)", "$1 $2");
        txt = txt.replaceAll("([\u4e00-\u9fa5]+)([A-Za-z]+[0-9]+)", "$1 $2");
        txt = txt.replaceAll("([\u4e00-\u9fa5]+)([A-Za-z])", "$1 $2");
        txt = txt.replaceAll("([\u4e00-\u9fa5])([0-9])", "$1 $2");
        txt = txt.replaceAll("([0-9])([\u4e00-\u9fa5])", "$1 $2");
        return txt;
    }

    /**
     * 繁体转简体
     * 
     * @param text
     * @return
     */
    private static String tradi2simp(String text) {
        return HanLP.convertToSimplifiedChinese(text).toLowerCase();
    }

    /**
     * 全角转半角
     * 
     * @param text
     * @return
     */
    private static String strQ2B(String text) {
        StringBuilder sb = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (c == 0x3000) {
                sb.append(' ');
            } else if (c >= 0xFF01 && c <= 0xFF5E) {
                sb.append((char)(c - 0xFEE0));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    public static List<String> getEsQuestion(String txt) {
        List<String> segment = LanguageProcessUtil.segment(keywordSearchProcess(txt));
        return segment.stream().map(String::trim).filter(e -> !e.isEmpty()).collect(Collectors.toList());
    }

    /**
     * 关键词搜索预处理
     * 
     * @param txt
     * @return
     */
    public static String keywordSearchProcess(List<String> txt) {
        return keywordSearchProcess(txt.stream().distinct().collect(Collectors.joining(" ")));
    }

    /**
     * 关键词搜索预处理
     * 
     * @param txt
     * @return
     */
    public static String keywordSearchProcess(String txt) {
        // 中英文增加空格
        txt = addSpaceBetweenEngZh(txt);
        // 繁体转简体
        txt = tradi2simp(strQ2B(txt.toLowerCase()));
        // 非字母数字汉字转换为空格
        txt = txt.replaceAll("[^\\p{IsHan}a-zA-Z0-9]+", " ");
        // 标准化空格
        txt = normalizeSpaces(txt);
        // 字母转小写
        txt = txt.toLowerCase();
        // 去除疑问词，语气助词
        txt = rmWWW(txt);
        // 移除高频低价值词
        txt = removeLowValueWordsFromText(txt);
        // 去除通用词
        txt = removeStopWords(txt);
        return txt;
    }

    public static String normalizeSpaces(String input) {
        if (input == null) {
            return null;
        }
        return input.trim().replaceAll("\\s+", " ");
    }

    /**
     * 同义词获取
     * 
     * @param
     * @return
     */
    public static List<String> synonyms(List<String> words, int topN) {
        return words.stream().map(e -> LanguageProcessUtil.synonyms(e, topN)).flatMap(List::stream)
            .collect(Collectors.toList());
    }

    /**
     * 同义词获取
     *
     * @param
     * @return
     */
    public static List<String> synonyms(String word, int topN) {
        CommonSynonymDictionary.SynonymItem synonymItem = CoreSynonymDictionary.get(word);
        if (synonymItem == null) {
            return new ArrayList<>();
        }
        return synonymItem.synonymList.stream().limit(topN).map(e -> e.realWord).collect(Collectors.toList());
    }

    /**
     * 分词
     * 
     * @param text
     * @return
     */
    public static List<String> segment(String text) {
        return segment(text, Lists.newArrayList(LanguageProcessUtil.ES_FILTER_NATURE));
    }

    /**
     * 分词
     *
     * @param text
     * @return
     */
    public static List<String> segment(String text, List<Nature> filterNature) {
        List<Term> segment = HanLP.segment(text);
        return segment.stream().filter(e -> !filterNature.contains(e)).map(e -> e.word).collect(Collectors.toList());
    }

    /**
     * 提取关键词
     * 
     * @param text
     * @param size
     * @return
     */
    public static List<String> extractKeyword(String text, int size) {
        return HanLP.extractKeyword(text, size);
    }

    /**
     * 摘要
     * 
     * @param text 带摘要文本
     * @param size 需要摘要的长度
     * @return
     */
    public static String getSummary(String text, int size) {
        return HanLP.getSummary(text, size);
    }

}
