package com.qnvip.qwen.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class SmartDateFormatUtil {
    /**
     * 根据日期的秒、分、小时、日、月字段是否为0，智能选择最合适的格式化表达式
     * 
     * @param date 待格式化的日期
     * @return 格式化后的日期字符串，省略无意义的时间部分
     */
    public static String format(Date date) {
        if (date == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 判断各时间字段是否为默认值
        boolean isSecondZero = calendar.get(Calendar.SECOND) == 0;
        boolean isMinuteZero = calendar.get(Calendar.MINUTE) == 0;
        boolean isHourZero = calendar.get(Calendar.HOUR_OF_DAY) == 0;
        boolean isDayDefault = calendar.get(Calendar.DAY_OF_MONTH) == 1;
        boolean isMonthDefault = calendar.get(Calendar.MONTH) == 0;

        String pattern;

        // 智能选择最简洁的格式
        if (!isSecondZero) {
            pattern = "yyyy年MM月dd日 HH:mm:ss";
        } else if (!isMinuteZero) {
            pattern = "yyyy年MM月dd日 HH:mm";
        } else if (!isHourZero) {
            pattern = "yyyy年MM月dd日 HH";
        } else if (!isDayDefault) {
            pattern = "yyyy年MM月dd日";
        } else if (!isMonthDefault) {
            pattern = "yyyy年MM月";
        } else {
            pattern = "yyyy年";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    // 测试示例
    public static void main(String[] args) {
        Calendar cal = Calendar.getInstance();

        // 完整日期时间
        cal.set(2023, 5, 15, 14, 30, 20);
        System.out.println(format(cal.getTime())); // 2023年06月15日 14:30:20

        // 秒为0
        cal.set(2023, 5, 15, 14, 30, 0);
        System.out.println(format(cal.getTime())); // 2023年06月15日 14:30

        // 分和秒为0
        cal.set(2023, 5, 15, 14, 0, 0);
        System.out.println(format(cal.getTime())); // 2023年06月15日 14

        // 小时、分、秒为0
        cal.set(2023, 5, 15, 0, 0, 0);
        System.out.println(format(cal.getTime())); // 2023年06月15日

        // 日、小时、分、秒为0（日为1）
        cal.set(2023, 5, 1, 0, 0, 0);
        System.out.println(format(cal.getTime())); // 2023年06月

        // 月、日、小时、分、秒为0（月为0，日为1）
        cal.set(2023, 0, 1, 0, 0, 0);
        System.out.println(format(cal.getTime())); // 2023年
    }
}
