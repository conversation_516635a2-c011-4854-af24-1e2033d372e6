package com.qnvip.qwen.util;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * dify工作流类型枚举
 */
@RequiredArgsConstructor
@Getter
public enum DifyWorkflowTypeEnum {

    // chat类型工作流
    CHAT(1, "chat"),
    // inspiration类型工作流
    INSPIRATION(2, "inspiration"),;

    private final int code;
    private final String desc;

    public static DifyWorkflowTypeEnum getByCode(int code) {
        for (DifyWorkflowTypeEnum value : DifyWorkflowTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}