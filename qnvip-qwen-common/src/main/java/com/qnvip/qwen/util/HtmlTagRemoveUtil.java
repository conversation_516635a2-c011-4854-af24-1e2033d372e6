package com.qnvip.qwen.util;

import java.util.regex.Pattern;

/**
 * HTML 标签移除工具类
 */
public class HtmlTagRemoveUtil {
    // 私有构造方法，防止实例化
    private HtmlTagRemoveUtil() {}

    // 移除 <script> 标签及其内容的正则表达式
    private static final Pattern SCRIPT_TAG_PATTERN = Pattern.compile("<script[^>]*>.*?</script>", Pattern.DOTALL);

    // 移除 <font> 标签的正则表达式
    private static final Pattern FONT_TAG_PATTERN = Pattern.compile("<font[^>]*>|</font>");

    // 移除 <span> 标签的正则表达式
    private static final Pattern SPAN_TAG_PATTERN = Pattern.compile("<span[^>]*>|</span>");

    /**
     * 移除字符串中的 HTML 标签
     *
     * @param input 包含 HTML 标签的字符串
     * @return 移除 HTML 标签后的字符串
     */
    public static String removeHtmlTags(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        // 移除 <script> 标签及其内容
        String result = SCRIPT_TAG_PATTERN.matcher(input).replaceAll("");

        // 移除 <font> 标签
        result = FONT_TAG_PATTERN.matcher(result).replaceAll("");

        return result;
    }

    public static String removeSpan(String input) {
        return SPAN_TAG_PATTERN.matcher(input).replaceAll("");
    }

    /**
     * <div style="text-align:center;">代扣失败四次取消当期代扣</div>
     * 
     * @param input
     * @return
     */
    public static String removeDiv(String input) {
        return input.replaceAll("<div[^>]*>|</div>", "");
    }

    public static void main(String[] args) {
        String htmlContent = "<div>Hello <font color='red'>World</font>! <script>alert('Hello');</script></div>";
        String result = removeHtmlTags(htmlContent);
        System.out.println(result);
    }

}
