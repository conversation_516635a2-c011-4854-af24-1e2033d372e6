package com.qnvip.qwen.util;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

// 表格结构封装
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelTableJson {

    public ExcelTableJson(List<ExcelSheet> data) {
        this.data = data;
    }

    private String fileName;

    /**
     * 工作簿
     */
    private List<ExcelSheet> data;

    @Data
    @AllArgsConstructor
    public static class ExcelSheet {
        /**
         * excel中工作簿名字，sheetName
         */
        private final String name;

        /**
         * table中的行数据 List<String> 为行 List<List<String>> 为列
         */
        private final List<List<String>> table;
    }
}
