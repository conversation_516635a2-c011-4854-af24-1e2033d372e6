package com.qnvip.qwen.util;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;

import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * ExcelJsonToMarkdownTableUtil 类用于将 Excel 导出的 JSON 数据转换为 Markdown 表格格式。 自动探测第一行是否只有第一个单元格有数据，如果是，则将其升级为表名。
 */
public class ExcelJsonToMarkdownTableUtil {

    /**
     * 将 JSON 字符串转换为 Markdown 表格格式。 1. 清除空行和特殊字符。 2. 探测表名。 3. 转换为 Markdown 表格。
     *
     * @param json 输入的 JSON 字符串
     * @return 转换后的 Markdown 表格字符串
     */
    public static List<SheetData> convertJsonToMarkdownTable(String json) {
        return convertJsonToMarkdownTable(JSON.parseObject(json, ExcelTableJson.class));
    }

    /**
     * 将 JSON 字符串转换为 Markdown 表格格式。 1. 清除空行和特殊字符。 2. 探测表名。 3. 转换为 Markdown 表格。
     *
     * @param json 输入的 JSON 字符串
     * @return 转换后的 Markdown 表格字符串
     */
    public static List<SheetData> convertJsonToMarkdownTable(ExcelTableJson json) {
        List<ExcelTableJson.ExcelSheet> data = json.getData();

        List<SheetData> markdownTables = new ArrayList<>();

        for (ExcelTableJson.ExcelSheet excelSheet : data) {
            if (excelSheet.getTable().isEmpty()) {
                continue;
            }
            SheetData sheetData = new SheetData();
            sheetData.setData(extractSheet(excelSheet));
            sheetData.setName(excelSheet.getName());
            markdownTables.add(sheetData);
        }

        return markdownTables;
    }

    /**
     * 从 JSON 对象中提取表格数据并构建 Markdown 表格。
     *
     * @param tableData 包含表格数据的 JSON 对象
     */
    private static String extractSheet(ExcelTableJson.ExcelSheet tableData) {
        TableStructure tableStructure = parseTableStructure(tableData);
        if (tableStructure == null) {
            return "";
        }
        return buildMarkdownTable(tableStructure);

    }

    /**
     * 解析 JSON 对象中的表格结构。
     *
     * @param tableData 包含表格数据的 JSON 对象
     * @return 解析后的 TableStructure 对象
     */
    private static TableStructure parseTableStructure(ExcelTableJson.ExcelSheet tableData) {
        List<List<String>> table = tableData.getTable();

        List<List<String>> rows = filterEmptyColumn(filterEmptyRow(replaceSpecialCharacters(table)));

        if (ObjectUtils.isEmpty(rows)) {
            return null;
        }

        return new TableStructure(tableData.getName(), detectTableName(rows), extractHeaderRow(rows),
            extractDataRows(rows));
    }

    private static List<List<String>> replaceSpecialCharacters(List<List<String>> jsonTable) {
        return jsonTable.stream().map(row -> row.stream().map(String::trim).map(e -> e.replace("|", " "))
            .map(e -> e.replace("\n", "  ")).collect(Collectors.toList())).collect(Collectors.toList());
    }

    /**
     * 过滤掉这一行单元格都为空的行。
     */
    private static List<List<String>> filterEmptyRow(List<List<String>> jsonTable) {
        return jsonTable.stream().filter(row -> row.stream().anyMatch(cell -> cell != null && !cell.trim().isEmpty()))
            .collect(Collectors.toList());
    }

    /**
     * 过滤掉这一列单元格都为空的列 List<List<String>> jsonTable jsonTable 的外层List是Column 内层List是Row String是单元格的值
     *
     *
     */
    private static List<List<String>> filterEmptyColumn(List<List<String>> jsonTable) {
        if (jsonTable == null) {
            throw new IllegalArgumentException("Input table cannot be null");
        }
        if (jsonTable.isEmpty()) {
            return new ArrayList<>();
        }
        int columnCount = 0;
        // Determine maximum number of columns in any row
        for (List<String> row : jsonTable) {
            if (row != null && row.size() > columnCount) {
                columnCount = row.size();
            }
        }
        // Determine which columns have any non-empty cell
        boolean[] keep = new boolean[columnCount];
        for (int col = 0; col < columnCount; col++) {
            for (List<String> row : jsonTable) {
                if (row != null && col < row.size()) {
                    String cell = row.get(col);
                    if (cell != null && !cell.trim().isEmpty()) {
                        keep[col] = true;
                        break;
                    }
                }
            }
        }
        // Build filtered table
        List<List<String>> result = new ArrayList<>();
        for (List<String> row : jsonTable) {
            List<String> newRow = new ArrayList<>();
            if (row != null) {
                for (int col = 0; col < columnCount; col++) {
                    if (keep[col]) {
                        newRow.add(col < row.size() ? row.get(col) : null);
                    }
                }
            }
            result.add(newRow);
        }
        return result;
    }

    /**
     * 探测表格名称。如果第一行只有第一个单元格有数据，则将其作为表名。
     *
     * @param rows 表格行数据
     * @return 表格名称，如果没有则返回空字符串
     */
    private static String detectTableName(List<List<String>> rows) {
        if (rows.isEmpty()) {
            return "";
        }

        List<String> firstRow = rows.get(0);
        return isTableNameRow(firstRow) ? firstRow.get(0) : "";
    }

    /**
     * 判断某一行是否为表名行（即只有第一个单元格有数据）。
     *
     * @param row 表格行数据
     * @return 如果是表名行则返回 true，否则返回 false
     */
    private static boolean isTableNameRow(List<String> row) {
        return !row.isEmpty() && !row.get(0).isEmpty() && row.stream().skip(1).allMatch(String::isEmpty);
    }

    /**
     * 提取表头行。
     *
     * @param rows 表格行数据
     * @return 表头行数据
     */
    private static List<String> extractHeaderRow(List<List<String>> rows) {
        // if (ObjectUtils.isEmpty(rows)) {
        // return new ArrayList<>();
        // }
        int startIndex = isTableNameRow(rows.get(0)) ? 1 : 0;
        return rows.size() > startIndex ? rows.get(startIndex) : new ArrayList<>();
    }

    /**
     * 提取数据行。
     *
     * @param rows 表格行数据
     * @return 数据行列表
     */
    private static List<List<String>> extractDataRows(List<List<String>> rows) {
        // if (ObjectUtils.isEmpty(rows)) {
        // return new ArrayList<>();
        // }
        int startIndex = isTableNameRow(rows.get(0)) ? 2 : 1;
        return rows.size() > startIndex ? rows.subList(startIndex, rows.size()) : new ArrayList<>();
    }

    /**
     * 构建 Markdown 表格。
     *
     * @param table 包含表格数据的 TableStructure 对象
     * @return 生成的 Markdown 表格字符串
     */
    private static String buildMarkdownTable(TableStructure table) {
        return buildHeader(table.getHeader()) + buildSeparator(table.getHeader())
            + buildBody(table.getDataRows()) + "\n\n";
    }

    /**
     * 格式化表格标题。
     *
     * @param table 包含表格数据的 TableStructure 对象
     * @return 格式化后的标题字符串
     */
    private static String formatTitle(TableStructure table) {
        if (ObjectUtils.isEmpty(table.getSheetName()) && ObjectUtils.isEmpty(table.getSheetName())) {
            return "";
        }
        String baseTitle = ObjectUtils.isEmpty(table.getSheetName()) ? "" : "### 表格: " + table.getSheetName();
        return table.getTableName().isEmpty() ? baseTitle + "\n\n"
            : String.format("%s %s\n\n", baseTitle, table.getTableName());
    }

    /**
     * 构建表头行。
     *
     * @param headerRow 表头行数据
     * @return 生成的表头行字符串
     */
    private static String buildHeader(List<String> headerRow) {
        return "| " + String.join(" | ", headerRow) + " |\n";
    }

    /**
     * 构建分隔线。
     *
     * @param headerRow 表头行数据
     * @return 生成的分隔线字符串
     */
    private static String buildSeparator(List<String> headerRow) {
        return "|" + headerRow.stream().map(e -> " --- ").collect(Collectors.joining("|")) + "|\n";
    }

    /**
     * 构建表格内容。
     *
     * @param rows 数据行列表
     * @return 生成的表格内容字符串
     */
    private static String buildBody(List<List<String>> rows) {
        return rows.stream().map(row -> "| " + String.join(" | ", row) + " |\n").collect(Collectors.joining());
    }

    /**
     * 读取 JSON 文件内容。
     *
     * @param filePath 文件路径
     * @return 文件内容字符串
     */
    private static String readJsonFile(String filePath) {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
        } catch (IOException e) {
            System.err.println("读取文件时出错：" + e.getMessage());
        }
        return content.toString();
    }

    /**
     * 主方法，用于测试 JSON 文件转换为 Markdown 表格的功能。
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out
            .println(convertJsonToMarkdownTable(readJsonFile("C:\\Users\\<USER>\\Desktop\\exceljson.json")));
    }

    @Data
    @AllArgsConstructor
    private static class TableStructure {
        private final String sheetName;
        private final String tableName;
        private final List<String> header;
        private final List<List<String>> dataRows;

        public boolean isValid() {
            return !header.isEmpty() && !dataRows.isEmpty();
        }
    }

    @Data
    public static class SheetData {
        private String name;
        private String data;
        private String checksum;
    }
}
