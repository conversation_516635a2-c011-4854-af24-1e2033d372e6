package com.qnvip.qwen.util;

public class TextCleanUtil {
    // 私有构造方法，防止实例化
    private TextCleanUtil() {}

    /**
     * 使用正则表达式一次性完成所有替换（效率更高）
     * 
     * @param input 原始文本
     * @return 清理后的文本
     */
    public static String cleanTextWithRegex(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 合并所有替换规则到一个正则表达式中
        return input.replace("\r", "\n");
    }

    // 测试方法
    public static void main(String[] args) {
        System.out.println("原始文本:");
        String testText = "这是    ``一段测试\r文本\n\n\n有多个  空白   和换行\r\n\r\n字符";

        System.out.println(testText);

        System.out.println("\n清理后文本:");
        String x = cleanTextWithRegex(testText);
        System.out.println(x);
    }

}
