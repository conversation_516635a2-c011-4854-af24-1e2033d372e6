package com.qnvip.qwen.util;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;

import com.alibaba.fastjson2.JSON;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 语雀LakeBoard JSON转换为Markdown工具类
 */
public class YuqueLakeBoardJSONToMarkdownUtil {

    /**
     * 将LakeBoard JSON转换为Markdown格式
     *
     * @param json 输入的JSON字符串
     * @return 转换后的Markdown字符串，如果输入不是Board类型或lakeboard格式，则返回原始JSON
     */
    public static String lakeboardToMarkdown(String json) {
        YuqueLakeBoard yuqueLakeBoard = JSON.parseObject(json, YuqueLakeBoard.class);

        StringBuilder sb = new StringBuilder();

        List<DiagramDataBody> mindmap = yuqueLakeBoard.getDiagramData().getBody().stream()
            .filter(e -> e.getType().equals("mindmap")).collect(Collectors.toList());
        sb.append(convertToMarkdown(mindmap));

        List<DiagramDataBody> geometry = yuqueLakeBoard.getDiagramData().getBody().stream()
            .filter(e -> e.getType().equals("geometry"))
            // .filter(e -> e.getType().equals("geometry") && e.getCategory() != null)
            // .filter(e -> e.getType().equals("geometry")&& e.getCategory()!=null &&
            // e.getCategory().equals("flowchart"))
            .collect(Collectors.toList());

        List<DiagramDataBody> line = yuqueLakeBoard.getDiagramData().getBody().stream()
            .filter(e -> e.getType().equals("line")).collect(Collectors.toList());

        sb.append(convertMermindFlowChat(geometry, line));

        return sb.toString();

    }

    /**
     * 将LakeboardBody列表转换为Markdown字符串
     *
     * @param bodies LakeboardBody列表
     * @return 转换后的Markdown字符串
     */
    private static String convertToMarkdown(List<DiagramDataBody> bodies) {
        // 将每个LakeboardBody转换为Markdown并拼接
        return bodies.stream().map(YuqueLakeBoardJSONToMarkdownUtil::convertMermindMindMap)
            .collect(Collectors.joining("\n\n"));
    }

    /**
     * 将单个LakeboardBody转换为Markdown字符串
     *
     * @param body LakeboardBody对象
     * @return 转换后的Markdown字符串
     */
    private static String convertMermindMindMap(DiagramDataBody body) {
        List<String> markdown = new ArrayList<>();
        markdown.add("```mermaid");
        markdown.add("mindmap");
        markdown.add("  root(" + body.getHtml() + ")");
        convertMermindMindMap(markdown, body.getChildren(), 3);
        markdown.add("```");
        return markdown.stream().collect(Collectors.joining("\n"));
    }

    public static void convertMermindMindMap(List<String> markdown, List<DiagramDataBodyChildrenOfMindmap> nodes,
        int level) {
        if (ObjectUtils.isEmpty(nodes)) {
            return;
        }
        for (DiagramDataBodyChildrenOfMindmap node : nodes) {
            markdown.add(getPrefix(level) + node.getHtml());
            convertMermindMindMap(markdown, node.getChildren(), level + 1);
        }
    }

    private static String getPrefix(int level) {
        StringBuilder prefix = new StringBuilder();
        for (int i = 0; i < level; i++) {
            prefix.append("  ");
        }
        return prefix.toString();
    }

    /**
     * 生成 mermind的flowchat
     *
     * @param geometry
     * @param lines
     * @return
     */
    public static String convertMermindFlowChat(List<DiagramDataBody> geometry, List<DiagramDataBody> lines) {
        if (ObjectUtils.isEmpty(geometry) || ObjectUtils.isEmpty(lines)) {
            return "";
        }
        // 添加逻辑，将id和对应的DiagramDataBody对象映射起来 ,方便查找
        Map<String, DiagramDataBody> idDataMap =
            geometry.stream().collect(Collectors.toMap(DiagramDataBody::getId, e -> e));

        // 长id转短id
        Map<String, Integer> idShortMap = new HashMap<>();

        Integer id = 0;
        for (DiagramDataBody e : geometry) {
            idShortMap.putIfAbsent(e.getId(), id++);
        }
        for (DiagramDataBody e : lines) {
            idShortMap.putIfAbsent(e.getId(), id++);
        }

        List<String> markdown = new ArrayList<>();
        markdown.add("```mermaid");
        markdown.add("flowchart TD");

        // 只处理 category=line 且 shape=elbow 的连线
        for (DiagramDataBody line : lines) {

            // 源节点 & 目标节点
            String sourceId = line.getSource().getId();
            String targetId = line.getTarget().getId();
            DiagramDataBody srcNode = idDataMap.get(sourceId);
            DiagramDataBody tgtNode = idDataMap.get(targetId);
            if (srcNode == null || tgtNode == null) {
                // 找不到对应节点，跳过
                continue;
            }

            // 格式化节点文字及形状
            String srcStr = formatNode(idShortMap, srcNode);
            String tgtStr = formatNode(idShortMap, tgtNode);

            // 可选的箭头标签
            String label = line.getHtml();
            if (label != null && !getHtmlData(label).isEmpty()) {
                markdown.add("    " + srcStr + " -->|" + getHtmlData(label) + "| " + tgtStr);
            } else {
                markdown.add("    " + srcStr + " --> " + tgtStr);
            }
        }

        markdown.add("```");
        if (markdown.size() == 3) {
            return "";
        }
        return markdown.stream().collect(Collectors.joining("\n"));

    }

    private static String getHtmlData(String label) {
        return ReplaceSpecialCharactersRemoveUtil.removeUnicodeControlChars(
            HtmlTagRemoveUtil.removeSpan(HtmlTagRemoveUtil.removeHtmlTags(HtmlTagRemoveUtil.removeDiv(label.trim()))));
    }

    /**
     * 根据节点的 shape 决定用 [] 还是 {} 包裹
     */
    private static String formatNode(Map<String, Integer> idMap, DiagramDataBody node) {
        Integer id = idMap.get(node.getId());
        String text = getHtmlData(node.getHtml());

        // decision 用 {}，其他（如 process）都用 []
        if ("decision".equals(node.getShape())) {
            return id + "{" + text + "}";
        } else {
            return id + "[" + text + "]";
        }
    }

    /**
     * 读取JSON文件内容
     *
     * @param filePath 文件路径
     * @return 文件内容字符串
     */
    private static String readJsonFile(String filePath) {
        try {
            return new String(Files.readAllBytes(Paths.get(filePath)));
        } catch (IOException e) {
            System.err.println("读取文件时出错：" + e.getMessage());
            return "";
        }
    }

    /**
     * 主方法，用于测试
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        String filePath = "C:\\Users\\<USER>\\Desktop\\boardmindmap.txt";
        String json = readJsonFile(filePath);
        String markdown = lakeboardToMarkdown(json);
        System.out.println(markdown);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    protected static class YuqueLakeBoard {
        /**
         * lakeboard
         */
        private String format;

        /**
         * Board
         */
        private String type;

        private DiagramData diagramData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    protected static class DiagramData {
        private List<DiagramDataBody> body;
    }

    /**
     * LakeBoard body数据结构
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    protected static class DiagramDataBody {

        /**
         * 本身的id
         */
        private String id;
        /**
         * mindmap geometry
         */
        private String type;
        /**
         * flowchart line
         */
        private String category;

        /**
         * process普通节点 decision决策节点 elbow箭头
         */
        private String shape;

        private String html;

        /**
         * category=line 并且shape=elbow时才有此字段
         */
        private ElementId source;

        /**
         * category=line 并且shape=elbow时才有此字段
         */
        private ElementId target;

        private List<DiagramDataBodyChildrenOfMindmap> children;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ElementId {
        private String id;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    protected static class DiagramDataBodyChildrenOfMindmap {

        private String html;
        private List<DiagramDataBodyChildrenOfMindmap> children;
    }
}