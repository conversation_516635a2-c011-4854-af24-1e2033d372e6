package com.qnvip.qwen.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @desc markdown 针对table的切块处理
 * @createTime 2025年04月17日 17:59:00
 */
public class MarkdownChunkTableUtil {
    public static final Pattern TABLE_PATTERN =
        Pattern.compile("\\|.*\\|(?:\\R\\|(?: *[-:]+ *\\|)+)?(?:\\R\\|.*\\|)+", Pattern.MULTILINE);

    public static List<String> getSegments(String content) {
        Matcher matcher = TABLE_PATTERN.matcher(content);
        int lastEnd = 0;
        List<String> segments = new ArrayList<>();

        while (matcher.find()) {
            if (matcher.start() > lastEnd) {
                segments.add(content.substring(lastEnd, matcher.start()).trim());
            }
            segments.add(matcher.group());
            lastEnd = matcher.end();
        }

        if (lastEnd < content.length()) {
            segments.add(content.substring(lastEnd).trim());
        }
        return segments;
    }

    public static void appendWithNewLine(StringBuilder builder, String text, List<String> resultList,
        Integer chunkLength) {
        if (builder.length() + text.length() + 1 > chunkLength) {
            resultList.add(builder.toString());
            builder.setLength(0);
        }
        builder.append(text).append("\n");
    }

    /**
     * 表格处理
     * 
     * @param tableContent
     * @param currentChunk
     * @param resultList
     * @param chunkLength
     */
    public static void processTableContent(String tableContent, StringBuilder currentChunk, List<String> resultList,
        Integer chunkLength) {
        String[] lines = tableContent.split("\n");
        if (lines.length < 2) {
            return;
        }
        // 获取表头和分割线
        String header = lines[0] + "\n" + lines[1] + "\n";

        if (currentChunk.length() + header.length() > chunkLength) {
            // 如果当前chunk加上表头已经超过限制，则先添加当前的chunk到结果列表中
            resultList.add(currentChunk.toString());
            currentChunk.setLength(0);
        }
        // 拼接表头到当前chunk中
        currentChunk.append(header);

        // 循环表格内容
        for (int i = 2; i < lines.length; i++) {
            String line = lines[i] + "\n";
            if (currentChunk.length() + line.length() > chunkLength) {
                // 如果当前chunk加上当前行已经超过限制，则先添加当前的chunk到结果列表中
                resultList.add(currentChunk.toString());
                // 重置当前chunk，并添加表头和当前行
                currentChunk.setLength(0);
                currentChunk.append(header).append(line);
            } else {
                // 否则，直接添加当前行到当前chunk中
                currentChunk.append(line);
            }
        }

    }
}
