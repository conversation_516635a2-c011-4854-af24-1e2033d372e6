package com.qnvip.qwen.util.docx2md;

import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.xwpf.usermodel.XWPFDocument;

import com.qnvip.qwen.util.docx2md.processor.DocxStructureProcessor;
import com.qnvip.qwen.util.docx2md.processor.ExcelProcessor;
import com.qnvip.qwen.util.docx2md.processor.ImageProcessor;
import com.qnvip.qwen.util.docx2md.vo.ExcelTable;
import com.qnvip.qwen.util.docx2md.vo.ImageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * Docx格式文档转Markdown工具类
 * 
 * <p>
 * 此工具类用于将Microsoft Word文档(.docx)转换为Markdown格式。 支持转换文档中的文本、标题、列表、表格、图片和嵌入的Excel对象。 转换后的内容保存为一个Markdown文件(output.md)。
 * </p>
 */
@Slf4j
public class DocxToMarkdownUtil {

    /**
     * 主方法，用于命令行执行转换
     *
     * @param args 命令行参数（未使用）
     */
    public static void main(String[] args) {
        String docxPath = "C:\\Users\\<USER>\\Desktop\\25W03自营部报告.docx";
        try {
            String markdownContent = docxToMarkdown(docxPath);
            // 在当前工作目录生成output.md文件
            Path outputPath = Paths.get("output.md");
            Files.write(outputPath, markdownContent.getBytes("UTF-8"));
            log.info("转换完成！结果已保存到 {}", outputPath.toAbsolutePath());
            // log.info("转换完成！markdownContent {}", markdownContent);
        } catch (Exception e) {
            log.error("处理文件时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 将Word文档(.docx)转换为Markdown格式
     * 
     * <p>
     * 此方法打开Word文档，提取其中的文本、图片和Excel对象， 然后将它们转换为Markdown格式。图片和Excel相关的资源会被保存到当前工作目录的子文件夹中。
     * </p>
     * 
     * @param docxPath Word文档的路径
     * @return 转换后的Markdown内容字符串
     * @throws Exception 如果转换过程中发生错误
     */
    public static String docxToMarkdown(String docxPath) throws Exception {
        try (XWPFDocument doc = new XWPFDocument(new FileInputStream(docxPath))) {

            // 从文档中提取图片
            List<ImageInfo> imagePaths = ImageProcessor.extractImages(doc);

            // 提取文档中的Excel对象
            List<ExcelTable> excelTables = new ArrayList<>();
            excelTables.addAll(ExcelProcessor.extractOLEObjects(doc));

            // 处理文档结构并生成最终内容
            List<String> markdownContent = DocxStructureProcessor.processDocument(doc, imagePaths, excelTables);

            // 只返回转换后的Markdown内容，不保存文件（保存操作由main方法完成）
            return String.join("\n\n", markdownContent);
        }
    }
}