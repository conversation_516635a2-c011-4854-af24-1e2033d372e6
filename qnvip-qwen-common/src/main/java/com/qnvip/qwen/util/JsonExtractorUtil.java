package com.qnvip.qwen.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.hutool.json.JSONUtil;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月02日 10:29:00
 */
public class JsonExtractorUtil {
    public static String extractJsonContent(String input) {
        // 首先尝试直接解析整个是否为合法JSON
        if (JSONUtil.isTypeJSON(input)) {
            return input; // 如果是合法JSON直接返回
        } else {
            // 如果不是JSON，尝试提取code block中的内容
            Pattern pattern = Pattern.compile("```json\\s*(.*?)\\s*```", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(input);
            if (matcher.find()) {
                return matcher.group(1).trim();
            } else {
                throw new RuntimeException("输入既不是有效的JSON，也未包含```json ```代码块");
            }
        }
    }

    public static void main(String[] args) {
        String input2 =
            "由于提供的知识组中包含一个无法直接在文本中展示的SVG图像链接，而生成问题的任务要求基于确切的文字内容进行。因此，在没有具体的文字描述或图解说明的情况下，我将假设该图像的内容是关于“全款转分期订单流转状态机”的流程，并尝试根据这一假设来拆分知识并生成相应的问题和答案对。\n\n以下是一个基于假设的输出示例：\n\n```json\n[\n    {\"question\":\"什么是'全款转分期订单流转状态机'?\",\"answer\":\"'全款转分期订单流转状态机'是一种用于描述从一次性支付全款转换为分期付款过程中的不同状态及其转换逻辑的方法。\"},\n    {\"question\":\"在'全款转分期订单流转状态机'中，可能包含哪些状态?\",\"answer\":\"在'全款转分期订单流转状态机'中，可能会包括待审核、审核通过、审核拒绝、等待用户确认、已确认等状态。\"},\n    {\"question\":\"'全款转分期'流程中的第一步是什么?\",\"answer\":\"'全款转分期'流程的开始阶段可能是提交转换申请或请求审核。\"},\n    {\"question\":\"如果在'全款转分期订单流转状态机'中遇到审核拒绝，下一步可能是什么?\",\"answer\":\"如果在'全款转分期订单流转状态机'中遇到审核拒绝，下一步可能是重新提交资料或修改申请条件后再次尝试。\"}\n]\n```\n\n请注意，以上内容是基于对“全款转分期订单流转状态机”这个概念的一般理解编写的，并非直接从提供的图像中提取的特定信息。 若要获得更准确的问题和答案，请提供关于该流程的具体文字说明或者描述图像中的关键元素。"; // 包含JSON代码块

        System.out.println(extractJsonContent(input2)); // 输出: {"question":"What?","answer":"Yes"}
    }
}
