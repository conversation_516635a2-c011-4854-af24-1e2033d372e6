package com.qnvip.qwen.util;

public class TokenCalculator {
    // 中文字符与token的换算比例
    private static final double CHINESE_CHAR_TO_TOKEN_RATIO = 0.6;
    // 英文字符与token的换算比例
    private static final double ENGLISH_CHAR_TO_TOKEN_RATIO = 0.3;

    /**
     * 计算纯中文字符串的token数量
     * 
     * @param chineseText 中文字符串
     * @return token数量
     */
    private static long calculateChineseTokens(String chineseText) {
        if (chineseText == null || chineseText.isEmpty()) {
            return 0;
        }
        return Math.round(chineseText.length() * CHINESE_CHAR_TO_TOKEN_RATIO);
    }

    /**
     * 计算纯英文字符串的token数量
     * 
     * @param englishText 英文字符串
     * @return token数量
     */
    private static long calculateEnglishTokens(String englishText) {
        if (englishText == null || englishText.isEmpty()) {
            return 0;
        }
        return Math.round(englishText.length() * ENGLISH_CHAR_TO_TOKEN_RATIO);
    }

    /**
     * 计算混合文本的token数量（简单按字符判断中英文）
     * 
     * @param mixedText 混合文本
     * @return token数量
     */
    public static long calculateMixedTokens(String mixedText) {
        if (mixedText == null || mixedText.isEmpty()) {
            return 0;
        }

        long chineseChars = 0;
        long englishChars = 0;

        for (char c : mixedText.toCharArray()) {
            // 简单判断中文字符（Unicode范围）
            if (isChineseCharacter(c)) {
                chineseChars++;
            } else {
                // 其他字符按英文计算（实际可能包含数字、符号等，这里简化处理）
                englishChars++;
            }
        }

        return Math.round(chineseChars * CHINESE_CHAR_TO_TOKEN_RATIO + englishChars * ENGLISH_CHAR_TO_TOKEN_RATIO);
    }

    /**
     * 判断字符是否为中文字符（简化版）
     * 
     * @param c 字符
     * @return 是否为中文字符
     */
    private static boolean isChineseCharacter(char c) {
        // 基本汉字Unicode范围：0x4E00 - 0x9FFF
        return c >= 0x4E00 && c <= 0x9FFF;
    }

    public static void main(String[] args) {
        // 示例用法
        String chineseText = "这是一段中文文本";
        String englishText = "This is an English text.";
        String mixedText = chineseText + englishText;

        System.out.println("中文文本Token数: " + calculateChineseTokens(chineseText));
        System.out.println("英文文本Token数: " + calculateEnglishTokens(englishText));
        System.out.println("混合文本Token数: " + calculateMixedTokens(mixedText));
    }
}