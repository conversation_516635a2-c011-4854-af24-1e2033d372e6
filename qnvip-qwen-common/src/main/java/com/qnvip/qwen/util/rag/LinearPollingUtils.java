package com.qnvip.qwen.util.rag;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class LinearPollingUtils {

    /**
     * 线性梯度加权轮询算法，将重要性高的实体分配更多文本块。
     *
     * @param entitiesOrRelations 按重要性降序排序的实体或关系（每个为Map，包含 sorted_chunks）
     * @param maxRelatedChunks 最重要实体/关系的最大分配 chunk 数
     * @param minRelatedChunks 最不重要实体/关系的最小分配 chunk 数
     * @return 选出的 chunk ID 列表
     */
    public static List<String> linearGradientWeightedPolling(List<Map<String, Object>> entitiesOrRelations,
        int maxRelatedChunks, int minRelatedChunks) {
        if (entitiesOrRelations == null || entitiesOrRelations.isEmpty()) {
            return Collections.emptyList();
        }

        int n = entitiesOrRelations.size();

        // 仅一个实体，直接返回前N个 chunk
        if (n == 1) {
            List<String> sortedChunks =
                (List<String>)entitiesOrRelations.get(0).getOrDefault("sorted_chunks", Collections.emptyList());
            return sortedChunks.subList(0, Math.min(maxRelatedChunks, sortedChunks.size()));
        }

        // Step 1: 计算每个实体预期分配的 chunk 数（线性递减）
        List<Integer> expectedCounts = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            double ratio = (double)i / (n - 1); // 0 到 1
            double expected = maxRelatedChunks - ratio * (maxRelatedChunks - minRelatedChunks);
            expectedCounts.add((int)Math.round(expected));
        }

        // Step 2: 第一轮分配
        List<String> selectedChunks = new ArrayList<>();
        List<Integer> usedCounts = new ArrayList<>();
        int totalRemaining = 0;

        for (int i = 0; i < n; i++) {
            Map<String, Object> entityRel = entitiesOrRelations.get(i);
            List<String> sortedChunks = (List<String>)entityRel.getOrDefault("sorted_chunks", Collections.emptyList());
            int expected = expectedCounts.get(i);
            int actual = Math.min(expected, sortedChunks.size());

            selectedChunks.addAll(sortedChunks.subList(0, actual));
            usedCounts.add(actual);

            if (expected > actual) {
                totalRemaining += (expected - actual);
            }
        }

        // Step 3: 多轮补充剩余配额
        for (int r = 0; r < totalRemaining; r++) {
            boolean allocated = false;

            for (int i = 0; i < n; i++) {
                Map<String, Object> entityRel = entitiesOrRelations.get(i);
                List<String> sortedChunks =
                    (List<String>)entityRel.getOrDefault("sorted_chunks", Collections.emptyList());
                int used = usedCounts.get(i);

                if (used < sortedChunks.size()) {
                    selectedChunks.add(sortedChunks.get(used));
                    usedCounts.set(i, used + 1);
                    allocated = true;
                    break; // 一轮只补一个
                }
            }

            // 全部耗尽，退出
            if (!allocated) {
                break;
            }
        }

        return selectedChunks;
    }
}
