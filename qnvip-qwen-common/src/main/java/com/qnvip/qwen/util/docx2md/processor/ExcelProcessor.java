package com.qnvip.qwen.util.docx2md.processor;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPicture;
import org.apache.poi.hssf.usermodel.HSSFPictureData;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.opc.PackagePart;
import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.apache.poi.openxml4j.opc.PackageRelationshipCollection;
import org.apache.poi.openxml4j.opc.TargetMode;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFShape;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

import com.qnvip.qwen.util.ExcelJsonToMarkdownTableUtil;
import com.qnvip.qwen.util.ExcelToExcelJsonUtil;
import com.qnvip.qwen.util.FileSystemUtil;
import com.qnvip.qwen.util.docx2md.vo.ExcelImageInfo;
import com.qnvip.qwen.util.docx2md.vo.ExcelTable;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Word文档中Excel对象处理工具类
 * 
 * <p>
 * 此工具类用于处理Word文档中嵌入的Excel对象，将其转换为Markdown表格格式。 包括提取Excel内容、图片等。
 * </p>
 */
@Slf4j
public class ExcelProcessor {

    /**
     * 从Word文档中提取OLE对象（主要是Excel表格）
     * 
     * <p>
     * 检测并提取文档中的OLE对象（如嵌入的Excel表格），将其转换为Markdown表格格式。
     * </p>
     * 
     * @param doc Word文档对象
     * @return Excel表格列表
     * @throws Exception 如果提取过程中发生错误
     */
    public static List<ExcelTable> extractOLEObjects(XWPFDocument doc) throws Exception {
        List<ExcelTable> excelTables = new ArrayList<>();
        log.info("开始提取文档中的OLE对象...");

        try {
            // 获取文档中的所有关系
            PackageRelationshipCollection relationships = doc.getPackagePart().getRelationships();

            // 存储关系ID和内容类型的映射
            Map<String, String> relIdToContentType = new HashMap<>();
            // 存储关系ID和目标ID的映射
            Map<String, String> relIdToTargetId = new HashMap<>();

            // 首先遍历所有关系，建立映射关系
            for (PackageRelationship rel : relationships) {
                String relId = rel.getId();
                String targetId = "";

                try {
                    targetId = rel.getTargetURI().toString();
                } catch (Exception e) {
                    // 对于绝对URI错误进行特殊处理
                    String uriString = rel.getTargetURI().getSchemeSpecificPart();
                    if (uriString != null) {
                        targetId = uriString;
                        log.info("处理特殊URI关系: {}", targetId);
                    } else {
                        log.warn("无法解析关系URI: {}", e.getMessage());
                        continue;
                    }
                }

                String contentType = "";

                try {
                    // 安全获取关系部分
                    if (!rel.getTargetMode().toString().equalsIgnoreCase("External")) {
                        PackagePart part = doc.getPackagePart().getRelatedPart(rel);
                        contentType = part.getContentType();
                    } else {
                        // 处理外部链接
                        contentType = "external/hyperlink";
                        log.info("发现外部链接: {}", targetId);
                    }

                    relIdToContentType.put(relId, contentType);
                    relIdToTargetId.put(relId, targetId);
                    log.debug("关系ID: {}, 目标ID: {}, 内容类型: {}", relId, targetId, contentType);
                } catch (Exception e) {
                    log.debug("获取关系信息出错: {}", e.getMessage());
                }
            }

            // 遍历所有段落，查找OLE对象引用
            Map<String, Integer> relIdToPosition = new HashMap<>();
            for (int paraIndex = 0; paraIndex < doc.getParagraphs().size(); paraIndex++) {
                XWPFParagraph paragraph = doc.getParagraphs().get(paraIndex);

                // 检查段落属性中的关系引用
                try {
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP ctp = paragraph.getCTP();
                    if (ctp != null) {
                        // 检查段落中的所有run
                        for (int runIndex = 0; runIndex < ctp.getRList().size(); runIndex++) {
                            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR ctr =
                                ctp.getRList().get(runIndex);

                            // 检查run中的对象属性
                            if (CollUtil.isNotEmpty(ctr.getObjectList())) {
                                for (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTObject obj : ctr
                                    .getObjectList()) {
                                    String xmlText = obj.xmlText();
                                    log.debug("段落 {} 中的对象XML: {}", paraIndex,
                                        (xmlText.length() > 100 ? xmlText.substring(0, 100) + "..." : xmlText));

                                    // 在XML中查找所有的关系ID引用
                                    for (String relId : relIdToContentType.keySet()) {
                                        if (xmlText.contains("r:id=\"" + relId + "\"")
                                            || xmlText.contains("r:id='" + relId + "'")
                                            || xmlText.contains("RelId=\"" + relId + "\"")
                                            || xmlText.contains("RelId='" + relId + "'")) {
                                            log.info("在段落 {} 的对象中找到关系ID: {}", paraIndex, relId);
                                            relIdToPosition.put(relId, paraIndex);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("检查段落属性出错: {}", e.getMessage());
                }
            }

            for (PackageRelationship rel : relationships) {
                PackagePart part = null;
                if (rel.getTargetMode() == TargetMode.INTERNAL) {
                    try {
                        part = doc.getPackagePart().getRelatedPart(rel);
                    } catch (Exception e) {
                        log.warn("内部关系获取失败: {}", e.getMessage());
                        continue; // 如果失败就跳过这个关系
                    }
                } else {
                    // 外部链接不处理
                    log.info("跳过外部链接: {}", rel.getTargetURI());
                    continue;
                }

                String contentType = part.getContentType();
                String relId = rel.getId();

                log.debug("处理关系ID: {}, 内容类型: {}", relId, contentType);

                if (contentType.contains("oleObject") || contentType.contains("excel")
                    || contentType.contains("spreadsheet")) {

                    log.info("发现OLE Excel对象，开始处理...");

                    // 查找对象的位置
                    int position = -1;
                    if (relIdToPosition.containsKey(relId)) {
                        position = relIdToPosition.get(relId);
                        log.info("找到OLE对象的位置: 段落 {}", position);
                    } else {
                        // 尝试通过目标ID查找
                        String targetId = relIdToTargetId.get(relId);
                        boolean found = false;
                        for (Map.Entry<String, Integer> entry : relIdToPosition.entrySet()) {
                            String otherRelId = entry.getKey();
                            String otherTargetId = relIdToTargetId.get(otherRelId);
                            if (targetId != null && targetId.equals(otherTargetId)) {
                                position = entry.getValue();
                                found = true;
                                log.info("通过目标ID找到OLE对象的位置: 段落 {}", position);
                                break;
                            }
                        }

                        if (!found) {
                            // 如果仍然没有找到位置，使用默认位置
                            position = excelTables.size();
                            log.warn("未找到Excel对象的位置，使用默认位置: {}", position);
                        }
                    }

                    // 读取数据流
                    byte[] data = readInputStream(part.getInputStream());
                    log.debug("OLE对象数据大小: {} bytes", data.length);

                    // 保存为临时文件
                    Path tempFile = Files.createTempFile("excel_ole_", ".bin");
                    Files.write(tempFile, data);

                    List<ExcelJsonToMarkdownTableUtil.SheetData> excelTableJsonToMd =
                        ExcelToExcelJsonUtil.toExcelMarkdown(tempFile);
                    String collect = excelTableJsonToMd.stream().map(ExcelJsonToMarkdownTableUtil.SheetData::getData)
                        .collect(Collectors.joining("\n\n"));

                    excelTables.add(new ExcelTable(collect, position));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理OLE对象出错: {}", e.getMessage());
        }

        log.info("OLE对象提取完成，共找到 {} 个Excel表格", excelTables.size());
        return excelTables;
    }

    /**
     * 从Excel工作簿中提取图片
     * 
     * <p>
     * 提取Excel工作簿中包含的图片，保存到指定目录，并返回图片路径列表。
     * </p>
     * 
     * @param workbook Excel工作簿对象
     * @return 图片路径列表
     * @throws IOException 如果图片提取或保存过程中发生错误
     */
    public static List<ExcelImageInfo> extractExcelImagesSimple(Workbook workbook) throws IOException {
        List<ExcelImageInfo> imagePaths = new ArrayList<>();
        // 用于存储已保存图片的哈希值
        Set<String> savedImageHashes = new HashSet<>();

        log.info("开始使用增强方法提取Excel中的图片...");
        if (workbook instanceof XSSFWorkbook) {
            XSSFWorkbook xwb = (XSSFWorkbook)workbook;

            // 遍历所有工作表并使用Stream处理
            IntStream.range(0, xwb.getNumberOfSheets()).mapToObj(xwb::getSheetAt).map(sheet -> {
                try {
                    return Optional.ofNullable(sheet.createDrawingPatriarch()).map(drawing -> {
                        List<XSSFShape> shapes = drawing.getShapes();
                        log.info("工作表 {} 中找到 {} 个绘图对象", xwb.getSheetIndex(sheet), shapes.size());
                        return shapes;
                    }).orElse(Collections.emptyList());
                } catch (Exception e) {
                    log.warn("获取工作表绘图对象时出错: {}", e.getMessage());
                    return Collections.<XSSFShape>emptyList();
                }
            }).flatMap(List::stream).filter(shape -> shape instanceof XSSFPicture).map(shape -> (XSSFPicture)shape)
                .forEach(pic -> {
                    XSSFPictureData picData = pic.getPictureData();
                    XSSFClientAnchor anchor = (XSSFClientAnchor)pic.getAnchor();

                    // 获取图片位置信息
                    int row1 = anchor.getRow1();
                    int col1 = anchor.getCol1();
                    // 修复getSheet()方法不存在的问题
                    int sheetIndex = xwb.getActiveSheetIndex(); // 使用当前活动工作表索引代替

                    byte[] data = picData.getData();
                    String imageHash = calculateHashAndSave(savedImageHashes, data);

                    if (imageHash == null) {
                        log.info("跳过重复图片 (位置: 工作表{}, 行{}, 列{})", sheetIndex, row1, col1);
                        return;
                    }

                    String ext =
                        Optional.ofNullable(picData.suggestFileExtension()).filter(e -> !e.isEmpty()).orElse("png");

                    String position = String.format("sheet%d_row%d_col%d", sheetIndex, row1, col1);
                    uploadImage(imagePaths, position, ext, data);
                });
        } else if (workbook instanceof HSSFWorkbook) {
            HSSFWorkbook hwb = (HSSFWorkbook)workbook;

            // 处理HSSF工作簿
            IntStream.range(0, hwb.getNumberOfSheets()).mapToObj(hwb::getSheetAt).forEach(sheet -> {
                Optional.ofNullable(sheet.getDrawingPatriarch()).ifPresent(drawing -> drawing.getChildren().stream()
                    .filter(shape -> shape instanceof HSSFPicture).map(shape -> (HSSFPicture)shape).forEach(pic -> {
                        HSSFPictureData picData = pic.getPictureData();
                        HSSFClientAnchor anchor = (HSSFClientAnchor)pic.getAnchor();

                        // 获取图片位置信息
                        int row1 = anchor.getRow1();
                        int col1 = anchor.getCol1();
                        int sheetIndex = hwb.getSheetIndex(sheet);

                        byte[] data = picData.getData();
                        String imageHash = calculateHashAndSave(savedImageHashes, data);

                        if (imageHash == null) {
                            log.info("跳过重复图片 (位置: 工作表{}, 行{}, 列{})", sheetIndex, row1, col1);
                            return;
                        }

                        String ext =
                            Optional.ofNullable(picData.suggestFileExtension()).filter(e -> !e.isEmpty()).orElse("png");

                        String position = String.format("sheet%d_row%d_col%d", sheetIndex, row1, col1);
                        uploadImage(imagePaths, position, ext, data);

                    }));

            });
        }
        return imagePaths;
    }

    /**
     * 从输入流读取字节数组
     * 
     * @param inputStream 输入流
     * @return 字节数组
     * @throws IOException 如果读取过程中发生错误
     */
    public static byte[] readInputStream(java.io.InputStream inputStream) throws IOException {
        java.io.ByteArrayOutputStream buffer = new java.io.ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[16384];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer.toByteArray();
    }

    /**
     * 获取Excel单元格的值并转换为字符串
     * 
     * @param cell Excel单元格
     * @return 单元格值的字符串表示
     */
    public static String getCellValueAsString(Cell cell) {
        return Optional.ofNullable(cell).map(c -> {
            switch (c.getCellType()) {
                case STRING:
                    return c.getStringCellValue();
                case NUMERIC:
                    return DateUtil.isCellDateFormatted(c) ? c.getDateCellValue().toString()
                        : String.valueOf(c.getNumericCellValue());
                case BOOLEAN:
                    return String.valueOf(c.getBooleanCellValue());
                case FORMULA:
                    return c.getCellFormula();
                default:
                    return "";
            }
        }).map(value -> value.replace("\n", "<br>")).orElse("");
    }

    public static String calculateHashAndSave(Set<String> savedImageHashes, byte[] data) {
        // 计算图片内容的哈希值
        String imageHash = Image2HashProcessor.calculateImageHash(data);

        // 如果图片已经保存过，跳过
        if (savedImageHashes.contains(imageHash)) {
            return null;
        }

        // 添加到已保存集合
        savedImageHashes.add(imageHash);
        return imageHash;
    }

    private static void uploadImage(List<ExcelImageInfo> imagePaths, String position, String ext, byte[] data) {
        String fileName = "excel_ole_img_" + position + "." + ext;
        String url = FileSystemUtil.upload(data, fileName);
        imagePaths.add(new ExcelImageInfo(position, url));
    }
}