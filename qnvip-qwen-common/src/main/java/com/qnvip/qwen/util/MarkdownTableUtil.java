package com.qnvip.qwen.util;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;

/**
 * Markdown 表格格式化工具 功能： 0. 提取表格并留下占位符 1. 表名探测 2. 表头探测提取成标题 4. 移除空行
 */
public class MarkdownTableUtil {

    // 表格占位符格式，例如 {{{table0}}}
    private static final String TABLE_PLACEHOLDER = "{{{table%d}}}";
    // 正则表达式，用于匹配 Markdown 表格
    // 匹配以 | 开头和结尾的行，包含可能的表头分隔线（如 |---|---|）

    private static final Pattern TABLE_PATTERN =
        Pattern.compile("(?m)(^\\|.+\\|\\s*\\n^\\|(?:[-:| ]+)\\|\\s*\\n(?:^\\|.*\\|\\s*\\n?)*)", Pattern.MULTILINE);

    public static void main(String[] args) {
        // 示例：读取文件并格式化表格
        String inputFilePath = "C:\\Users\\<USER>\\Desktop\\【标签系统】标签工厂 副本 (1).md";
        String content = readFileContent(inputFilePath);
        String newContent = format(content);
        System.out.println(newContent);
    }

    /**
     * 格式化 Markdown 内容中的表格 1. 提取原始表格 2. 用占位符替换原始表格 3. 将每个表格转换为中间结构并重新生成格式化后的表格
     */
    public static String format(String contentOrigin) {

        String[] contents = contentOrigin.split("\n\n");

        StringBuilder stringBuilder = new StringBuilder();
        for (int contentI = 0; contentI < contents.length; contentI++) {
            String content = contents[contentI];
            // 提取所有原始表格
            List<String> extractedTables = extractTables(content);
            if (ObjectUtils.isEmpty(extractedTables)) {
                stringBuilder.append(content).append("\n\n");
                continue;
            }
            // 替换为占位符
            String newContent = replaceTablesWithPlaceholders(content);

            // 处理每个表格
            for (int i = 0; i < extractedTables.size(); i++) {
                // 将 Markdown 表格转换为中间结构 ExcelTable
                ExcelTableJson.ExcelSheet excelSheet =
                    MarkdownTableUtil.convertMarkdownTableToTableStructure(extractedTables.get(i));
                ExcelTableJson excelTableJson = new ExcelTableJson(Lists.newArrayList(excelSheet));
                // 将中间结构重新转换为格式化的 Markdown 表格
                List<ExcelJsonToMarkdownTableUtil.SheetData> sheetData =
                    ExcelJsonToMarkdownTableUtil.convertJsonToMarkdownTable(excelTableJson);
                String formattedTable = sheetData.stream().map(ExcelJsonToMarkdownTableUtil.SheetData::getData)
                    .collect(Collectors.joining("\n\n\n"));

                // 替换占位符为格式化后的表格
                newContent = newContent.replace(String.format(TABLE_PLACEHOLDER, i), formattedTable);
                stringBuilder.append(newContent).append("\n\n");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容字符串
     */
    private static String readFileContent(String filePath) {
        try {
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
            return content.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 提取所有 Markdown 表格内容
     * 
     * @param content Markdown 文本
     * @return 表格列表，每个元素为一个表格的原始字符串
     */
    public static List<String> extractTables(String content) {
        List<String> extractedTables = new ArrayList<>();
        Matcher matcher = TABLE_PATTERN.matcher(content);
        while (matcher.find()) {
            extractedTables.add(matcher.group());
        }
        return extractedTables;
    }

    /**
     * 将原始内容中的表格替换为占位符
     * 
     * @param content 原始内容
     * @return 包含占位符的新内容
     */
    private static String replaceTablesWithPlaceholders(String content) {
        StringBuilder newContent = new StringBuilder();
        Matcher matcher = TABLE_PATTERN.matcher(content);
        int lastIndex = 0;
        int tableIndex = 0;

        while (matcher.find()) {
            // 添加非表格内容
            newContent.append(content, lastIndex, matcher.start());
            // 添加占位符
            newContent.append(String.format(TABLE_PLACEHOLDER, tableIndex++));
            lastIndex = matcher.end();
        }
        // 添加剩余内容
        newContent.append(content.substring(lastIndex));
        return newContent.toString();
    }

    /**
     * 将 Markdown 表格转换为 ExcelTable 中间结构
     * 
     * @param markdownTable 原始表格字符串
     * @return ExcelTable 对象，包含表格数据
     */
    public static ExcelTableJson.ExcelSheet convertMarkdownTableToTableStructure(String markdownTable) {
        // 按行分割表格
        String[] lines = markdownTable.split("\n");
        // 查找分隔线行（包含 "-" 的行）
        int separatorIndex = -1;
        for (int i = 0; i < lines.length; i++) {
            if (lines[i].trim().startsWith("|") && lines[i].contains("-")) {
                separatorIndex = i;
                break;
            }
        }
        if (separatorIndex == -1) {
            throw new IllegalArgumentException("Invalid Markdown table format: separator line not found");
        }

        // 处理行数据：移除分隔线，清理每行内容
        List<String> rows = Arrays.stream(lines).map(String::trim).collect(Collectors.toList());
        rows.remove(separatorIndex); // 移除分隔线行

        // 解析每行数据为单元格列表
        // 处理转义的竖线
        List<String> collect = rows.stream().map(e -> e.replace("\\|", " ")).map(e -> e.replace("|", "| ").trim())
            .collect(Collectors.toList());

        List<List<String>> tableRows = collect.stream()
            // 跳过 split 后第一个空元素（因行首的 | 导致）
            .map(e -> Arrays.stream(e.split("\\|")).skip(1).map(String::trim).collect(Collectors.toList()))
            .collect(Collectors.toList());

        return new ExcelTableJson.ExcelSheet("", tableRows); // 表名暂为空
    }
}