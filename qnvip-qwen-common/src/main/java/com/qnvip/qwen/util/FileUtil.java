package com.qnvip.qwen.util;

import java.io.File;

public class FileUtil {
    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名或文件路径
     * @return 文件扩展名，如果没有扩展名则返回 null
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }
        int lastIndexOfDot = fileName.lastIndexOf('.');
        if (lastIndexOfDot == -1 || lastIndexOfDot == fileName.length() - 1) {
            return null; // 没有扩展名或以点结尾
        }
        return fileName.substring(lastIndexOfDot + 1);
    }

    /**
     * 获取文件扩展名（通过File对象）
     *
     * @param file File对象
     * @return 文件扩展名，如果没有扩展名则返回 null
     */
    public static String getFileExtension(File file) {
        if (file == null) {
            return null;
        }
        return getFileExtension(file.getName());
    }
}
