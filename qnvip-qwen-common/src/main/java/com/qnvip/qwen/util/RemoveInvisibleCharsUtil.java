package com.qnvip.qwen.util;

public class RemoveInvisibleCharsUtil {
    public static String removeNonVisibleChars(String input) {
        if (input == null) {
            return null;
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (Character.isWhitespace(c) && c != '\r' && c != '\n') {
                continue;
            }
            if (Character.isISOControl(c) && c != '\r' && c != '\n') {
                continue;
            }
            if (c == '\u200B') {
                continue;
            }
            result.append(c);
        }
        return result.toString();
    }

    public static void main(String[] args) {
        String testString = "Hello\u0009World!\nNew Line";
        String result = removeNonVisibleChars(testString);
        System.out.println(result);
    }
}
