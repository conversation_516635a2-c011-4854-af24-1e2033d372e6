package com.qnvip.qwen.util;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;

import com.qnvip.common.exception.SystemException;
import com.qnvip.qwen.enums.ErrorCodeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021-06-17
 */
@Slf4j
public class CopierUtil {

    public static <K, T> void copy(K source, T target) {
        // BeanCopier copier = BeanCopier.create(source.getClass(), target.getClass(), false);
        // copier.copy(source, target, null);
        BeanUtils.copyProperties(source, target);
    }

    public static <K, T> void copy(K source, T target, String... ignoreProperties) {
        BeanUtils.copyProperties(source, target, ignoreProperties);
    }

    public static <K, T> T copy(K source, Class<T> targetClass) {
        try {
            if (source == null) {
                return null;
            }
            T target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw SystemException.instance(ErrorCodeEnum.SYSTEM_ERR);
        }
    }

    public static <K, T> List<T> copyList(List<K> sources, Class<T> targetClass) {
        try {
            List<T> tList = new ArrayList<>();
            if (sources == null) {
                return tList;
            }
            for (K source : sources) {
                T target = targetClass.newInstance();
                BeanUtils.copyProperties(source, target);
                tList.add(target);
            }
            return tList;
        } catch (InstantiationException | IllegalAccessException e) {
            log.error(e.getMessage(), e);
            throw SystemException.instance(ErrorCodeEnum.SYSTEM_ERR);
        }
    }

}
