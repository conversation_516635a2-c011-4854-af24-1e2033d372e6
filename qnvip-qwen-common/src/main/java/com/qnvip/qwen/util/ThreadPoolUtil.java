package com.qnvip.qwen.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolUtil {
    /**
     * 创建固定大小的线程池
     * 
     * @param threadCount 线程池中的线程数量
     * @return 线程池对象
     */
    public static ExecutorService createThreadPool(int threadCount, int maxThread, int queueSize) {
        return new ThreadPoolExecutor(threadCount, maxThread, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(queueSize));
    }
}
