package com.qnvip.qwen.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * Excel拆分合并单元格的工具类
 */
public class ExcelUnmergeCellsUtil {
    public static void main(String[] args) throws IOException {
        String inputFilePath = "C:\\Users\\<USER>\\Desktop\\任务跟进看板.xlsx";
        String outputFilePath = "C:\\Users\\<USER>\\Desktop\\任务跟进看板2.xlsx";

        unmergeAndFillExcel(inputFilePath, outputFilePath);
    }

    /**
     * 解除单元格合并
     * 
     * @param inputFilePath
     * @param outputFilePath
     */
    public static void unmergeAndFillExcel(String inputFilePath, String outputFilePath) {
        File inputFileFile = new File(inputFilePath);
        File outputPutFile = new File(outputFilePath);
        unmergeAndFillExcel(inputFileFile, outputPutFile);
    }

    /**
     * 解除单元格合并
     * 
     * @param inputFileFile
     * @param outputPutFile
     */
    public static void unmergeAndFillExcel(File inputFileFile, File outputPutFile) {
        try (Workbook workbook = WorkbookFactory.create(inputFileFile)) {
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

                // 倒序遍历合并区域
                for (int j = mergedRegions.size() - 1; j >= 0; j--) {
                    CellRangeAddress region = mergedRegions.get(j);
                    unmergeAndFillCells(sheet, region);
                    sheet.removeMergedRegion(j);
                }
            }

            // 保存修改后的工作簿
            try (FileOutputStream out = new FileOutputStream(outputPutFile)) {
                workbook.write(out);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void unmergeAndFillCells(Sheet sheet, CellRangeAddress region) {
        int firstRow = region.getFirstRow();
        int lastRow = region.getLastRow();
        int firstCol = region.getFirstColumn();
        int lastCol = region.getLastColumn();

        // 获取合并区域左上角单元格的值和样式
        Row firstRowObj = sheet.getRow(firstRow);
        if (firstRowObj == null) {
            return;
        }

        Cell firstCell = firstRowObj.getCell(firstCol);
        if (firstCell == null) {
            return;
        }

        // 获取单元格值和类型
        Object value = getCellValue(firstCell);
        CellStyle style = firstCell.getCellStyle();

        // 填充所有拆分后的单元格
        for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                row = sheet.createRow(rowNum);
            }

            for (int colNum = firstCol; colNum <= lastCol; colNum++) {
                Cell cell = row.getCell(colNum);
                if (cell == null) {
                    cell = row.createCell(colNum);
                }

                // 设置值和样式
                setCellValue(cell, value, firstCell.getCellType());
                cell.setCellStyle(style);
            }
        }
    }

    private static Object getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return cell.getNumericCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private static void setCellValue(Cell cell, Object value, CellType cellType) {
        switch (cellType) {
            case STRING:
                cell.setCellValue((String)value);
                break;
            case NUMERIC:
                cell.setCellValue((Double)value);
                break;
            case BOOLEAN:
                cell.setCellValue((Boolean)value);
                break;
            case FORMULA:
                cell.setCellFormula((String)value);
                break;
            default:
                // 其他类型无需处理
        }
    }
}
