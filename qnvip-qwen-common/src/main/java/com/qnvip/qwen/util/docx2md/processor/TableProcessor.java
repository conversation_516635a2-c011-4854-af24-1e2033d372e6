package com.qnvip.qwen.util.docx2md.processor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import com.qnvip.qwen.util.FileSystemUtil;
import com.qnvip.qwen.util.docx2md.vo.TableStructure;

import lombok.extern.slf4j.Slf4j;

/**
 * Word文档表格处理工具类
 * 
 * <p>
 * 此工具类用于处理Word文档中的表格内容，将其转换为Markdown表格格式。 包括处理表格行、列、单元格合并等。
 * </p>
 */
@Slf4j
public class TableProcessor {

    /**
     * 图片保存的基础路径
     */
    private static String imageBasePath = "images/";


    /**
     * 将Word表格转换为Markdown表格
     * 
     * <p>
     * 处理Word表格的行、列、单元格内容和合并单元格等，转换为Markdown表格格式。
     * </p>
     * 
     * @param table Word表格对象
     * @return 转换后的Markdown表格内容
     */
    public static String tableToMarkdown(XWPFTable table) {
        // 先分析表格结构以识别合并单元格
        int rowCount = table.getRows().size();
        int colCount = table.getRow(0).getTableCells().size();

        // 计算实际的最大列数（考虑水平合并）
        colCount = calculateTableColumnCount(table, rowCount, colCount);

        // 创建表格数据结构
        TableStructure tableStructure = initializeTableStructure(rowCount, colCount);

        // 解析表格结构，识别合并单元格
        parseTableStructure(table, rowCount, colCount, tableStructure);

        // 填充垂直合并单元格的内容
        fillVerticalMergedCells(rowCount, colCount, tableStructure.getVerticalMergeContents(),
            tableStructure.getCellContents());

        // 生成Markdown表格
        return generateMarkdownTable(rowCount, colCount, tableStructure);
    }

    /**
     * 计算表格的列数
     * 
     * @param table 表格对象
     * @param rowCount 行数
     * @param initialColCount 初始列数
     * @return 计算后的列数
     */
    private static int calculateTableColumnCount(XWPFTable table, int rowCount, int initialColCount) {
        int colCount = initialColCount;
        for (int i = 0; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            int cellsInRow = row.getTableCells().size();
            for (int j = 0; j < cellsInRow; j++) {
                XWPFTableCell cell = row.getCell(j);
                try {
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr != null && tcPr.isSetGridSpan()) {
                        int gridSpan = tcPr.getGridSpan().getVal().intValue();
                        int totalCells = j + gridSpan;
                        colCount = Math.max(colCount, totalCells);
                    }
                } catch (Exception e) {
                    log.debug("计算表格列数时出错: {}", e.getMessage());
                }
            }
        }
        return colCount;
    }

    /**
     * 初始化表格数据结构
     * 
     * @param rowCount 行数
     * @param colCount 列数
     * @return 表格结构对象
     */
    private static TableStructure initializeTableStructure(int rowCount, int colCount) {
        TableStructure structure = new TableStructure();

        // 创建单元格内容的二维数组
        String[][] cellContents = new String[rowCount][colCount];
        // 存储单元格的行跨度和列跨度信息
        int[][] rowSpans = new int[rowCount][colCount]; // 垂直方向跨越的行数
        int[][] colSpans = new int[rowCount][colCount]; // 水平方向跨越的列数
        // 存储哪些单元格被合并(被其他单元格覆盖)
        boolean[][] mergedCells = new boolean[rowCount][colCount];
        // 存储垂直合并单元格的内容，用于后续填充
        String[][] verticalMergeContents = new String[rowCount][colCount];

        // 初始化数组
        for (int i = 0; i < rowCount; i++) {
            for (int j = 0; j < colCount; j++) {
                cellContents[i][j] = "";
                rowSpans[i][j] = 1;
                colSpans[i][j] = 1;
                mergedCells[i][j] = false;
                verticalMergeContents[i][j] = null;
            }
        }

        structure.setCellContents(cellContents);
        structure.setRowSpans(rowSpans);
        structure.setColSpans(colSpans);
        structure.setMergedCells(mergedCells);
        structure.setVerticalMergeContents(verticalMergeContents);

        return structure;
    }

    /**
     * 解析表格结构
     * 
     * @param table 表格对象
     * @param rowCount 行数
     * @param colCount 列数
     * @param structure 表格结构对象
     */
    private static void parseTableStructure(XWPFTable table, int rowCount, int colCount, TableStructure structure) {
        for (int rowIdx = 0; rowIdx < rowCount; rowIdx++) {
            XWPFTableRow row = table.getRow(rowIdx);
            int cellsInRow = row.getTableCells().size();
            int colIdx = 0; // 实际列索引，考虑列合并

            for (int j = 0; j < cellsInRow; j++) {
                // 跳过已经被标记为合并的单元格
                while (colIdx < colCount && structure.getMergedCells()[rowIdx][colIdx]) {
                    colIdx++;
                }

                if (colIdx >= colCount) {
                    break;
                }

                XWPFTableCell cell = row.getCell(j);

                // 获取单元格内容，包括文本和图片
                String cellContent = processCellContentWithImages(cell);
                structure.getCellContents()[rowIdx][colIdx] = cellContent;

                // 处理合并单元格
                processMergedCell(cell, rowIdx, colIdx, structure, table, j);

                // 移动到下一列，考虑列跨度
                colIdx += structure.getColSpans()[rowIdx][colIdx];
            }
        }
    }

    /**
     * 处理合并单元格
     * 
     * @param cell 单元格对象
     * @param rowIdx 行索引
     * @param colIdx 列索引
     * @param structure 表格结构对象
     * @param table 表格对象
     * @param cellIndex 单元格在行中的索引
     */
    private static void processMergedCell(XWPFTableCell cell, int rowIdx, int colIdx, TableStructure structure,
        XWPFTable table, int cellIndex) {
        try {
            // 检查是否是水平合并单元格
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc ctTc = cell.getCTTc();
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr tcPr = ctTc.getTcPr();

            if (tcPr != null) {
                // 处理水平合并
                processHorizontalMerge(tcPr, rowIdx, colIdx, structure);

                // 处理垂直合并
                processVerticalMerge(tcPr, rowIdx, colIdx, structure, table, cellIndex);
            }
        } catch (Exception e) {
            log.debug("处理合并单元格时出错: {}", e.getMessage());
        }
    }

    /**
     * 处理水平合并
     * 
     * @param tcPr 单元格属性
     * @param rowIdx 行索引
     * @param colIdx 列索引
     * @param structure 表格结构对象
     */
    private static void processHorizontalMerge(org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr tcPr,
        int rowIdx, int colIdx, TableStructure structure) {
        if (tcPr.isSetGridSpan()) {
            int gridSpan = tcPr.getGridSpan().getVal().intValue();
            structure.getColSpans()[rowIdx][colIdx] = gridSpan;

            // 标记被合并的单元格
            for (int k = 1; k < gridSpan && colIdx + k < structure.getColSpans()[0].length; k++) {
                structure.getMergedCells()[rowIdx][colIdx + k] = true;
            }
        }
    }

    /**
     * 处理垂直合并
     * 
     * @param tcPr 单元格属性
     * @param rowIdx 行索引
     * @param colIdx 列索引
     * @param structure 表格结构对象
     * @param table 表格对象
     * @param cellIndex 单元格在行中的索引
     */
    private static void processVerticalMerge(org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr tcPr,
        int rowIdx, int colIdx, TableStructure structure, XWPFTable table, int cellIndex) {
        if (tcPr.isSetVMerge()) {
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVMerge vMerge = tcPr.getVMerge();
            if (vMerge.getVal() == null || vMerge.getVal().toString().equals("restart")) {
                // 开始垂直合并
                processVerticalMergeStart(rowIdx, colIdx, structure, table, cellIndex);
            }
        }
    }

    /**
     * 处理垂直合并的起始单元格
     * 
     * @param rowIdx 行索引
     * @param colIdx 列索引
     * @param structure 表格结构对象
     * @param table 表格对象
     * @param cellIndex 单元格在行中的索引
     */
    private static void processVerticalMergeStart(int rowIdx, int colIdx, TableStructure structure, XWPFTable table,
        int cellIndex) {
        int spanCount = 1;
        String cellText = structure.getCellContents()[rowIdx][colIdx];

        // 保存垂直合并的起始单元格内容
        structure.getVerticalMergeContents()[rowIdx][colIdx] = cellText;

        // 计算这个单元格向下合并了多少行
        for (int i = rowIdx + 1; i < structure.getCellContents().length; i++) {
            try {
                XWPFTableRow nextRow = table.getRow(i);
                // 确保索引有效
                if (cellIndex < nextRow.getTableCells().size()) {
                    XWPFTableCell nextCell = nextRow.getCell(cellIndex);
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr nextTcPr =
                        nextCell.getCTTc().getTcPr();

                    if (nextTcPr != null && nextTcPr.isSetVMerge() && (nextTcPr.getVMerge().getVal() == null
                        || !nextTcPr.getVMerge().getVal().toString().equals("restart"))) {
                        spanCount++;
                        // 记录它们需要使用上方单元格的内容
                        structure.getVerticalMergeContents()[i][colIdx] = cellText;
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } catch (Exception e) {
                log.debug("处理垂直合并单元格时出错: {}", e.getMessage());
                break;
            }
        }

        structure.getRowSpans()[rowIdx][colIdx] = spanCount;
    }

    /**
     * 填充垂直合并单元格的内容
     * 
     * @param rowCount 行数
     * @param colCount 列数
     * @param verticalMergeContents 垂直合并内容数组
     * @param cellContents 单元格内容数组
     */
    private static void fillVerticalMergedCells(int rowCount, int colCount, String[][] verticalMergeContents,
        String[][] cellContents) {
        for (int rowIdx = 0; rowIdx < rowCount; rowIdx++) {
            for (int colIdx = 0; colIdx < colCount; colIdx++) {
                if (verticalMergeContents[rowIdx][colIdx] != null && !verticalMergeContents[rowIdx][colIdx].isEmpty()) {
                    cellContents[rowIdx][colIdx] = verticalMergeContents[rowIdx][colIdx];
                }
            }
        }
    }

    /**
     * 生成Markdown表格
     * 
     * @param rowCount 行数
     * @param colCount 列数
     * @param structure 表格结构对象
     * @return Markdown表格内容
     */
    private static String generateMarkdownTable(int rowCount, int colCount, TableStructure structure) {
        StringBuilder markdownTable = new StringBuilder();

        // 处理表头行
        List<String> headers = generateTableHeaders(colCount, structure);

        // 添加表头行
        markdownTable.append("| ").append(String.join(" | ", headers)).append(" |").append("\n");

        // 添加分隔行
        markdownTable.append("| ").append(headers.stream().map(h -> "---").collect(Collectors.joining(" | ")))
            .append(" |").append("\n");

        // 处理数据行
        generateTableRows(rowCount, colCount, structure, markdownTable);

        return markdownTable.toString();
    }

    /**
     * 生成表格头部
     * 
     * @param colCount 列数
     * @param structure 表格结构对象
     * @return 表头列表
     */
    private static List<String> generateTableHeaders(int colCount, TableStructure structure) {
        List<String> headers = new ArrayList<>();
        for (int colIdx = 0; colIdx < colCount; colIdx++) {
            if (!structure.getMergedCells()[0][colIdx]) {
                // 如果是横向合并的起始单元格，需要重复内容来保持Markdown表格结构
                int span = structure.getColSpans()[0][colIdx];
                if (span > 1) {
                    for (int i = 0; i < span; i++) {
                        headers.add(structure.getCellContents()[0][colIdx]);
                    }
                } else {
                    headers.add(structure.getCellContents()[0][colIdx]);
                }
            }
        }
        return headers;
    }

    /**
     * 生成表格行
     * 
     * @param rowCount 行数
     * @param colCount 列数
     * @param structure 表格结构对象
     * @param markdownTable 表格构建器
     */
    private static void generateTableRows(int rowCount, int colCount, TableStructure structure,
        StringBuilder markdownTable) {
        for (int rowIdx = 1; rowIdx < rowCount; rowIdx++) {
            List<String> rowData = new ArrayList<>();

            for (int colIdx = 0; colIdx < colCount; colIdx++) {
                if (!structure.getMergedCells()[rowIdx][colIdx]) {
                    // 处理水平合并
                    int span = structure.getColSpans()[rowIdx][colIdx];
                    if (span > 1) {
                        // 对于合并的单元格，在Markdown中重复内容以保持表格结构
                        for (int i = 0; i < span; i++) {
                            rowData.add(structure.getCellContents()[rowIdx][colIdx]);
                        }
                    } else {
                        rowData.add(structure.getCellContents()[rowIdx][colIdx]);
                    }
                }
            }

            if (!rowData.isEmpty()) {
                markdownTable.append("| ").append(String.join(" | ", rowData)).append(" |").append("\n");
            }
        }
    }

    /**
     * 处理表格单元格内容，包括文本和图片
     * 
     * @param cell 单元格对象
     * @return 处理后的内容（文本或图片的Markdown表示）
     */
    private static String processCellContentWithImages(XWPFTableCell cell) {
        StringBuilder cellContent = new StringBuilder();

        // 处理所有段落
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            // 检查段落中是否有图片
            String imageContent = extractImagesFromParagraph(paragraph);
            if (!imageContent.isEmpty()) {
                cellContent.append(imageContent);
            } else {
                // 如果没有图片，则获取文本内容
                cellContent.append(paragraph.getText());
            }

            // 如果不是最后一个段落，添加换行
            if (paragraphs.indexOf(paragraph) < paragraphs.size() - 1) {
                cellContent.append("<br>");
            }
        }

        return processTableCellContent(cellContent.toString());
    }

    /**
     * 从段落中提取图片
     * 
     * @param paragraph 段落对象
     * @return 图片的Markdown表示，如果没有图片则返回空字符串
     */
    private static String extractImagesFromParagraph(XWPFParagraph paragraph) {
        StringBuilder imageMarkdown = new StringBuilder();

        // 遍历段落中的所有Run
        for (XWPFRun run : paragraph.getRuns()) {
            try {
                // 检查Run中是否包含图片
                if (run.getEmbeddedPictures() != null && !run.getEmbeddedPictures().isEmpty()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFPicture picture : run.getEmbeddedPictures()) {
                        String imagePath = saveImageAndGetPath(picture);
                        if (imagePath != null) {
                            // 添加Markdown格式的图片引用
                            imageMarkdown.append("![image](").append(imagePath).append(")");
                        }
                    }
                }
            } catch (Exception e) {
                log.debug("提取段落中的图片时出错: {}", e.getMessage());
            }
        }

        return imageMarkdown.toString();
    }

    /**
     * 保存图片并返回保存路径
     * 
     * @param picture Word中的图片对象
     * @return 保存后的图片路径，如果保存失败则返回null
     */
    private static String saveImageAndGetPath(org.apache.poi.xwpf.usermodel.XWPFPicture picture) {
        // 获取图片数据
        byte[] data = picture.getPictureData().getData();

        // 创建唯一的文件名
        String imageId = UUID.randomUUID().toString();
        String imageName = "image_" + imageId + "." + picture.getPictureData().suggestFileExtension();

        return FileSystemUtil.upload(data, imageName);
    }

    /**
     * 处理表格单元格内容
     * 
     * <p>
     * 处理表格单元格内容，替换换行符并转义特殊字符。
     * </p>
     *
     * @param cellText 单元格原始文本
     * @return 处理后的文本
     */
    public static String processTableCellContent(String cellText) {
        if (cellText == null) {
            return "";
        }

        // 移除前后空格
        String trimmedText = cellText.trim();

        // 替换表格中的竖线字符，避免破坏Markdown表格结构
        trimmedText = trimmedText.replace("|", "\\|");

        // 替换换行符为HTML标签<br>
        trimmedText = trimmedText.replace("\n", "<br>");
        trimmedText = trimmedText.replace("\r\n", "<br>");
        trimmedText = trimmedText.replace("\r", "<br>");

        return trimmedText;
    }

    /**
     * 转义Markdown表格中的特殊字符
     * 
     * @param content 原始内容
     * @return 转义后的内容
     */
    public static String escapeMarkdownTableCell(String content) {
        // 转义Markdown表格中的特殊字符
        return content != null ? content.replace("|", "\\|").replace("\r", "").trim() : "";
    }
}