package com.qnvip.qwen.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月25日 16:25:00
 */
public class VideoSplitUtil {

    // 定义块大小（单位：字节）
    private static final int CHUNK_SIZE = 15 * 1024 * 1024; // 1MB

    public static void main(String[] args) {
        String inputFilePath = "C:\\Users\\<USER>\\Desktop\\B3B4后续工作会议纪要：多方面规划安排 .mp3"; // 输入音频文件路径
        String outputDir = "C:\\Users\\<USER>\\Desktop\\"; // 输出分块文件目录

        try {
            splitAudioFile(inputFilePath, outputDir);
            System.out.println("音频文件分块处理完成！");
        } catch (IOException e) {
            System.err.println("处理音频文件时出错: " + e.getMessage());
        }
    }

    /**
     * 将音频文件分块处理
     *
     * @param inputFilePath 输入文件路径
     * @param outputDir 输出分块文件目录
     * @throws IOException 如果文件操作失败
     */
    public static void splitAudioFile(String inputFilePath, String outputDir) throws IOException {
        // 创建输出目录
        Path outputPath = Paths.get(outputDir);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
        }

        // 读取输入文件
        try (InputStream inputStream = new FileInputStream(inputFilePath)) {
            byte[] buffer = new byte[CHUNK_SIZE];
            int bytesRead;
            int chunkIndex = 0;

            // 分块读取并写入文件
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                String chunkFileName = outputDir + File.separator + "chunk_" + chunkIndex + ".mp3";
                try (OutputStream outputStream = new FileOutputStream(chunkFileName)) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                chunkIndex++;
            }
        }
    }

    /**
     * 合并分块文件（可选）
     *
     * @param inputDir 分块文件目录
     * @param outputFilePath 合并后的输出文件路径
     * @throws IOException 如果文件操作失败
     */
    public static void mergeAudioFiles(String inputDir, String outputFilePath) throws IOException {
        try (OutputStream outputStream = new FileOutputStream(outputFilePath)) {
            int chunkIndex = 0;
            while (true) {
                String chunkFileName = inputDir + File.separator + "chunk_" + chunkIndex + ".mp3";
                File chunkFile = new File(chunkFileName);
                if (!chunkFile.exists()) {
                    break;
                }

                try (InputStream inputStream = new FileInputStream(chunkFile)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }
                chunkIndex++;
            }
        }
    }
}
