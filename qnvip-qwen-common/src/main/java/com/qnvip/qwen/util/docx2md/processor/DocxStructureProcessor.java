package com.qnvip.qwen.util.docx2md.processor;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.xwpf.usermodel.BodyElementType;
import org.apache.poi.xwpf.usermodel.IBodyElement;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;

import com.qnvip.qwen.util.docx2md.vo.BodyElementPosition;
import com.qnvip.qwen.util.docx2md.vo.ExcelTable;
import com.qnvip.qwen.util.docx2md.vo.ImageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * Word文档结构处理工具类
 * 
 * <p>
 * 此工具类用于处理Word文档的整体结构，按照正确的顺序组织段落、表格和Excel对象。
 * </p>
 */
@Slf4j
public class DocxStructureProcessor {

    /**
     * 处理Word文档内容，将段落、表格和Excel对象转换为Markdown格式
     * 
     * <p>
     * 此方法分析文档结构，按照原始文档的顺序处理段落、表格和Excel对象，并生成相应的Markdown内容。
     * </p>
     * 
     * @param doc Word文档对象
     * @param imagePaths 图片信息列表
     * @param excelTables Excel表格列表
     * @return 转换后的Markdown内容列表
     * @throws Exception 如果处理过程中发生错误
     */
    public static List<String> processDocument(XWPFDocument doc, List<ImageInfo> imagePaths,
        List<ExcelTable> excelTables) throws Exception {
        List<String> markdownContent = new ArrayList<>();

        // 预先按位置排序Excel表格，确保按正确顺序插入
        List<ExcelTable> sortedExcelTables = new ArrayList<>(excelTables);
        sortedExcelTables.sort(Comparator.comparingInt(ExcelTable::getPosition));

        // 构建段落映射，保持原始段落的索引与内容的对应关系
        final Map<Integer, String> paragraphContents = new HashMap<>();

        // 构建表格映射，记录表格在文档中的位置
        final Map<Integer, String> tableContents = new HashMap<>();

        // 首先分析文档结构，确定段落和表格的顺序
        int bodyElementIndex = 0;
        List<BodyElementPosition> documentStructure = new ArrayList<>();

        // 遍历文档中的所有元素，记录它们的类型和位置
        for (IBodyElement element : doc.getBodyElements()) {
            if (element.getElementType() == BodyElementType.PARAGRAPH) {
                documentStructure.add(new BodyElementPosition(BodyElementType.PARAGRAPH, bodyElementIndex));
            } else if (element.getElementType() == BodyElementType.TABLE) {
                documentStructure.add(new BodyElementPosition(BodyElementType.TABLE, bodyElementIndex));
            }
            bodyElementIndex++;
        }

        log.info("文档结构分析：共 {} 个元素（段落和表格）", documentStructure.size());

        // 先处理所有段落，构建映射但不立即添加到最终结果
        int paragraphIndex = 0;
        for (XWPFParagraph paragraph : doc.getParagraphs()) {
            String content = ParagraphProcessor.processParagraph(paragraph, imagePaths);
            // 过滤掉空内容和null
            if (content != null && !content.trim().isEmpty() && !content.trim().equals("null")) {
                paragraphContents.put(paragraphIndex, content);
            }
            paragraphIndex++;
        }

        // 处理所有表格，构建映射但不立即添加到最终结果
        int tableIndex = 0;
        for (XWPFTable table : doc.getTables()) {
            String tableContent = TableProcessor.tableToMarkdown(table);
            if (tableContent != null && !tableContent.trim().isEmpty() && !tableContent.trim().equals("null")) {
                tableContents.put(tableIndex, tableContent);
            }
            tableIndex++;
        }

        // 根据文档结构，按顺序构建最终内容
        int currentParagraphIndex = 0;
        int currentTableIndex = 0;

        for (BodyElementPosition elementPosition : documentStructure) {
            if (elementPosition.getType() == BodyElementType.PARAGRAPH) {
                // 检查是否有Excel表格需要在此位置插入
                for (ExcelTable excelTable : sortedExcelTables) {
                    if (excelTable.getPosition() == currentParagraphIndex) {
                        String content = excelTable.getTable();
                        if (content != null && !content.trim().isEmpty() && !content.trim().equals("null")) {
                            markdownContent.add(content);
                        }
                    }
                }

                // 添加段落内容（如果存在）
                if (paragraphContents.containsKey(currentParagraphIndex)) {
                    markdownContent.add(paragraphContents.get(currentParagraphIndex));
                }
                currentParagraphIndex++;
            } else if (elementPosition.getType() == BodyElementType.TABLE) {
                // 添加表格内容（如果存在）
                if (tableContents.containsKey(currentTableIndex)) {
                    markdownContent.add(tableContents.get(currentTableIndex));
                }
                currentTableIndex++;
            }
        }

        return markdownContent;
    }
}