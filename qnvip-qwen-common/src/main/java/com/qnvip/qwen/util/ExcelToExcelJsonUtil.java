package com.qnvip.qwen.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFShape;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.alibaba.fastjson.JSON;

import cn.hutool.crypto.digest.MD5;

public class ExcelToExcelJsonUtil {
    private static final String IMG_MD_WRAP = "![](%s)";

    public static void main(String[] args) throws Exception {
        ExcelTableJson tables = readExcel("C:\\Users\\<USER>\\Desktop\\Apple授权小程序公域推广一览表.xlsx");
        System.out.println(JSON.toJSONString(tables));
    }

    // 假定这个方法会把本地文件（或二进制流）上传并返回一个可访问的 URL
    public static String fileUpload(String filePath) {
        return "https://todo待处理/" + new File(filePath).getName();
    }

    /**
     * 接收图片字节数组并上传，返回 URL
     */
    private static String uploadPicture(byte[] data, String ext) {
        // 将图片写入临时文件
        String fileChecksum = MD5.create().digestHex(data);
        String filename = fileChecksum + "." + ext;

        String url = FileSystemUtil.upload(data, filename);
        return String.format(IMG_MD_WRAP, url);
    }

    public static List<ExcelJsonToMarkdownTableUtil.SheetData> toExcelMarkdown(Path localFile) {
        return ExcelJsonToMarkdownTableUtil.convertJsonToMarkdownTable(getExcelTableJson(localFile));
    }

    public static ExcelTableJson getExcelTableJson(Path localFile) {
        Path unmergeDir = localFile.getParent().resolve("unmerge");
        if (!Files.exists(unmergeDir)) {
            try {
                Files.createDirectory(unmergeDir);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        Path targetFilePath = unmergeDir.resolve(localFile.getFileName());
        ExcelUnmergeCellsUtil.unmergeAndFillExcel(localFile.toFile(), targetFilePath.toFile());

        unmergeDir.toFile().deleteOnExit();

        return ExcelToExcelJsonUtil.readExcel(targetFilePath.toFile().toString());
    }

    public static ExcelTableJson readExcel(String filePath) {
        ExcelTableJson tables = new ExcelTableJson(new ArrayList<>());

        try (FileInputStream fis = new FileInputStream(filePath); XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            String fileName = new File(filePath).getName();
            tables.setFileName(fileName);

            // 遍历每个 sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                XSSFSheet sheet = workbook.getSheetAt(i);
                String sheetName = sheet.getSheetName();

                // 1) 先扫描图片并记录它们的位置
                Map<String, String> imgMap = new HashMap<>();
                XSSFDrawing drawing = sheet.getDrawingPatriarch();
                if (drawing != null) {
                    for (XSSFShape shape : drawing.getShapes()) {
                        if (shape instanceof XSSFPicture) {
                            XSSFPicture pic = (XSSFPicture)shape;
                            XSSFClientAnchor anchor = pic.getPreferredSize();
                            int row = anchor.getRow1();
                            int col = anchor.getCol1();
                            XSSFPictureData pdata = pic.getPictureData();
                            String ext = pdata.suggestFileExtension();
                            String url = uploadPicture(pdata.getData(), ext);
                            imgMap.put(row + "_" + col, url);
                        }
                    }
                }

                // 2) 遍历单元格，优先取图片 URL，否则按类型读取文本或超链接
                List<List<String>> data = new ArrayList<>();
                FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

                for (Row row : sheet) {
                    List<String> rowData = new ArrayList<>();
                    int r = row.getRowNum();

                    // 假设 sheet 的列数不定，用到哪个列就遍历到哪个列
                    int lastCol = row.getLastCellNum();
                    for (int c = 0; c < lastCol; c++) {
                        Cell cell = row.getCell(c, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        String key = r + "_" + c;

                        // 如果该坐标有图片，直接放 URL
                        if (imgMap.containsKey(key)) {
                            rowData.add(imgMap.get(key));
                            continue;
                        }

                        // 处理超链接（图片或其它文件）
                        Hyperlink link = cell.getHyperlink();
                        if (link != null) {
                            rowData.add(fileUpload(link.getAddress()));
                            continue;
                        }

                        // 普通文本、数字、布尔、公式
                        String cellValue;
                        switch (cell.getCellType()) {
                            case STRING:
                                cellValue = cell.getStringCellValue();
                                break;
                            case NUMERIC:
                                if (DateUtil.isCellDateFormatted(cell)) {
                                    Date date = cell.getDateCellValue();
                                    cellValue = SmartDateFormatUtil.format(date);
                                } else {
                                    cellValue = Double.toString(cell.getNumericCellValue());
                                }
                                break;
                            case BOOLEAN:
                                cellValue = Boolean.toString(cell.getBooleanCellValue());
                                break;
                            case FORMULA:
                                CellValue cv = evaluator.evaluate(cell);
                                cellValue = cv.formatAsString();
                                break;
                            default:
                                cellValue = "";
                        }
                        rowData.add(cellValue);
                    }
                    data.add(rowData);
                }

                tables.getData().add(new ExcelTableJson.ExcelSheet(sheetName, data));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return tables;
    }

}
