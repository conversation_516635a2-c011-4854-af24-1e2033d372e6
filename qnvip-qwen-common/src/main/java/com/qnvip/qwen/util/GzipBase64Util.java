package com.qnvip.qwen.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月14日 16:15:00
 */
public class GzipBase64Util {
    public static void main(String[] args) {
        String originalString = "Hello, this is a string that will be compressed and encoded in Base64.";

        try {
            // 压缩并编码
            String compressedBase64 = compressAndEncode(originalString);
            System.out.println("Compressed and Base64 Encoded String: ");
            System.out.println(compressedBase64);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String compressAndEncode(String str) throws IOException {
        // 将字符串转换为字节数组
        byte[] bytes = str.getBytes("UTF-8");

        // 使用 ByteArrayOutputStream 来存放压缩后的数据
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            // 写入字节到 GZIPOutputStream
            gzipOutputStream.write(bytes);
        }

        // 获取压缩后的字节数组
        byte[] compressedBytes = byteArrayOutputStream.toByteArray();

        // 使用 Base64 编码压缩后的字节数组
        return Base64.getEncoder().encodeToString(compressedBytes);
    }
}
