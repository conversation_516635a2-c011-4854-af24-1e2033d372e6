package com.qnvip.qwen.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PhoneNumberMaskingUtil {
    public static String maskPhoneNumbers(String text) {
        // 修改后的正则表达式，确保匹配独立的11位数字
        Pattern pattern = Pattern.compile("(?<!\\d)(1\\d{10})(?!\\d)");
        Matcher matcher = pattern.matcher(text);

        StringBuilder result = new StringBuilder();
        int lastEnd = 0;

        while (matcher.find()) {
            String elevenDigits = matcher.group(1);
            // 直接使用正则表达式中的验证，省略额外检查
            String maskedPhoneNumber = elevenDigits.substring(0, 3) + "****" + elevenDigits.substring(7);
            result.append(text, lastEnd, matcher.start());
            result.append(maskedPhoneNumber);
            lastEnd = matcher.end();
        }
        result.append(text.substring(lastEnd));
        return result.toString();
    }

    public static void main(String[] args) {
        String text = "这是一段包含手机号13800138000的文本，还有其他数字 12323456789012 ";
        String maskedText = maskPhoneNumbers(text);
        System.out.println(maskedText);
        // 输出：这是一段包含手机号 138****8000 的文本，还有其他数字 123456789012。
    }
}