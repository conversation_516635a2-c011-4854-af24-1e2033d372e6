package com.qnvip.qwen.util.docx2md.processor;

import java.math.BigInteger;
import java.util.List;

import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFPictureData;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import com.qnvip.qwen.util.docx2md.vo.ImageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * Word文档段落处理工具类
 * 
 * <p>
 * 此工具类用于处理Word文档中的段落内容，将其转换为Markdown格式。 包括处理标题、列表、超链接、文本格式和图片等。
 * </p>
 */
@Slf4j
public class ParagraphProcessor {

    // 用于跟踪上一个段落的numId
    private static BigInteger lastNumId = null;

    /**
     * 处理段落，将其转换为Markdown格式
     * 
     * <p>
     * 处理段落的文本内容、格式、标题级别和图片等，转换为Markdown格式。
     * </p>
     * 
     * @param paragraph Word段落对象
     * @param imagePaths 图片信息列表
     * @return 转换后的Markdown段落内容
     */
    public static String processParagraph(XWPFParagraph paragraph, List<ImageInfo> imagePaths) {
        StringBuilder text = new StringBuilder();

        // 检查是否是标题段落
        boolean isHeading = isHeadingParagraph(paragraph);
        int headingLevel = getHeadingLevel(paragraph);

        if (isHeading) {
            return processHeadingParagraph(paragraph, headingLevel);
        }

        // 检查段落是否包含超链接
        String hyperlinkContent = processHyperlinkInParagraph(paragraph);
        if (hyperlinkContent != null) {
            return hyperlinkContent;
        }

        // 处理段落对齐方式
        applyParagraphAlignment(paragraph, text);

        // 处理段落内容
        processRunsInParagraph(paragraph, text, imagePaths);

        String result = text.toString().trim();
        // 修复连续的加粗标记，如 ********、****等情况
        result = fixConsecutiveBoldMarkers(result);

        // 移除空的居中标签
        result = result.replaceAll("<div align=\"center\"></div>", "");

        return result.isEmpty() ? null : result;
    }


    /**
     * 处理标题段落
     * 
     * @param paragraph 段落对象
     * @param headingLevel 标题级别
     * @return 处理后的Markdown标题内容
     */
    private static String processHeadingParagraph(XWPFParagraph paragraph, int headingLevel) {
        StringBuilder text = new StringBuilder();
        // 如果是标题，根据级别添加相应数量的#
        for (int i = 0; i < headingLevel; i++) {
            text.append("#");
        }
        text.append(" ");

        // 添加标题内容（不需要处理其他格式）
        text.append(paragraph.getText().trim());
        return text.toString();
    }

    /**
     * 处理段落中的超链接
     * 
     * @param paragraph 段落对象
     * @return 如果段落包含超链接，则返回处理后的超链接内容，否则返回null
     */
    private static String processHyperlinkInParagraph(XWPFParagraph paragraph) {
        try {
            // 使用CTHyperlink检查段落中的超链接
            if (paragraph.getCTP() == null || paragraph.getCTP().getHyperlinkList() == null
                || paragraph.getCTP().getHyperlinkList().isEmpty()) {
                return null;
            }

            for (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHyperlink ctHyperlink : paragraph.getCTP()
                .getHyperlinkList()) {
                if (ctHyperlink.getId() == null) {
                    continue;
                }

                // 获取超链接关系ID
                String hyperlinkId = ctHyperlink.getId();

                // 获取超链接关系
                PackageRelationship relationship = paragraph.getDocument().getPackagePart()
                    .getRelationshipsByType(
                        "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink")
                    .getRelationshipByID(hyperlinkId);

                if (relationship != null) {
                    return processHyperlinkRelationship(ctHyperlink, relationship);
                }
            }
        } catch (Exception e) {
            log.warn("处理段落超链接时出错: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 处理超链接关系
     * 
     * @param ctHyperlink 超链接元素
     * @param relationship 超链接关系
     * @return 处理后的超链接文本
     */
    private static String processHyperlinkRelationship(
        org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHyperlink ctHyperlink,
        PackageRelationship relationship) {
        try {
            StringBuilder text = new StringBuilder();
            String url = relationship.getTargetURI().toString();
            // 提取超链接文本
            String linkText = extractHyperlinkText(ctHyperlink);

            if (linkText.isEmpty()) {
                linkText = url; // 如果没有文本，使用URL作为文本
            }

            // 处理URL中的特殊字符
            if (url.startsWith("mailto:")) {
                // 处理邮件链接
                text.append("[").append(linkText).append("](").append(url).append(")");
            } else {
                // 处理普通链接
                text.append("[").append(linkText).append("](").append(url).append(")");
                log.info("处理超链接: {} -> {}", linkText, url);
            }

            // 已处理超链接，返回结果
            return text.toString().trim();
        } catch (Exception e) {
            log.warn("解析超链接URL时出错: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取超链接文本
     * 
     * @param ctHyperlink 超链接元素
     * @return 超链接文本
     */
    private static String
        extractHyperlinkText(org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHyperlink ctHyperlink) {
        StringBuilder linkText = new StringBuilder();
        if (ctHyperlink.getRList() != null && !ctHyperlink.getRList().isEmpty()) {
            for (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR ctr : ctHyperlink.getRList()) {
                if (ctr.getTList() != null) {
                    for (org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText ctText : ctr.getTList()) {
                        linkText.append(ctText.getStringValue());
                    }
                }
            }
        }
        return linkText.toString();
    }


    /**
     * 应用段落对齐方式
     * 
     * @param paragraph 段落对象
     * @param text 文本构建器
     */
    private static void applyParagraphAlignment(XWPFParagraph paragraph, StringBuilder text) {
        if (paragraph.getAlignment() != null) {
            switch (paragraph.getAlignment()) {
                case CENTER:
                    text.insert(0, "<div align=\"center\">");
                    text.append("</div>");
                    break;
                case RIGHT:
                    text.insert(0, "<div align=\"right\">");
                    text.append("</div>");
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 处理段落中的Run元素
     * 
     * @param paragraph 段落对象
     * @param text 文本构建器
     * @param imagePaths 图片路径列表
     * @return 是否包含Excel对象
     */
    private static boolean processRunsInParagraph(XWPFParagraph paragraph, StringBuilder text,
        List<ImageInfo> imagePaths) {
        return paragraph.getRuns().stream().anyMatch(run -> {
            try {
                if (!run.getEmbeddedPictures().isEmpty()) {
                    return processPicturesInRun(run, text, imagePaths);
                } else {
                    return processTextInRun(run, text);
                }
            } catch (Exception e) {
                log.error("处理run时出错: {}", e.getMessage(), e);
                return false;
            }
        });
    }

    /**
     * 处理Run中的图片
     * 
     * @param run Run元素
     * @param text 文本构建器
     * @param imagePaths 图片路径列表
     * @return 是否包含Excel对象
     */
    private static boolean processPicturesInRun(XWPFRun run, StringBuilder text, List<ImageInfo> imagePaths) {
        return run.getEmbeddedPictures().stream().anyMatch(picture -> {
            XWPFPictureData pictureData = picture.getPictureData();
            // 检查是否为OLE对象
            if (isExcelObject(pictureData)) {
                return true; // 是Excel对象
            }

            // 处理普通图片
            imagePaths.stream()
                .filter(imageInfo -> imageInfo.getRelId().equals(pictureData.getParent().getRelationId(pictureData)))
                .findFirst().ifPresent(imageInfo -> text.append("![图片](").append(imageInfo.getPath()).append(")"));

            return false; // 不是Excel对象
        });
    }

    /**
     * 判断图片数据是否为Excel对象
     * 
     * @param pictureData 图片数据
     * @return 如果是Excel对象返回true，否则返回false
     */
    private static boolean isExcelObject(XWPFPictureData pictureData) {
        if (pictureData == null) {
            return false;
        }

        String extension = pictureData.suggestFileExtension().toLowerCase();

        // 检查文件扩展名
        return extension.contains("xls") || extension.contains("xlsx");
    }

    /**
     * 处理Run中的文本
     * 
     * @param run Run元素
     * @param text 文本构建器
     * @return 是否包含Excel对象（始终返回false）
     */
    private static boolean processTextInRun(XWPFRun run, StringBuilder text) {
        String runText = run.toString();
        if (runText != null && !runText.trim().equals("null")) {
            // 检查是否包含提示文本
            if (!runText.contains("点击图片可查看完整电子表格")) {
                applyTextFormatting(run, text, runText);
            }
        }
        return false;
    }

    /**
     * 应用文本格式
     * 
     * @param run Run元素
     * @param text 文本构建器
     * @param runText 运行文本
     */
    private static void applyTextFormatting(XWPFRun run, StringBuilder text, String runText) {
        if (run.isBold()) {
            text.append("**").append(runText).append("**");
        } else if (run.isItalic()) {
            text.append("*").append(runText).append("*");
        } else {
            text.append(runText);
        }
    }

    /**
     * 判断段落是否为标题
     * 
     * <p>
     * 通过分析段落的样式、字体大小、加粗等属性，判断段落是否为标题。
     * </p>
     *
     * @param paragraph Word段落对象
     * @return 如果是标题返回true，否则返回false
     */
    public static boolean isHeadingParagraph(XWPFParagraph paragraph) {
        // 尝试通过样式名称判断
        String styleName = null;
        try {
            if (paragraph.getStyleID() != null) {
                styleName = paragraph.getStyle();
            }
        } catch (Exception e) {
            log.debug("获取段落样式时出错: {}", e.getMessage());
        }

        // 检查样式名称是否包含"heading"、"标题"等关键词
        if (styleName != null && (styleName.toLowerCase().contains("heading")
            || styleName.toLowerCase().contains("title") || styleName.contains("标题"))) {
            return true;
        }

        // 检查段落的字体大小和加粗等属性
        try {
            if (paragraph.getRuns() != null && !paragraph.getRuns().isEmpty()) {
                XWPFRun firstRun = paragraph.getRuns().get(0);
                // 标题通常字体较大且加粗
                int fontSize = firstRun.getFontSize();
                boolean isBold = firstRun.isBold();

                // 字体大小大于正文字体(通常为11或12)且为粗体，可能是标题
                if (fontSize > 14 && isBold) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("检查段落格式时出错: {}", e.getMessage());
        }

        // 根据段落标识符判断
        try {
            if (paragraph.getCTP() != null && paragraph.getCTP().getPPr() != null
                && paragraph.getCTP().getPPr().getOutlineLvl() != null) {
                return true;
            }
        } catch (Exception e) {
            log.debug("检查段落大纲级别时出错: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 获取标题级别（1-6）
     * 
     * <p>
     * 通过分析段落的样式名称、大纲级别和字体大小等属性，确定标题的级别（1-6，对应Markdown中的#到######）。
     * </p>
     * 
     * @param paragraph Word段落对象
     * @return 标题级别（1-6）
     */
    public static int getHeadingLevel(XWPFParagraph paragraph) {
        // 默认标题级别
        int level = 1;

        // 尝试从样式名称中获取级别
        String styleName = null;
        try {
            if (paragraph.getStyleID() != null) {
                styleName = paragraph.getStyle();
            }
        } catch (Exception e) {
            log.debug("获取段落样式时出错: {}", e.getMessage());
        }

        if (styleName != null) {
            if (styleName.toLowerCase().contains("heading 1") || styleName.toLowerCase().contains("title")
                || styleName.contains("标题 1")) {
                return 1;
            } else if (styleName.toLowerCase().contains("heading 2") || styleName.contains("标题 2")) {
                return 2;
            } else if (styleName.toLowerCase().contains("heading 3") || styleName.contains("标题 3")) {
                return 3;
            } else if (styleName.toLowerCase().contains("heading 4") || styleName.contains("标题 4")) {
                return 4;
            } else if (styleName.toLowerCase().contains("heading 5") || styleName.contains("标题 5")) {
                return 5;
            } else if (styleName.toLowerCase().contains("heading 6") || styleName.contains("标题 6")) {
                return 6;
            }
        }

        // 尝试从段落大纲级别获取
        try {
            if (paragraph.getCTP() != null && paragraph.getCTP().getPPr() != null
                && paragraph.getCTP().getPPr().getOutlineLvl() != null) {
                level = paragraph.getCTP().getPPr().getOutlineLvl().getVal().intValue() + 1;
                return Math.min(level, 6); // 确保级别在1-6之间
            }
        } catch (Exception e) {
            log.debug("获取段落大纲级别时出错: {}", e.getMessage());
        }

        // 根据字体大小判断级别
        try {
            if (paragraph.getRuns() != null && !paragraph.getRuns().isEmpty()) {
                XWPFRun firstRun = paragraph.getRuns().get(0);
                int fontSize = firstRun.getFontSize();

                if (fontSize >= 28) {
                    return 1;
                } else if (fontSize >= 24) {
                    return 2;
                } else if (fontSize >= 20) {
                    return 3;
                } else if (fontSize >= 18) {
                    return 4;
                } else if (fontSize >= 16) {
                    return 5;
                } else {
                    return 6;
                }
            }
        } catch (Exception e) {
            log.debug("根据字体大小判断标题级别时出错: {}", e.getMessage());
        }

        return level;
    }

    /**
     * 修复连续加粗标记问题
     *
     * <p>
     * 修复文本中连续的加粗标记，例如将 "**文本1****文本2**" 修复为 "**文本1 文本2**"
     * </p>
     *
     * @param text 原始文本
     * @return 修复后的文本
     */
    public static String fixConsecutiveBoldMarkers(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 修复连续的四个星号（两组加粗标记相连）
        String result = text.replaceAll("\\*\\*\\*\\*", " ");

        // 修复连续的六个星号（三组加粗标记相连）
        result = result.replaceAll("\\*\\*\\*\\*\\*\\*", " ");

        // 修复连续的八个星号（四组加粗标记相连）
        result = result.replaceAll("\\*\\*\\*\\*\\*\\*\\*\\*", " ");

        return result;
    }
}