package com.qnvip.qwen.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IDCardMaskingUtil {
    public static String maskIDCardNumbers(String text) {
        // 正则表达式匹配 18 位身份证号（含校验码 X/x），确保独立存在
        Pattern pattern = Pattern.compile("(?<!\\d)(\\d{17}[\\dXx])(?!\\d)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String idCard = matcher.group(1);
            // 保留前 3 位和后 4 位，隐藏中间部分
            String masked = idCard.substring(0, 3) + "***********" + idCard.substring(14);
            matcher.appendReplacement(result, masked);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static void main(String[] args) {
        String text = "身份证号：11010519900307888X，长数字测试：123456789012345678，带空格号 350782 1985 0101 1234。";
        String maskedText = maskIDCardNumbers(text);
        System.out.println(maskedText);
        /* 输出：
           身份证号：110***********88X，长数字测试：123456789012345678，带空格号 350782 1985 0101 1234。
        */
    }
}