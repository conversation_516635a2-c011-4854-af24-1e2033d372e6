package com.qnvip.qwen.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;

public class ElasticsearchScoreNormalizeUtil {

    /**
     * 获取归一化后得分大于指定阈值的SearchHit列表
     * 
     * @param response Elasticsearch查询响应
     * @param threshold 归一化得分阈值（0-1）
     * @return 过滤后的SearchHit列表
     */
    public static List<SearchHit> filterScore(SearchResponse response, double threshold) {
        List<SearchHit> result = new ArrayList<>();
        SearchHit[] hits = response.getHits().getHits();

        if (hits == null || hits.length == 0) {
            return result;
        }

        // 收集所有原始得分
        double[] scores = Arrays.stream(hits).mapToDouble(SearchHit::getScore).toArray();

        // 计算极值
        double maxScore = Arrays.stream(scores).max().orElse(0);

        // 处理所有得分相同的情况
        if (Double.compare(maxScore, 0) == 0) {
            if (maxScore > 0 && threshold <= 1.0) {
                return Arrays.asList(hits);
            }
            return result;
        }


        List<Double> array = new ArrayList<>();
        // 过滤和归一化处理
        for (SearchHit hit : hits) {
            double normalized = normalize(hit.getScore(), maxScore);
            array.add(normalized);
            if (normalized > threshold) {
                result.add(hit);
            }
        }

        return result;
    }

    /**
     * 归一化得分计算（最小-最大归一化）
     */
    private static double normalize(double score, double max) {
        return (score) / (max);
    }
}