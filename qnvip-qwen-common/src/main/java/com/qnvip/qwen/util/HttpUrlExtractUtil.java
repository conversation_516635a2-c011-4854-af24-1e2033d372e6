package com.qnvip.qwen.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HttpUrlExtractUtil {

    /**
     * 从文本中提取所有HTTP/HTTPS链接
     *
     * @param text 原始文本
     * @return 包含所有HTTP/HTTPS链接的列表（按出现顺序）
     */
    public static List<String> extractHttpUrls(String text) {
        List<String> urls = new ArrayList<>();
        // 中文与英文字母/数字之间加空格
        text = text.replaceAll("([\\u4e00-\\u9fa5])([a-zA-Z0-9])", "$1 $2");
        text = text.replaceAll("([a-zA-Z0-9])([\\u4e00-\\u9fa5])", "$1 $2");

        // 正则表达式匹配 http 或 https 开头的 URL，包含端口、路径、参数
        String regex = "(?i)\\bhttps?://[\\w\\-\\.]+(:\\d+)?(/[\\w\\-\\.%\\?=&/#]*)?";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            urls.add(matcher.group());
        }

        return urls;
    }
    // 使用示例
    public static void main(String[] args) {
        String sampleText = "访问官方网站https://www.example.com或测试链接http://test.site:8080/path?query=value。";

        List<String> extractedUrls = extractHttpUrls(sampleText);

        System.out.println("提取到的HTTP/HTTPS链接:");
        for (String url : extractedUrls) {
            System.out.println(url);
        }
    }
}
