package com.qnvip.qwen.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月14日 18:42:00
 */
@Slf4j
@Component
public class FileSystemUtil {

    private static String uploadUrl = "http://***********:9113";

    private static final int MAX_RETRY_TIMES = 3;


    public static String upload(byte[] data, String fileName) {
        File tempFile = null;
        try {
            // 创建临时文件
            tempFile = new File("tmp/" + fileName);
            tempFile.getParentFile().mkdirs();
            // 写入数据
            Files.write(tempFile.toPath(), data);
            // log.info("临时文件创建成功: {}", tempFile.getAbsolutePath());

            // 调用已有的上传方法
            String result = upload(tempFile, "qnvip-qwen/" + fileName);

            return result;
        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            throw new RuntimeException("创建临时文件失败", e);
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    public static String upload(File file, String key) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < MAX_RETRY_TIMES) {
            try {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("systemCode", "47");
                paramMap.put("file", file);
                paramMap.put("fileKey", key);

                String result = HttpUtil.post(uploadUrl + "/mgmt/uploadPub", paramMap);
                log.info("文件上传结果: {}", result);

                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject.getBoolean("success")) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    String fileKey = data.getString("fileKey");
                    String domain = data.getString("domain");
                    String url = domain + fileKey;
                    log.info("文件url: {}", url);
                    return url;
                } else {
                    log.error("文件上传失败，当前重试次数: {}，失败信息: {}", retryCount, result);
                    retryCount++;
                }
            } catch (Exception e) {
                lastException = e;
                log.error("文件上传异常，当前重试次数: {}", retryCount, e);
                retryCount++;
            }

            if (retryCount < MAX_RETRY_TIMES) {
                try {
                    // 重试前等待一段时间
                    Thread.sleep(2000);
                } catch (InterruptedException interruptedException) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        // 所有重试都失败后抛出异常
        String errorMsg = "文件上传失败，已重试" + MAX_RETRY_TIMES + "次";
        log.error(errorMsg);
        if (lastException != null) {
            throw new RuntimeException(errorMsg, lastException);
        } else {
            throw new RuntimeException(errorMsg);
        }
    }

    public static String getUploadUrl() {
        return uploadUrl;
    }

    @Value("${fileSystem.host:}")
    public void setUploadUrl(String uploadUrl) {
        FileSystemUtil.uploadUrl = uploadUrl;
    }
}
