package com.qnvip.qwen.es;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import cn.hutool.core.lang.Pair;

@Order(1)
@Configuration
public class EsClientConfig {

    @Value("${elasticsearch.hostAndPort}")
    private String hostAndPort;
    @Value("${elasticsearch.username}")
    private String username;
    @Value("${elasticsearch.password}")
    private String password;

    private static String hostAndPort0;
    private static String username0;
    private static String password0;

    @PostConstruct
    public void init() {
        hostAndPort0 = hostAndPort;
        username0 = username;
        password0 = password;
    }

    public static ElasticsearchOperations elasticsearchOperations() {
        RestHighLevelClient client = createRestHighLevelClient();
        return new ElasticsearchRestTemplate(client);
    }

    public static synchronized RestHighLevelClient rebuildByException(@SuppressWarnings("unused") RuntimeException re,
        RestHighLevelClient client, Class<?> clientHolderClass) {
        RestHighLevelClient client0 = getByHolder(clientHolderClass);
        if (client0 != null) {
            return client0;
        }

        try {
            if (client != null) {
                client.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return buildHolder(clientHolderClass);
    }

    private static RestHighLevelClient getByHolder(Class<?> clientHolderClass) {
        Pair<LocalDateTime, RestHighLevelClient> valPair = clientHolderTimerMap.get(clientHolderClass);
        if (valPair == null) {
            return null;
        }

        boolean after = valPair.getKey().isAfter(LocalDateTime.now().minusSeconds(70));
        if (after) {
            return valPair.getValue();
        }
        return null;
    }

    private static RestHighLevelClient buildHolder(Class<?> clientHolderClass) {
        RestHighLevelClient client = createRestHighLevelClient();
        clientHolderTimerMap.put(clientHolderClass, Pair.of(LocalDateTime.now(), client));
        return client;
    }

    private static final Map<Class<?>, Pair<LocalDateTime, RestHighLevelClient>> clientHolderTimerMap = new HashMap<>();

    private static RestHighLevelClient createRestHighLevelClient() {
        ClientConfiguration configuration =
            ClientConfiguration.builder().connectedTo(hostAndPort0).withBasicAuth(username0, password0)
                .withConnectTimeout(Duration.ofSeconds(15)).withSocketTimeout(Duration.ofSeconds(60)).build();
        return RestClients.create(configuration).rest();
    }

}
