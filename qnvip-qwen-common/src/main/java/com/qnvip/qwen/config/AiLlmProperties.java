package com.qnvip.qwen.config;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@ConfigurationProperties(prefix = AiLlmProperties.PREFIX)
@Data
@Configuration
public class AiLlmProperties {
    public static final String PREFIX = "llm";

    private Map<String, Api> apis;

    public void setApis(Map<String, Api> apis) {
        this.apis = apis;
    }

    @Data
    public static class Api {

        /**
         * api请求地址
         */
        private String url;

        /**
         * ep-20250324174654-5c8b2
         */
        private String modelName;

        /**
         * 7471f763-54af-4382-9285-ac0994fbe08f
         */
        private String apiKey;

    }

}
