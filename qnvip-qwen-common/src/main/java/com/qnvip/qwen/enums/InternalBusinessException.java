package com.qnvip.qwen.enums;

import com.qnvip.common.exception.BusinessException;

public class InternalBusinessException extends BusinessException {
    protected InternalBusinessException(int errorCode, String errorMsg) {
        super(errorCode, errorMsg);
    }

    public static InternalBusinessException instance(int errorCode, String errorMsg) {
        return new InternalBusinessException(errorCode, errorMsg);
    }
}
