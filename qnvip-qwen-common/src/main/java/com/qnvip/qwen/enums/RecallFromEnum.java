package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum RecallFromEnum {
    // 导航向量召回
    NAV_MILVUS("milvus_nav"),
    // 多路召回之es
    MULT_ES("mult_es"),

    MULT_GRAPH("mult_graph"),
    // 多路召回之向量之QA
    MULT_MILVUS_QA("mult_milvus_qa"),
    // 多路召回之向量之切块
    MULT_MILVUS_CHUNK("mult_milvus_chunk"),
    // 重排序
    MULT_RERANK("mult_rerank"),
    // 多路召回周围切块
    CHUNK_MERGE("chunk_merge"),
    // 统计
    STATIC("static"),;

    private final String value;

    public static RecallFromEnum getByValue(String value) {
        for (RecallFromEnum recallFromEnum : RecallFromEnum.values()) {
            if (recallFromEnum.value.equals(value)) {
                return recallFromEnum;
            }
        }
        return null;
    }

}