package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @time 2018年05月03日 下午4:40
 */
@Getter
@RequiredArgsConstructor
public enum StatusEnum {

    UNKNOW(99, "未知"),

    DEFAULT(0, "默认值"), INIT(1, "初始"), PENDING(2, "等待中"), COMPLETE(3, "已完善"), FAIL(5, "失败"), SUCCESS(10, "审核通过"), REJECT(15, "审核不通过"), OVERDUE(15, "过期"), DISABLED(20, "失效"),

    YES(1, "是"),
    NO(0, "否"),

    ;

    private final int code;
    private final String desc;

    public static StatusEnum getStatus(int value) {
        switch (value) {
            case 1:
                return StatusEnum.INIT;
            case 2:
                return StatusEnum.PENDING;
            case 5:
                return StatusEnum.FAIL;
            case 10:
                return StatusEnum.SUCCESS;
            default:
                return StatusEnum.UNKNOW;
        }
    }

}
