package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum RecallStageEnum {

    NAVIGATION(0, "导航"),

    ES(10, "es召回"),

    MILVUS(20, "milvus召回"),

    GRAPH(32, "graph召回"),


    RE_RANKING(40, "重排序召回"),

    STATIC(41, "STATIC"),

    CHUNK_MERGE(43, "切块组文章"),

    DOC_TOP_N(44, "文章取TOP-N"),



    ;

    private final int code;
    private final String desc;

    public static RecallStageEnum getByCode(int code) {
        for (RecallStageEnum value : RecallStageEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}
