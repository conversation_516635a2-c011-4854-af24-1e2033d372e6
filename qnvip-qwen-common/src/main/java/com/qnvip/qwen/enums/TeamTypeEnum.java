package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum TeamTypeEnum {

    // 语雀
    YUQUE(1, "语雀"),
    // 飞书
    FEISHU(2, "飞书"),
    // api
    API(10, "飞书"),;

    private final int code;
    private final String desc;

    public static TeamTypeEnum getByCode(int code) {
        for (TeamTypeEnum value : TeamTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}