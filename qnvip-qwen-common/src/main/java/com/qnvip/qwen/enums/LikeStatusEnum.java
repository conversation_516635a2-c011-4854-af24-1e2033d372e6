package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum LikeStatusEnum {
    // 赞
    LIKE(1, "like"),
    // 否
    DISLIKE(0, null),
    // 踩
    STEP_ON(-1, "dislike");

    private final int code;
    private final String desc;

    public static LikeStatusEnum getByCode(int code) {
        for (LikeStatusEnum value : LikeStatusEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }
}
