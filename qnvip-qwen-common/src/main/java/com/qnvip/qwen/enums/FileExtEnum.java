package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum FileExtEnum {


    MD(1, "md"),

    XLSX(2, "xlsx"),

    DOCX(3, "docx"),

    TXT(4, "txt"),

    PDF(5, "pdf"),

    PPT(6, "ppt"),

    PNG(7, "png"),

    JPG(8, "jpg"),

    JPEG(9, "jpeg"),

    BMP(10, "bmp"),

    RAW(11, "raw"),

    XLS(12, "xls"),

    DOC(13, "doc"),

    EXCEL_JSON(14, "exceljson"),

    BOARD_JSON(15, "boardjson"),

    TABLE_JSON(16, "tablejson"),

    MIND_NOTE(17, "mindnote"),

    ;

    private final int code;
    private final String desc;

    public static FileExtEnum getByCode(int code) {
        for (FileExtEnum value : FileExtEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

    public static boolean isIn(String fileExtension, FileExtEnum... extensionEnums) {
        for (FileExtEnum extensionEnum : extensionEnums) {

            if (extensionEnum.getDesc().equals(fileExtension.toLowerCase())) {
                return true;
            }
        }
        return false;
    }


}