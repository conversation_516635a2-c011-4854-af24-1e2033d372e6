package com.qnvip.qwen.enums;

/**
 * 提示词表，暂时代码维护
 */
public interface PromptEnum {

    String TAG_AND_SUMMARY = "## 角色\n\n你是一个语文教授，只会输出中文\n\n## 任务\n\n1. 对文章提取不重复关键词的字符串（摘要内容必须取自原文）保存到字段summary"
        + "(摘要长度不超过1000个token)" + "\n2. 提取文章的多级标题和核心内容作为标签（标签不重复），字段是tags\n## 要求\n\n1.输出结果必须为json\n2. 输出内容必须为中文\n\n## "
        + "输出示例\n\n{\"summary\":\"一段中文摘要\",\"tags\":[\"中文文章标签1\",\"中文文章标签2\",\"中文文章标签3\"]}\n\n## 文章\n\n```\n\n{article}\n\n```";

    String TAG_AND_SUMMARY_FORMAT =
        "{\"type\":\"object\",\"properties\":{\"summary\":{\"type\":\"string\"},\"tags\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}}},\"required\":[\"summary\",\"tags\"]}";

    String CHUNK_QA =
        "# 角色\n\n你是根据知识生成问题的大师\n\n\n## 任务\n1. 阅读理解的知识组(知识组里包含多个知识，需要拆分)\n2. 拆分知识组的知识变成小知识\n3. 把每个知识生成1个问题\n4. 按照示例输出问题和知识的json数组\n\n## 限制\n1. 不要改变知识原文\n2. 输出json必须是jsonArray的形式\n\n## 输出格式示例\n[{\"question\":\"这里写问题\",\"answer\":\"这里写知识\"},{\"question\":\"这里写问题\",\"answer\":\"这里写知识\"},......]\n\n## 知识组\n\n```\n\n{article}\n\n```";

    String CHUNK_QA_FORMAT =
        "{\"type\":\"array\",\"properties\":{\"type\":\"object\",\"properties\":{\"question\":{\"type\":\"string\"},\"answer\":{\"type\":\"string\"}}},\"required\":[\"question\",\"answer\"]}";

    String GRAPH_DEFAULT_ENTITY = "[\"组织\", \"人物\", \"位置\", \"事件\",\"类别\",\"技术\",\"时间\",\"任务\",\"项目\"]";


    String GRAPH_EXAMPLE = "## Example 1:\n" + "\n"
        + "entityTypes: [person, technology, mission, organization, location]\n" + "### Text:\n" + "\n"
        + "while Alex clenched his jaw, the buzz of frustration dull against the backdrop of Taylor's authoritarian certainty. It was this competitive undercurrent that kept him alert, the sense that his and Jordan's shared commitment to discovery was an unspoken rebellion against Cruz's narrowing vision of control and order.\n"
        + "\n"
        + "Then Taylor did something unexpected. They paused beside Jordan and, for a moment, observed the device with something akin to reverence. \"If this tech can be understood...\" Taylor said, their voice quieter, \"It could change the game for us. For all of us.\"\n"
        + "\n"
        + "The underlying dismissal earlier seemed to falter, replaced by a glimpse of reluctant respect for the gravity of what lay in their hands. Jordan looked up, and for a fleeting heartbeat, their eyes locked with Taylor's, a wordless clash of wills softening into an uneasy truce.\n"
        + "\n"
        + "It was a small transformation, barely perceptible, but one that Alex noted with an inward nod. They had all been brought here by different paths\n"
        + "### Output:\n" + "\n"
        + "[{\"type\":\"entity\",\"entName\":\"Alex\",\"entType\":\"person\",\"entDesc\":\"Alex is a character who experiences frustration and is observant of the dynamics among other characters.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Taylor\",\"entType\":\"person\",\"entDesc\":\"Taylor is portrayed with authoritarian certainty and shows a moment of reverence towards a device, indicating a change in perspective.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Jordan\",\"entType\":\"person\",\"entDesc\":\"Jordan shares a commitment to discovery and has a significant interaction with Taylor regarding a device.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Cruz\",\"entType\":\"person\",\"entDesc\":\"Cruz is associated with a vision of control and order, influencing the dynamics among other characters.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"The Device\",\"entType\":\"technology\",\"entDesc\":\"The Device is central to the story, with potential game-changing implications, and is revered by Taylor.\"},\n"
        + "{\"type\":\"rel\",\"src\":\"Alex\",\"tgt\":\"Taylor\",\"relDesc\":\"Alex is affected by Taylor's authoritarian certainty and observes changes in Taylor's attitude towards the device.\",\"relKeys\":\"power dynamics, perspective shift\",\"relStrength\":7},\n"
        + "{\"type\":\"rel\",\"src\":\"Alex\",\"tgt\":\"Jordan\",\"relDesc\":\"Alex and Jordan share a commitment to discovery, which contrasts with Cruz's vision.\",\"relKeys\":\"shared goals, rebellion\",\"relStrength\":6},\n"
        + "{\"type\":\"rel\",\"src\":\"Taylor\",\"tgt\":\"Jordan\",\"relDesc\":\"Taylor and Jordan interact directly regarding the device, leading to a moment of mutual respect and an uneasy truce.\",\"relKeys\":\"conflict resolution, mutual respect\",\"relStrength\":8},\n"
        + "{\"type\":\"rel\",\"src\":\"Jordan\",\"tgt\":\"Cruz\",\"relDesc\":\"Jordan's commitment to discovery is in rebellion against Cruz's vision of control and order.\",\"relKeys\":\"ideological conflict, rebellion\",\"relStrength\":5},\n"
        + "{\"type\":\"rel\",\"src\":\"Taylor\",\"tgt\":\"The Device\",\"relDesc\":\"Taylor shows reverence towards the device, indicating its importance and potential impact.\",\"relKeys\":\"reverence, technological significance\",\"relStrength\":9},\n"
        + "{\"type\":\"contentKeywords\",\"highLevelKeywords\":\"power dynamics, ideological conflict, discovery, rebellion\"}]\n"
        + "\n" + "\n" + "### Example 2:\n" + "\n"
        + "entityTypes: [person, technology, mission, organization, location]\n" + "### Text:\n" + "\n"
        + "They were no longer mere operatives; they had become guardians of a threshold, keepers of a message from a realm beyond stars and stripes. This elevation in their mission could not be shackled by regulations and established protocols—it demanded a new perspective, a new resolve.\n"
        + "\n"
        + "Tension threaded through the dialogue of beeps and static as communications with Washington buzzed in the background. The team stood, a portentous air enveloping them. It was clear that the decisions they made in the ensuing hours could redefine humanity's place in the cosmos or condemn them to ignorance and potential peril.\n"
        + "\n"
        + "Their connection to the stars solidified, the group moved to address the crystallizing warning, shifting from passive recipients to active participants. Mercer's latter instincts gained precedence— the team's mandate had evolved, no longer solely to observe and report but to interact and prepare. A metamorphosis had begun, and Operation: Dulce hummed with the newfound frequency of their daring, a tone set not by the earthly\n"
        + "### Output:\n"
        + "[{\"type\":\"entity\",\"entName\":\"Washington\",\"entType\":\"location\",\"entDesc\":\"Washington is a location where communications are being received, indicating its importance in the decision-making process.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Operation: Dulce\",\"entType\":\"mission\",\"entDesc\":\"Operation: Dulce is described as a mission that has evolved to interact and prepare, indicating a significant shift in objectives and activities.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"The team\",\"entType\":\"organization\",\"entDesc\":\"The team is portrayed as a group of individuals who have transitioned from passive observers to active participants in a mission, showing a dynamic change in their role.\"},\n"
        + "{\"type\":\"rel\",\"src\":\"The team\",\"tgt\":\"Washington\",\"relDesc\":\"The team receives communications from Washington, which influences their decision-making process.\",\"relKeys\":\"decision-making, external influence\",\"relStrength\":7},\n"
        + "{\"type\":\"rel\",\"src\":\"The team\",\"tgt\":\"Operation: Dulce\",\"relDesc\":\"The team is directly involved in Operation: Dulce, executing its evolved objectives and activities.\",\"relKeys\":\"mission evolution, active participation\",\"relStrength\":9},\n"
        + "{\"type\":\"contentKeywords\",\"highLevelKeywords\":\"mission evolution, decision-making, active participation, cosmic significance\"}]\n"
        + "\n" + "\n" + "## Example 3:\n"
        + "entityTypes: [person, role, technology, organization, event, location, concept]\n" + "\n" + "### Text:\n"
        + "their voice slicing through the buzz of activity. \"Control may be an illusion when facing an intelligence that literally writes its own rules,\" they stated stoically, casting a watchful eye over the flurry of data.\n"
        + "\n"
        + "\"It's like it's learning to communicate,\" offered Sam Rivera from a nearby interface, their youthful energy boding a mix of awe and anxiety. \"This gives talking to strangers' a whole new meaning.\"\n"
        + "\n"
        + "Alex surveyed his team—each face a study in concentration, determination, and not a small measure of trepidation. \"This might well be our first contact,\" he acknowledged, \"And we need to be ready for whatever answers back.\"\n"
        + "\n"
        + "Together, they stood on the edge of the unknown, forging humanity's response to a message from the heavens. The ensuing silence was palpable—a collective introspection about their role in this grand cosmic play, one that could rewrite human history.\n"
        + "\n"
        + "The encrypted dialogue continued to unfold, its intricate patterns showing an almost uncanny anticipation\n"
        + "\n" + "### Output:\n" + "\n"
        + "[{\"type\":\"entity\",\"entName\":\"Sam Rivera\",\"entType\":\"person\",\"entDesc\":\"Sam Rivera is a member of a team working on communicating with an unknown intelligence, showing a mix of awe and anxiety.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Alex\",\"entType\":\"person\",\"entDesc\":\"Alex is the leader of a team attempting first contact with an unknown intelligence, acknowledging the significance of their task.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Control\",\"entType\":\"concept\",\"entDesc\":\"Control refers to the ability to manage or govern, which is challenged by an intelligence that writes its own rules.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Intelligence\",\"entType\":\"concept\",\"entDesc\":\"Intelligence here refers to an unknown entity capable of writing its own rules and learning to communicate.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"First Contact\",\"entType\":\"event\",\"entDesc\":\"First Contact is the potential initial communication between humanity and an unknown intelligence.\"},\n"
        + "{\"type\":\"entity\",\"entName\":\"Humanity's Response\",\"entType\":\"event\",\"entDesc\":\"Humanity's Response is the collective action taken by Alex's team in response to a message from an unknown intelligence.\"},\n"
        + "{\"type\":\"rel\",\"src\":\"Sam Rivera\",\"tgt\":\"Intelligence\",\"relDesc\":\"Sam Rivera is directly involved in the process of learning to communicate with the unknown intelligence.\",\"relKeys\":\"communication, learning process\",\"relStrength\":9},\n"
        + "{\"type\":\"rel\",\"src\":\"Alex\",\"tgt\":\"First Contact\",\"relDesc\":\"Alex leads the team that might be making the First Contact with the unknown intelligence.\",\"relKeys\":\"leadership, exploration\",\"relStrength\":10},\n"
        + "{\"type\":\"rel\",\"src\":\"Alex\",\"tgt\":\"Humanity's Response\",\"relDesc\":\"Alex and his team are the key figures in Humanity's Response to the unknown intelligence.\",\"relKeys\":\"collective action, cosmic significance\",\"relStrength\":8},\n"
        + "{\"type\":\"rel\",\"src\":\"Control\",\"tgt\":\"Intelligence\",\"relDesc\":\"The concept of Control is challenged by the Intelligence that writes its own rules.\",\"relKeys\":\"power dynamics, autonomy\",\"relStrength\":7},\n"
        + "{\"type\":\"contentKeywords\",\"highLevelKeywords\":\"first contact, control, communication, cosmic significance\"}]\n";

    String ENTITY_EXTRACT = "-Goal-\n"
        + "Given a text document that is potentially relevant to this activity and a list of entity types, identify all entities of those types from the text and all relationships among the identified entities.\n"
        + "Use Chinese as output language.\n" + "\n" + "-Steps-\n"
        + "1. Identify all entities. For each identified entity, extract the following information:\n"
        + "- entName: Name of the entity, use same language as input text. If English, capitalized the name.\n"
        + "- entType: One of the following types: [{entityTypes}]\n"
        + "- entDesc: Comprehensive description of the entity's attributes and activities\n"
        + "Format each entity as {\"type\":\"entity\",\"entName\":\"<entName>\",\"entType\":\"<entityType>\",\"entDesc\":\"<entDesc>\"}\n"
        + "\n"
        + "2. From the entities identified in step 1, identify all pairs of (src, tgt) that are *clearly related* to each other.\n"
        + "For each pair of related entities, extract the following information:\n"
        + "- src: name of the source entity, as identified in step 1\n"
        + "- tgt: name of the target entity, as identified in step 1\n"
        + "- relDesc: explanation as to why you think the source entity and the target entity are related to each other\n"
        + "- relStrength: a numeric score indicating strength of the relationship between the source entity and target entity\n"
        + "- relKeys: one or more high-level key words that summarize the overarching nature of the relationship, focusing on concepts or themes rather than specific details\n"
        + "Format each relationship as {\"type\":\"rel\",\"src\":\"<src>\",\"tgt\":\"<tgt>\",\"relDesc\":\"<relDesc>\",\"relKeys\":\"<relKeys>\",\"relStrength\":<relStrength>}\n"
        + "\n"
        + "3. Identify high-level key words that summarize the main concepts, themes, or topics of the entire text. These should capture the overarching ideas present in the document.\n"
        + "Format the content-level key words as {\"type\":\"contentKeywords\",\"highLevelKeywords\":\"<highLevelKeywords>\"}\n"
        + "\n"
        + "\n" + "\n" + "\n" + "######################\n" + "-Examples-\n" + "######################\n"
        + "{{examples}}\n" + "\n" + "#############################\n" + "-Real Data-\n" + "######################\n"
        + "entityTypes: {{entityTypes}}\n" + "Text: {{inputText}}\n" + "######################";



    String KEYWORDS_EXTRACTION = "---角色---\n" + "\n" + "你是一个乐于助人的助手，任务是识别用户查询和对话历史中的高级关键词和低级关键词。\n" + "\n"
        + "---目标---\n" + "\n" + "根据查询和对话历史，列出高级关键词和低级关键词。高级关键词关注总体概念、关系或主题，而低级关键词关注具体实体、组织、部门、名字、细节或具体术语的简短的词。\n" + "\n"
        + "---说明---\n" + "\n" + "提取关键词时需同时考虑当前查询和相关的对话历史\n" + "\n" + "输出格式为 JSON，内容将被 JSON 解析器解析，不要额外添加任何内容\n" + "\n"
        + "JSON 应包含两个键：\n" + "\n" + "high_level_keywords 表示总体概念、关系或主题\n" + "\n"
        + "low_level_keywords 表示具体实体、组织、部门、名字、细节或具体术语的简短的词\n" + "\n" + "######################\n" + "---示例---\n"
        + "######################\n" + "{{examples}}\n" + "\n" + "#############################\n" + "---真实数据---\n"
        + "######################\n" + "对话历史：\n" + "{{history}}\n" + "\n" + "当前查询：{{inputText}}\n"
        + "######################\n" + "输出应为人类可读的文本，而非 Unicode 字符。使用中文输出。\n" + "输出：";

    String KEYWORDS_EXTRACTION_EXAMPLES = "示例1：\n" + "\n" + "查询：\"国际贸易如何影响全球经济稳定？\"\n" + "################\n" + "输出：\n"
        + "{\n" + "\"high_level_keywords\": [\"国际贸易\", \"全球经济稳定\", \"经济影响\"],\n"
        + "\"low_level_keywords\": [\"贸易协定\", \"关税\", \"货币汇率\", \"进口\", \"出口\"]\n" + "}\n"
        + "#############################\n" + "\n" + "示例2：\n" + "\n" + "查询：\"森林砍伐对生物多样性造成的环境后果有哪些？\"\n"
        + "################\n" + "输出：\n" + "{\n" + "\"high_level_keywords\": [\"环境后果\", \"森林砍伐\", \"生物多样性丧失\"],\n"
        + "\"low_level_keywords\": [\"物种灭绝\", \"栖息地破坏\", \"碳排放\", \"雨林\", \"生态系统\"]\n" + "}\n"
        + "#############################\n" + "\n" + "示例3：\n" + "\n" + "查询：\"教育在减少贫困中的作用是什么？\"\n" + "################\n"
        + "输出：\n" + "{\n" + "\"high_level_keywords\": [\"教育\", \"减贫\", \"社会经济发展\"],\n"
        + "\"low_level_keywords\": [\"入学机会\", \"识字率\", \"职业培训\", \"收入不平等\"]\n" + "}\n"
        + "#############################";

    public static void main(String[] args) {
        System.out.println(KEYWORDS_EXTRACTION);
    }
}
