package com.qnvip.qwen.enums;

import com.qnvip.common.base.BaseErrorCode;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 错误码枚举
 *
 * <AUTHOR>
 * @since 2024/2/1
 */
@RequiredArgsConstructor
@Getter
public enum ErrorCodeEnum implements BaseErrorCode {


    SYSTEM_ERR(-1, "系统异常"), DEMO_ERROR(10001, "demo error{0}"),

    ;


    /**
     * 错误码,6位数字前2位为模块（或者业务领域）编号、后4位为错误编号
     * 例如：10001（01为模块编号、0001为错误编号）
     */
    private final Integer code;
    /**
     * 错误信息
     */
    private final String msg;


    /**
     * 设置系统编码
     * @return
     */
    @Override
    public String systemCode() {
        return "99";
    }
}
