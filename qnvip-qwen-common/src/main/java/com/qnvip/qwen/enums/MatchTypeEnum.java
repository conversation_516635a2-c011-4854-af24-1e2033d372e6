package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum MatchTypeEnum {

    // 成功
    SUCCESS(1, "成功"),
    // 失败
    FAILURE(2, "失败");

    private final int code;
    private final String desc;

    public static MatchTypeEnum getByCode(int code) {
        for (MatchTypeEnum value : MatchTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}