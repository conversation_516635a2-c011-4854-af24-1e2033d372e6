package com.qnvip.qwen.enums;

import java.util.List;

import com.qnvip.qwen.util.Lists;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum CategoryEnum {

    // 实体
    ENTITY(2, Lists.newArrayList("entity", "实体", "ent"), "entity"),
    // 关系
    RELATIONSHIP(3, Lists.newArrayList("relationship", "关系", "rel"), "relationship"),
    // 内容关键词
    CONTENT_KEYWORDS(4, Lists.newArrayList("contentKeywords", "内容关键词"), "relationship"),;

    private final int code;
    private final List<String> desc;
    private final String realDesc;

    public static CategoryEnum getByCode(int code) {
        for (CategoryEnum value : CategoryEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}
