package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum ChatStageEnum {
    // 意图问询
    INTENT_INQUIRY(1, "意图问询"),
    // 问答
    Q_AND_A(2, "问答");

    private final int code;
    private final String desc;

    public static ChatStageEnum getByCode(int code) {
        for (ChatStageEnum value : ChatStageEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }
}
