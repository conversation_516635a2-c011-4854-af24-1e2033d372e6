package com.qnvip.qwen.enums;

import java.util.Arrays;
import java.util.Comparator;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum TaskStepsEnum {

    TASK_STEP_FORMAT(10, "转格式"),

    TASK_STEP_CLEAN(20, "数据清洗"),

    TASK_STEP_DESENSITIZATION(30, "脱敏"),

    TASK_STEP_NAVIGATION(40, "文章导航(摘要,打tag，目录)"),

    TASK_STEP_CHUNK(50, "切块"),

    TASK_STEP_GRAPH(55, "图谱"),

    TASK_STEP_QA(60, "Qa结构化"),

    TASK_STEP_FINISH(100, "完结");

    private final int code;
    private final String desc;

    public static TaskStepsEnum getByCode(int code) {
        for (TaskStepsEnum value : TaskStepsEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

    public TaskStepsEnum getNextStep() {
        return Arrays.stream(values()).filter(step -> step.code > this.code)
            .min(Comparator.comparingInt(TaskStepsEnum::getCode)).orElse(null);
    }
}