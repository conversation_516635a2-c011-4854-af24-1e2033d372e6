package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum ResourceTypeEnum {

    // 知识库资源
    KNOWLEDGE_BASE(1, "知识库资源");

    private final int code;
    private final String desc;

    public static ResourceTypeEnum getByCode(int code) {
        for (ResourceTypeEnum value : ResourceTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}