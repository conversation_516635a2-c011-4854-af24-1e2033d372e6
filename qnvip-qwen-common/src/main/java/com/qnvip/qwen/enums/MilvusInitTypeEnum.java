package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum MilvusInitTypeEnum {

    // 导航
    // NAVIGATION(1, "导航"),
    // 切块
    CHUNKING(2, "切块"),
    // qa链
    QA_LINK(3, "qa链"), GRAPH(4, "graph"),;

    private final int code;
    private final String desc;

    public static MilvusInitTypeEnum getByCode(int code) {
        for (MilvusInitTypeEnum value : MilvusInitTypeEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}