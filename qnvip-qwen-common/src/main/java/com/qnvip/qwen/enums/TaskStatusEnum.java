package com.qnvip.qwen.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum TaskStatusEnum {

    // 待开始
    PENDING(0, "待开始"),
    // 进行中
    PROCESSING(1, "进行中"),
    // 已结束
    COMPLETED(2, "已结束"),
    // 异常
    FAILED(3, "异常");

    private final int code;
    private final String desc;

    public static TaskStatusEnum getByCode(int code) {
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            if (code == value.code) {
                return value;
            }
        }
        return null;
    }

}