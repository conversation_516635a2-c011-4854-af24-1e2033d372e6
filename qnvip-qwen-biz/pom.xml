<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qnvip</groupId>
        <artifactId>qnvip-qwen</artifactId>
        <version>${project.version}</version>
    </parent>

    <artifactId>qnvip-qwen-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.qnvip</groupId>
            <artifactId>qnvip-qwen-service</artifactId>
            <version>${project.version}</version>
        </dependency>


        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-all</artifactId>
            <version>0.62.2</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>dashscope-sdk-java</artifactId>-->
        <!--            &lt;!&ndash; 请将 'the-latest-version' 替换为最新版本号：https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java &ndash;&gt;-->
        <!--            <version>2.19.4</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                    <artifactId>slf4j-api</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                    <artifactId>slf4j-simple</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.google.code.gson</groupId>-->
        <!--            <artifactId>gson</artifactId>-->
        <!--            <version>2.9.1</version>-->
        <!--        </dependency>-->
    </dependencies>

</project>
