package com.qnvip.qwen.bizService.impl.oauth2;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.bizService.Oauth2BizService;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月27日 17:19:00
 */
@Slf4j
@Service
public class Oauth2BizBizServiceImpl implements Oauth2BizService {

    @Value("${security.oauth2.client.url:}")
    private String oauth2Host;

    @Override
    public String getOauth2Token(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            throw FrameworkException.instance("参数异常！");
        }
        Map<String, Object> param = new HashMap<>();
        param.put("mobile", mobile);
        String resp = HttpUtil.get(oauth2Host + "/openapi/getTokenByMobile", param);
        log.info("获取token,mobile:{},返回结果：{}", mobile, resp);
        if (StringUtils.isEmpty(resp)) {
            throw FrameworkException.instance("获取token失败！");
        }
        JSONObject jo = JSONObject.parse(resp);
        String code = jo.getString("code");
        String errmsg = jo.getString("message");
        if (!"0".equals(code)) {
            throw FrameworkException.instance("登录失败，" + errmsg);
        }
        return jo.getString("data");
    }

}
