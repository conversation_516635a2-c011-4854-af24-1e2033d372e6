package com.qnvip.qwen.bizService.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.bizService.DynamicsNavigationBizService;
import com.qnvip.qwen.bizService.ReCallBizService;
import com.qnvip.qwen.enums.BooleanEnum;
import com.qnvip.qwen.enums.RecallTypeEnum;
import com.qnvip.qwen.service.ChatHistoryService;
import com.qnvip.qwen.service.TeamBookService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.ChatHistoryReq;
import com.qnvip.qwen.vo.request.DifyMutilRecallReq;
import com.qnvip.qwen.vo.request.DifyUserQuestionReq;
import com.qnvip.qwen.vo.request.DifyUserRecallReq;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DynamicsNavigationBizServiceImpl implements DynamicsNavigationBizService {

    protected static final ExecutorService executor = new ThreadPoolExecutor(4, 6, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(1024), new ThreadFactoryBuilder().setNamePrefix("syncNavigation-executor-%d").build(),
        new ThreadPoolExecutor.CallerRunsPolicy());

    @Resource
    private ReCallBizService reCallBizService;
    @Resource
    private ChatHistoryService chatHistoryService;
    @Resource
    private TeamBookService bookService;

    @Override
    public String navigation(DifyUserQuestionReq record) {

        // 提交无权限知识库召回任务
        Future<String> possibleRecallFuture = executor.submit(() -> {
            try {
                return userNoPermitRecall(CopierUtil.copy(record, DifyUserQuestionReq.class));
            } catch (Exception e) {
                e.printStackTrace();
                return e.getMessage();
            }
        });

        try {
            // 获取异步结果（带超时处理）
            String recall = userRecall(CopierUtil.copy(record, DifyUserQuestionReq.class));
            String possibleRecall = possibleRecallFuture.get(10, TimeUnit.SECONDS);

            if (ObjectUtils.isEmpty(recall) && ObjectUtils.isEmpty(possibleRecall)) {
                return "";
            }

            return recall + "\n\n" + possibleRecall;
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            Thread.currentThread().interrupt();
            return "系统异常";
        }
    }

    @Override
    public String navigationDebug(DifyUserQuestionReq record) {
        log.error("--- 错误 当前进入为调试方法 --- ");
        return userRecall(CopierUtil.copy(record, DifyUserQuestionReq.class));
    }

    private String userRecall(DifyMutilRecallReq record) {
        if (ObjectUtils.isEmpty(record.getUserInput()) && ObjectUtils.isEmpty(record.getUserInputs())) {
            return "";
        }

        List<DifyUserRecallReq> reqs = new ArrayList<>();
        for (String userInput : record.getUserInputs()) {

            DifyUserRecallReq req = new DifyUserRecallReq();
            req.setQuestionId(getQuestion(record, userInput));
            req.setUserId(record.getUserId());
            req.setUserInput(userInput);
            req.setFileOriginIds(record.getFileOriginIds());

            req.setRecallType(RecallTypeEnum.HAS_PERMIT.getDesc());
            req.setBookIds(record.getUserBookIs());
            req.setRecallConfig(record.getUserPermit());
            req.setWorkflowRunId(record.getWorkflowRunId());
            reqs.add(req);
        }
        return reCallBizService.recall(reqs);
    }

    private String userNoPermitRecall(DifyUserQuestionReq record) {
        List<Long> allBookIds = bookService.getALLBookIds();
        allBookIds.removeAll(record.getUserBookIs());
        List<Long> notPermitBookIds = allBookIds;
        if (notPermitBookIds.isEmpty()) {
            return "";
        }

        if (ObjectUtils.isEmpty(record.getUserInput()) && ObjectUtils.isEmpty(record.getUserInputs())) {
            return "";
        }

        String userInput =
            ObjectUtils.isEmpty(record.getUserInput()) ? record.getUserInputs().get(0) : record.getUserInput();

        DifyUserRecallReq req = new DifyUserRecallReq();
        req.setQuestionId(getQuestion(record, record.getUserInput()));
        req.setUserId(record.getUserId());
        req.setUserInput(userInput);
        req.setRecallType(RecallTypeEnum.NO_PERMIT.getDesc());
        req.setBookIds(notPermitBookIds);
        req.setRecallConfig(record.getNoPermit());
        req.setWorkflowRunId(record.getWorkflowRunId());
        return reCallBizService.recall(req);
    }

    private Long getQuestion(DifyMutilRecallReq record, String userInput) {

        Long questionId = chatHistoryService.loadBeforeQuestionIdByWorkflowRunId(record);
        if (questionId != null) {
            return questionId;
        }

        ChatHistoryReq chatHistoryReq = new ChatHistoryReq();
        chatHistoryReq.setUserId(record.getUserId());
        chatHistoryReq.setContent(userInput);
        chatHistoryReq.setFromUser(BooleanEnum.TRUE.getValue());
        chatHistoryReq.setConversationId(record.getConversationId());
        chatHistoryReq.setAgentId(record.getAgentId());
        chatHistoryReq.setWorkflowRunId(record.getWorkflowRunId());
        chatHistoryReq.setUseBook("true");
        return chatHistoryService.saveChat(chatHistoryReq);
    }

}
