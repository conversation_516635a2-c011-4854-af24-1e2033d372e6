package com.qnvip.qwen.bizService.impl.task.processor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.enums.TaskOperateEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * TaskProcessorRegistry 类用于管理和执行任务处理器。 该类实现了 InitializingBean 接口，在 Spring 容器初始化完成后自动注册所有任务处理器。 通过任务步骤代码（step
 * code）来查找并执行对应的任务处理器。 如果找不到对应的任务处理器，将抛出 IllegalArgumentException 异常。
 */
@Slf4j
@Service
public class TaskProcessorRegistry implements InitializingBean {

    private static final Map<Integer, TaskProcessor> REGISTRY_MAP = new HashMap<>();

    @Resource
    private List<TaskProcessor> taskProcessors;

    @Override
    public void afterPropertiesSet() {
        taskProcessors.forEach(e -> REGISTRY_MAP.put(e.supportStep().getCode(), e));
    }

    /**
     * 根据任务步骤代码执行对应的任务处理器。
     *
     * @param task 任务进度模型，包含当前步骤信息。
     * @return
     * @throws IllegalArgumentException 如果找不到对应的任务处理器。
     */

    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        TaskProcessor taskProcessor = REGISTRY_MAP.get(task.getNowStep().getCode());
        if (taskProcessor == null) {
            String errorMessage = String.format("不支持的步骤：%s，任务ID：%s", task.getNowStep().getDesc(), task.getId());
            log.error(errorMessage);
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(), errorMessage);
        }
        return taskProcessor.execute(task);
    }

}
