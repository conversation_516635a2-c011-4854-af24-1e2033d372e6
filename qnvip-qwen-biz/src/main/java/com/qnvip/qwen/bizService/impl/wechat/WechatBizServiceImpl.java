package com.qnvip.qwen.bizService.impl.wechat;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.bizService.ApiSyncService;
import com.qnvip.qwen.bizService.WechatBizService;
import com.qnvip.qwen.dal.dto.ApiFileDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentListDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberHierarchicalDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberListDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberTreeDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatTokenDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatUserDTO;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月27日 16:59:00
 */
@Slf4j
@Service
public class WechatBizServiceImpl implements WechatBizService, ApiSyncService {

    private final String wechatToken = "ai_wechat_token";
    @Resource
    private RedisTemplate redisTemplate;
    @Value("${wechat.corpid:}")
    private String wechatCorPid;
    @Value("${wechat.secret:}")
    private String wechatSecret;

    @Override
    public WechatUserDTO getUserInfo(String code) {
        String token = getToken();
        Map<String, Object> map = new HashMap<>();
        map.put("access_token", token);
        map.put("code", code);
        String resp = HttpUtil.get("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo", map);
        log.info("获取用户信息返回结果:{}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw FrameworkException.instance("获取用户信息失败");
        }
        WechatUserDTO userIdDTO = JSONObject.parseObject(resp, WechatUserDTO.class);
        if (userIdDTO.getErrcode() != 0) {
            throw FrameworkException.instance("获取用户信息失败,errmsg:" + userIdDTO.getErrmsg());
        }
        String userId = userIdDTO.getUserId();
        String userTicket = userIdDTO.getUserTicket();
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("user_ticket", userTicket);
        String userResp =
            HttpUtil.post("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token=" + getToken(),
                JSON.toJSONString(userMap));
        log.info("获取用户详情返回结果:{}", userResp);
        if (StringUtils.isEmpty(userResp)) {
            throw FrameworkException.instance("获取用户信息失败");
        }
        WechatUserDTO userInfo = JSONObject.parseObject(userResp, WechatUserDTO.class);
        if (userInfo.getErrcode() != 0) {
            throw FrameworkException.instance("获取用户信息失败,errmsg:" + userIdDTO.getErrmsg());
        }
        return userInfo;
    }

    private String getToken() {
        String token = (String)redisTemplate.opsForValue().get(wechatToken);
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("corpid", wechatCorPid);
        map.put("corpsecret", wechatSecret);
        String result = HttpUtil.get("https://qyapi.weixin.qq.com/cgi-bin/gettoken", map);
        String errmsg = null;
        if (StringUtils.isNotEmpty(result)) {
            WechatTokenDTO resp = JSONObject.parseObject(result, WechatTokenDTO.class);
            errmsg = resp.getErrmsg();
            if (resp.getErrcode() == 0) {
                token = resp.getAccessToken();
                redisTemplate.opsForValue().set(wechatToken, resp.getAccessToken(), resp.getExpiresIn(),
                    TimeUnit.SECONDS);
                return token;
            }
        }
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("获取token异常," + errmsg);
        }
        return token;
    }

    @Override
    public WechatDepartmentListDTO getDepartmentList() {
        return getDepartmentList(getToken());
    }

    public WechatDepartmentListDTO getDepartmentList(String token) {
        Map<String, Object> map = new HashMap<>();
        map.put("access_token", token);
        String resp = HttpUtil.get("https://qyapi.weixin.qq.com/cgi-bin/department/list", map);
        log.info("获取部门列表返回结果:{}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw FrameworkException.instance("获取部门列表失败");
        }
        WechatDepartmentListDTO departmentList = JSONObject.parseObject(resp, WechatDepartmentListDTO.class);
        if (departmentList.getErrcode() != 0) {
            throw FrameworkException.instance("获取部门列表失败,errmsg:" + departmentList.getErrmsg());
        }
        return departmentList;
    }

    @Override
    public WechatDepartmentMemberListDTO getDepartmentMemberList(String token, Integer departmentId) {
        if (departmentId == null) {
            throw FrameworkException.instance("部门ID不能为空");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("access_token", token);
        map.put("department_id", departmentId);
        String resp = HttpUtil.get("https://qyapi.weixin.qq.com/cgi-bin/user/simplelist", map);
        log.info("获取部门成员列表返回结果:{}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw FrameworkException.instance("获取部门成员列表失败");
        }
        WechatDepartmentMemberListDTO memberList = JSONObject.parseObject(resp, WechatDepartmentMemberListDTO.class);
        if (memberList.getErrcode() != 0) {
            throw FrameworkException.instance("获取部门成员列表失败,errmsg:" + memberList.getErrmsg());
        }
        return memberList;
    }


    public List<WechatDepartmentMemberTreeDTO> getDepartmentMemberTree() {
        String token = getToken();

        List<WechatDepartmentMemberTreeDTO> treeList = new ArrayList<>();

        try {
            // 1. 获取所有部门列表
            WechatDepartmentListDTO departmentList = getDepartmentList(token);
            if (departmentList.getDepartment() == null || departmentList.getDepartment().isEmpty()) {
                log.warn("未获取到部门列表");
                return treeList;
            }

            // 2. 遍历每个部门，获取其成员列表
            for (WechatDepartmentDTO department : departmentList.getDepartment()) {
                WechatDepartmentMemberTreeDTO treeNode = new WechatDepartmentMemberTreeDTO();
                treeNode.setDepartment(department);

                try {
                    // 获取部门成员列表
                    WechatDepartmentMemberListDTO memberList = getDepartmentMemberList(token, department.getId());
                    if (memberList.getUserList() != null) {
                        treeNode.setMembers(memberList.getUserList());
                    } else {
                        treeNode.setMembers(new ArrayList<>());
                    }
                } catch (Exception e) {
                    log.error("获取部门 {} 成员列表失败: {}", department.getName(), e.getMessage());
                    treeNode.setMembers(new ArrayList<>());
                }

                treeList.add(treeNode);
            }

            log.info("成功构建部门和成员树形关系，共 {} 个部门", treeList.size());

        } catch (Exception e) {
            log.error("构建部门和成员树形关系失败", e);
            throw FrameworkException.instance("获取部门和成员树形关系失败: " + e.getMessage());
        }

        return treeList;
    }

    private List<WechatDepartmentMemberHierarchicalDTO>
        buildDepartmentTree(List<WechatDepartmentMemberTreeDTO> departmentMembers) {
        // 创建ID到DTO的映射
        Map<Integer, WechatDepartmentMemberHierarchicalDTO> idToDtoMap = new HashMap<>();

        // 第一遍遍历：创建所有部门DTO
        for (WechatDepartmentMemberTreeDTO dm : departmentMembers) {
            WechatDepartmentDTO department = dm.getDepartment();
            WechatDepartmentMemberHierarchicalDTO dto = new WechatDepartmentMemberHierarchicalDTO();
            dto.setId(department.getId());
            dto.setParent(department.getParentId());
            dto.setDepartmentName(department.getName());

            // 添加成员名字
            List<String> memberNames = new ArrayList<>();
            for (WechatDepartmentMemberDTO member : dm.getMembers()) {
                memberNames.add(member.getName());
            }
            dto.setMemberNames(memberNames);

            idToDtoMap.put(department.getId(), dto);
        }

        // 第二遍遍历：构建层级关系
        List<WechatDepartmentMemberHierarchicalDTO> rootDepartments = new ArrayList<>();
        for (WechatDepartmentMemberHierarchicalDTO dto : idToDtoMap.values()) {
            if (dto.getParent() == 0) {
                rootDepartments.add(dto);
            } else {
                WechatDepartmentMemberHierarchicalDTO parentDto = idToDtoMap.get(dto.getParent());
                if (parentDto != null) {
                    if (parentDto.getChildren() == null) {
                        parentDto.setChildren(new ArrayList<>());
                    }
                    parentDto.getChildren().add(dto);
                }
            }
        }

        return rootDepartments;
    }

    @Override
    public String formatAsProperties(List<WechatDepartmentMemberTreeDTO> departmentMembers) {
        List<WechatDepartmentMemberHierarchicalDTO> wechatDepartmentMemberHierarchicalDTOS =
            buildDepartmentTree(departmentMembers);
        StringBuilder sb = new StringBuilder();
        sb.append("# 部门和成员关系\n");
        sb.append("## :左边为各级部门名字，空格分隔不同部门 :右边为改部门的员工名字 ，下面数据只有部门名字和员工名字\n ");

        for (WechatDepartmentMemberHierarchicalDTO node : wechatDepartmentMemberHierarchicalDTOS) {
            formatNode(node, null, sb);
        }
        return sb.toString();
    }

    private void formatNode(WechatDepartmentMemberHierarchicalDTO node, String prefix, StringBuilder sb) {

        String currentPath = prefix == null ? sanitizeKey(node.getDepartmentName())
            : prefix + " " + sanitizeKey(node.getDepartmentName());

        if (node.getMemberNames() != null && !node.getMemberNames().isEmpty()) {
            sb.append(currentPath).append(": ").append(String.join(",", node.getMemberNames())).append("\n");
        }

        if (node.getChildren() != null) {
            for (WechatDepartmentMemberHierarchicalDTO child : node.getChildren()) {
                formatNode(child, currentPath, sb);
            }
        }
    }

    private String sanitizeKey(String name) {
        return name == null ? "(unnamed)" : name.replaceAll("\\s+", "_");
    }

    @Override
    public String bookName() {
        return "公司组织架构";
    }

    @Override
    public String tocName() {
        return "部门成员表";
    }

    @Override
    public ApiFileDTO getData() {
        List<WechatDepartmentMemberTreeDTO> departmentMemberTree = getDepartmentMemberTree();
        String data = formatAsProperties(departmentMemberTree);
        return new ApiFileDTO("部门成员表", data);
    }
}
