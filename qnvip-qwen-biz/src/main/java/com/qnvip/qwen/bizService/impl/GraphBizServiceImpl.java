package com.qnvip.qwen.bizService.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.qnvip.qwen.bizService.AIChatBizService;
import com.qnvip.qwen.bizService.GraphBizService;
import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dao.FileGraphRelationDaoService;
import com.qnvip.qwen.dal.dto.FilterOptionsDTO;
import com.qnvip.qwen.dal.dto.GraphKeywordDTO;
import com.qnvip.qwen.dal.dto.RagGraphEntityDTO;
import com.qnvip.qwen.dal.dto.RagGraphMutilDTO;
import com.qnvip.qwen.dal.dto.RagGraphRelationDTO;
import com.qnvip.qwen.dal.dto.graph.GraphEntity;
import com.qnvip.qwen.dal.dto.graph.GraphRelationship;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.dal.graph.Neo4jService;
import com.qnvip.qwen.dal.milvus.MilvusGraphEntityService;
import com.qnvip.qwen.dal.milvus.MilvusGraphRelationService;
import com.qnvip.qwen.enums.PromptEnum;
import com.qnvip.qwen.enums.RecallFromEnum;
import com.qnvip.qwen.service.FileRecallLogService;
import com.qnvip.qwen.util.rag.GraphUtil;
import com.qnvip.qwen.util.rag.LinearPollingUtils;
import com.qnvip.qwen.vo.request.DifyUserRecallReq;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GraphBizServiceImpl implements GraphBizService {

    private static final int DEFAULT_RELATED_CHUNK_NUMBER = 5;

    @Resource
    private FileChunkDaoService fileChunkDaoService;

    @Resource
    private FileRecallLogService fileRecallLogService;

    @Resource
    private AIChatBizService aiGenerateService;

    @Resource
    private Neo4jService neo4jService;

    @Resource
    private MilvusGraphEntityService milvusGraphEntityService;

    @Resource
    private MilvusGraphRelationService milvusGraphRelationService;

    @Resource
    private FileGraphRelationDaoService fileGraphRelationDaoService;

    @Override
    public List<String> graphRecall(DifyUserRecallReq req) {
        String keywordsJson = aiGenerateService
            .chatBlockingJsonByThird(PromptEnum.KEYWORDS_EXTRACTION.replace("{{inputText}}", req.getUserInput())
                .replace("{{examples}}", PromptEnum.KEYWORDS_EXTRACTION_EXAMPLES));
        GraphKeywordDTO keyword = JSON.parseObject(keywordsJson, GraphKeywordDTO.class);
        if (ObjectUtils.isEmpty(keyword.getLowLevelKeywords()) && ObjectUtils.isEmpty(keyword.getHighLevelKeywords())) {
            return new ArrayList<>();
        }

        RagGraphMutilDTO llData = getNodeData(req, keyword);

        RagGraphMutilDTO lhData = getEdgeData(req, keyword);

        // 合并实体和关系上下文(去重处理)
        List<GraphEntity> mergeEntities =
            mergeEntityByRoundRobinAndDeduplication(llData.getEntityDTOS(), lhData.getEntityDTOS());
        List<GraphRelationship> mergeRelations =
            mergeRelationByRoundRobinAndDeduplication(lhData.getRelationDTOS(), llData.getRelationDTOS());

        // 获取最相关实体的切块id
        List<String> entityChunkIds = findMostRelatedTextUnitFromEntities(mergeEntities);
        List<String> relationChunkIds = findRelatedTextUnitFromRelationships(mergeRelations, entityChunkIds);

        // 交替从两个列表
        List<String> resultChunkIds = mergeByRoundRobinAndDeduplication(entityChunkIds, relationChunkIds);

        if (CollUtil.isEmpty(entityChunkIds)) {
            log.warn("用户问题{} {}多路召回graph 未命中", req.getQuestionId(), req.getRecallType());
            return new ArrayList<>();
        }



        // 按切块顺序获取fileId
        List<FileChunkDO> fileChunkDOS = fileChunkDaoService.listFileOriginIdByUids(req.getBookIds(), resultChunkIds);

        // 取topN
        List<String> filterChunkIds = fileChunkDOS.stream().map(FileChunkDO::getUid)
            .limit(req.getRecallConfig().getGraphChunkIdFilter().getTopN()).collect(Collectors.toList());

        Map<String, Long> chunkUidFileIdMap =
            fileChunkDOS.stream().collect(Collectors.toMap(FileChunkDO::getUid, FileChunkDO::getFileOriginId));
        List<Long> orderedUniqueOriginIds = filterChunkIds.stream().map(chunkUidFileIdMap::get).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());

        // 保存多路召回的日志
        fileRecallLogService.saveMultChunkGraphRecall(req, req.getQuestionId(), orderedUniqueOriginIds, filterChunkIds,
            RecallFromEnum.MULT_GRAPH);

        return filterChunkIds;
    }

    private RagGraphMutilDTO getEdgeData(DifyUserRecallReq req, GraphKeywordDTO keyword) {
        if (ObjectUtils.isEmpty(keyword) || ObjectUtils.isEmpty(keyword.getHighLevelKeywords())) {
            return new RagGraphMutilDTO();
        }
        List<RagGraphRelationDTO> queryRelations = milvusGraphRelationService.search(req.getBookIds(),
            req.getFileOriginIds(), keyword.toHighLevelKeywordsStr(), req.getRecallConfig().getGraphVectorFilter(),
            req.getExcludedChunkIds());

        List<GraphRelationship> graphRelationships = convertToGraphRelationships(queryRelations);

        Map<String, GraphRelationship> edgeDataDict =
            neo4jService.getEdgesBatch(req.getBookIds(), req.getExcludedChunkIds(), graphRelationships);

        List<GraphRelationship> edgeDatas = new ArrayList<>();
        for (GraphRelationship k : graphRelationships) {
            String key = GraphUtil.generateEdgeKey(k.getSourceEntity(), k.getTargetEntity());
            GraphRelationship edgeProps = edgeDataDict.get(key);
            if (edgeProps != null) {
                edgeDatas.add(edgeProps);
            }
        }

        List<GraphEntity> useEntities = findMostRelatedEntitiesFromRelationships(req.getBookIds(),
            edgeDatas, req.getExcludedChunkIds(), req.getRecallConfig().getGraphVectorFilter());

        RagGraphMutilDTO ragGraphMutilDTO = new RagGraphMutilDTO();
        ragGraphMutilDTO.setRelationDTOS(edgeDatas);
        ragGraphMutilDTO.setEntityDTOS(useEntities);

        return ragGraphMutilDTO;
    }

    public List<GraphEntity> findMostRelatedEntitiesFromRelationships(List<Long> bookIds,
        List<GraphRelationship> edgeDatas, List<String> excludedChunkIds, FilterOptionsDTO graphVectorFilter) {

        List<String> entityNames = new ArrayList<>();
        Set<String> seen = new HashSet<>();

        for (GraphRelationship e : edgeDatas) {
            if (e.getSourceEntity() != null && seen.add(e.getSourceEntity())) {
                entityNames.add(e.getSourceEntity());
            }
            if (e.getTargetEntity() != null && seen.add(e.getTargetEntity())) {
                entityNames.add(e.getTargetEntity());
            }
        }

        // 批量获取节点数据
        Map<String, GraphEntity> nodesDict = neo4jService.getNodesBatch(bookIds, excludedChunkIds, entityNames);

        // 按顺序重建结果列表
        List<GraphEntity> nodeDatas = new ArrayList<>();
        for (String entityName : entityNames) {
            GraphEntity node = nodesDict.get(entityName);
            if (node == null) {
                System.out.println("Node '" + entityName + "' not found in batch retrieval.");
                continue;
            }
            // entityName 字段已在 GraphEntity 中，无需额外合并
            nodeDatas.add(node);
        }

        return nodeDatas;

    }

    public List<GraphRelationship> convertToGraphRelationships(List<RagGraphRelationDTO> dtoList) {
        List<GraphRelationship> result = new ArrayList<>();
        for (RagGraphRelationDTO dto : dtoList) {
            GraphRelationship relationship = new GraphRelationship();
            relationship.setSourceEntity(dto.getSrcId());
            relationship.setTargetEntity(dto.getTgtId());
            relationship.setBookId(dto.getBookId() == null ? null : Collections.singletonList(dto.getBookId()));
            relationship.setFileOriginId(
                dto.getFileOriginId() == null ? null : Collections.singletonList(dto.getFileOriginId()));
            relationship
                .setFileChunkUid(dto.getChunkUid() == null ? null : Collections.singletonList(dto.getChunkUid()));
            relationship.setRelationshipKeyword(
                dto.getKeywords() == null ? null : Collections.singletonList(dto.getKeywords()));
            // rank 和 relationshipStrength 可根据需要赋值，这里默认不赋值
            result.add(relationship);
        }
        return result;
    }

    /**
     * 给实体填入度数
     * 
     * @param req
     * @param keyword
     * @return
     */
    private RagGraphMutilDTO getNodeData(DifyUserRecallReq req, GraphKeywordDTO keyword) {
        if (ObjectUtils.isEmpty(keyword) || ObjectUtils.isEmpty(keyword.getLowLevelKeywords())) {
            return new RagGraphMutilDTO();
        }
        List<RagGraphEntityDTO> queryEntities =
            milvusGraphEntityService.search(req.getBookIds(), req.getFileOriginIds(), keyword.toLowLevelKeywordsStr(),
                req.getRecallConfig().getGraphChunkIdFilter(), req.getExcludedChunkIds());

        Map<String, GraphEntity> nodesBatch = neo4jService
            .getNodesBatch(req.getBookIds(), req.getExcludedChunkIds(),
                queryEntities.stream().map(RagGraphEntityDTO::getEntityName).collect(Collectors.toList()));

        Map<String, Integer> nameCountMap = neo4jService.nodeDegreesBatch(req.getBookIds(), req.getExcludedChunkIds(),
            queryEntities.stream().map(RagGraphEntityDTO::getEntityName).distinct().collect(Collectors.toList()));

        Set<String> names = new HashSet<>();
        List<GraphEntity> llEntity = new ArrayList<>();
        for (RagGraphEntityDTO queryEntity : queryEntities) {
            if (names.contains(queryEntity.getEntityName())) {
                continue;
            }
            names.add(queryEntity.getEntityName());;
            GraphEntity graphEntity = nodesBatch.get(queryEntity.getEntityName());
            if (graphEntity == null) {
                continue;
            }
            graphEntity.setGraphScore(nameCountMap.getOrDefault(queryEntity.getEntityName(), 1));
            graphEntity.setVdbScore(queryEntity.getVdbScore());
            llEntity.add(graphEntity);
        }

        // 取出实体的边和对应节点，并且计数和内部排序
        List<GraphRelationship> llRelation =
            findMostRelatedEdgesFromEntities(req.getBookIds(), req.getExcludedChunkIds(), queryEntities);

        RagGraphMutilDTO ragGraphMutilDTO = new RagGraphMutilDTO();
        ragGraphMutilDTO.setEntityDTOS(llEntity);
        ragGraphMutilDTO.setRelationDTOS(llRelation);
        return ragGraphMutilDTO;
    }



    public List<GraphRelationship> findMostRelatedEdgesFromEntities(List<Long> bookIds, List<String> excludedChunkIds,
        List<RagGraphEntityDTO> nodeDatas) {
        // 1. 从输入实体数据中提取所有节点名称
        List<String> nodeNames = nodeDatas.stream().map(RagGraphEntityDTO::getEntityName).collect(Collectors.toList());

        // Step 2: 获取每个节点对应的边（Map<String, List<Map<String, String>>>）
        Map<String, List<GraphRelationship>> batchEdgesDict =
            neo4jService.getNodesEdgesBatch(bookIds, excludedChunkIds, nodeNames);

        // Step 3: 去重边集合（使用 src 和 tgt 排序后的 key 作为唯一性标识）
        Set<String> seen = new HashSet<>();
        List<GraphRelationship> allEdges = new ArrayList<>();

        for (String nodeName : nodeNames) {
            List<GraphRelationship> edges = batchEdgesDict.getOrDefault(nodeName, Collections.emptyList());
            for (GraphRelationship edge : edges) {
                String src = edge.getSourceEntity();
                String tgt = edge.getTargetEntity();
                if (src == null || tgt == null) {
                    continue;
                }

                // 排序后的 key 保证边唯一（无向边）
                String key = GraphUtil.generateEdgeKey(src, tgt);
                if (seen.add(key)) {
                    GraphRelationship sortedEdge = sortEdgeMap(src, tgt);
                    allEdges.add(sortedEdge);
                }
            }
        }

        // Step 4: 批量查询边属性和边rank
        Map<String, GraphRelationship> edgeDataDict = fileGraphRelationDaoService.mapEdgeRelationship(bookIds,
                excludedChunkIds,  allEdges.stream().map(edge -> GraphUtil.generateEdgeKey(edge.getSourceEntity(), edge.getTargetEntity()))
                        .collect(Collectors.toSet()));
//        Map<String, GraphRelationship> edgeDataDict = neo4jService.getEdgesBatch(bookIds, excludedChunkIds, allEdges);
        Map<String, Integer> edgeDegreesDict = edgeDegreesBatch(bookIds, excludedChunkIds, allEdges);




        // Step 5: 构建结果对象
        List<GraphRelationship> result = new ArrayList<>();

        for (GraphRelationship edge : allEdges) {
            String key = GraphUtil.generateEdgeKey(edge.getSourceEntity(), edge.getTargetEntity());
            GraphRelationship props = edgeDataDict.get(key);
            if (props != null) {
                int rank = edgeDegreesDict.getOrDefault(key, 0);

                GraphRelationship rel = new GraphRelationship();
                rel.setSourceEntity(edge.getSourceEntity());
                rel.setTargetEntity(edge.getTargetEntity());
                rel.setRank(rank);
                rel.setRelationshipStrength(weights);
                result.add(rel);
            }
        }

        // Step 6: 排序：rank + strength 降序
        result.sort(Comparator.comparingInt(GraphRelationship::getRank).reversed()
            .thenComparing(GraphRelationship::getRelationshipStrength, Comparator.reverseOrder()));

        return result;
    }

    private GraphRelationship sortEdgeMap(String a, String b) {
        GraphRelationship edge = new GraphRelationship();
        if (a.compareTo(b) <= 0) {
            edge.setSourceEntity(a);
            edge.setTargetEntity(b);
        } else {
            edge.setSourceEntity(b);
            edge.setTargetEntity(a);
        }
        return edge;
    }

    /**
     * 交替合并去重获取元素
     * 
     * @param entityChunkIds
     * @param relationChunkIds
     * @return
     */
    public List<String> mergeByRoundRobinAndDeduplication(List<String> entityChunkIds, List<String> relationChunkIds) {
        List<String> result = new ArrayList<>();
        Set<String> seen = new HashSet<>(); // 用于跟踪已添加的元素
        int maxLength = Math.max(entityChunkIds.size(), relationChunkIds.size());

        for (int i = 0; i < maxLength; i++) {
            // 处理实体元素
            if (i < entityChunkIds.size()) {
                String entityId = entityChunkIds.get(i);
                // 仅添加未重复的元素
                if (!seen.contains(entityId)) {
                    seen.add(entityId);
                    result.add(entityId);
                }
            }

            // 处理关系元素
            if (i < relationChunkIds.size()) {
                String relationId = relationChunkIds.get(i);
                // 仅添加未重复的元素
                if (!seen.contains(relationId)) {
                    seen.add(relationId);
                    result.add(relationId);
                }
            }
        }

        return result;
    }

    public List<GraphEntity> mergeEntityByRoundRobinAndDeduplication(List<GraphEntity> llEntity,
        List<GraphEntity> lhEntity) {
        List<GraphEntity> result = new ArrayList<>();
        Set<String> seen = new HashSet<>(); // 用于跟踪已添加的元素
        int maxLength = Math.max(llEntity.size(), lhEntity.size());

        for (int i = 0; i < maxLength; i++) {
            if (i < llEntity.size()) {
                String entityId = llEntity.get(i).getEntityName();
                // 仅添加未重复的元素
                if (!seen.contains(entityId)) {
                    seen.add(entityId);
                    result.add(llEntity.get(i));
                }
            }

            if (i < lhEntity.size()) {
                String relationId = lhEntity.get(i).getEntityName();
                // 仅添加未重复的元素
                if (!seen.contains(relationId)) {
                    seen.add(relationId);
                    result.add(lhEntity.get(i));
                }
            }
        }
        return result;
    }

    public List<GraphRelationship> mergeRelationByRoundRobinAndDeduplication(List<GraphRelationship> lhRelation,
        List<GraphRelationship> llRelation) {
        List<GraphRelationship> result = new ArrayList<>();
        Set<String> seen = new HashSet<>(); // 用于跟踪已添加的元素
        int maxLength = Math.max(lhRelation.size(), llRelation.size());

        for (int i = 0; i < maxLength; i++) {
            if (i < lhRelation.size()) {
                String entityId = lhRelation.get(i).getSourceEntity() + "_" + lhRelation.get(i).getSourceEntity();
                // 仅添加未重复的元素
                if (!seen.contains(entityId)) {
                    seen.add(entityId);
                    result.add(lhRelation.get(i));
                }
            }

            if (i < llRelation.size()) {
                String relationId = llRelation.get(i).getSourceEntity() + "_" + llRelation.get(i).getSourceEntity();
                // 仅添加未重复的元素
                if (!seen.contains(relationId)) {
                    seen.add(relationId);
                    result.add(llRelation.get(i));
                }
            }
        }
        return result;
    }

    private List<String> findRelatedTextUnitFromRelationships(List<GraphRelationship> edgeDatas,
        List<String> entityChunk) {
        if (edgeDatas == null || edgeDatas.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 1: 提取每条关系的 text chunks
        List<Map<String, Object>> relationsWithChunks = new ArrayList<>();
        for (GraphRelationship relation : edgeDatas) {
            if (!ObjectUtils.isEmpty(relation.getFileChunkUid())) {
                List<String> chunks = relation.getFileChunkUid();
                if (!chunks.isEmpty()) {
                    String relationKey =
                        GraphUtil.generateEdgeKey(relation.getSourceEntity(), relation.getTargetEntity());

                    Map<String, Object> relMap = new HashMap<>();
                    relMap.put("relation_key", relationKey);
                    relMap.put("chunks", new ArrayList<>(chunks));
                    relMap.put("relation_data", relation);
                    relationsWithChunks.add(relMap);
                }
            }
        }

        if (relationsWithChunks.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 2: 去重并统计 chunk 出现频率
        Map<String, Integer> chunkOccurrenceCount = new HashMap<>();
        for (Map<String, Object> relationInfo : relationsWithChunks) {
            List<String> chunks = (List<String>)relationInfo.get("chunks");
            List<String> dedupedChunks = new ArrayList<>();
            for (String chunkId : chunks) {
                int count = chunkOccurrenceCount.getOrDefault(chunkId, 0) + 1;
                chunkOccurrenceCount.put(chunkId, count);
                if (count == 1) {
                    dedupedChunks.add(chunkId);
                }
            }
            relationInfo.put("chunks", dedupedChunks);
        }

        // Step 3: 根据频次降序排序 chunk
        for (Map<String, Object> relationInfo : relationsWithChunks) {
            List<String> chunks = (List<String>)relationInfo.get("chunks");
            List<String> sortedChunks =
                chunks.stream().sorted((a, b) -> Integer.compare(chunkOccurrenceCount.getOrDefault(b, 0),
                    chunkOccurrenceCount.getOrDefault(a, 0))).collect(Collectors.toList());
            relationInfo.put("sorted_chunks", sortedChunks);
        }

        // Step 4: 应用加权轮询
        List<String> selectedChunkIds =
            LinearPollingUtils.linearGradientWeightedPolling(relationsWithChunks, DEFAULT_RELATED_CHUNK_NUMBER, 1);

        if (selectedChunkIds.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 4.5: 和 entity chunks 去重
        if (!ObjectUtils.isEmpty(entityChunk)) {
            Set<String> entityChunkIds = entityChunk.stream().filter(Objects::nonNull).collect(Collectors.toSet());

            selectedChunkIds =
                selectedChunkIds.stream().filter(id -> !entityChunkIds.contains(id)).collect(Collectors.toList());

            if (selectedChunkIds.isEmpty()) {
                return Collections.emptyList();
            }
        }

        // Step 5: 批量获取 chunk 数据（去重保持顺序）
        return selectedChunkIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取实体最相关的切块id 方法通过实体分块提取、去重、频次排序和线性加权轮询，公平高效地选出最具代表性的实体关联文本块。
     * 
     * @param nodeDatas
     * @return
     */
    private List<String> findMostRelatedTextUnitFromEntities(List<GraphEntity> nodeDatas) {
        if (nodeDatas == null || nodeDatas.isEmpty()) {
            return Collections.emptyList();
        }

        // 步骤1：收集每个实体的所有文本块
        List<Map<String, Object>> entitiesWithChunks = new ArrayList<>();
        for (GraphEntity entity : nodeDatas) {
            if (!ObjectUtils.isEmpty(entity.getFileChunkUid())) {
                List<String> chunks = entity.getFileChunkUid();
                if (!chunks.isEmpty()) {
                    Map<String, Object> entry = new HashMap<>();
                    entry.put("entity_name", entity.getEntityName());
                    entry.put("chunks", new ArrayList<>(chunks));
                    entry.put("entity_data", entity);
                    entitiesWithChunks.add(entry);
                }
            }
        }

        if (entitiesWithChunks.isEmpty()) {
            return Collections.emptyList();
        }

        // 步骤2：统计块出现次数并消除重复
        Map<String, Integer> chunkOccurrenceCount = new HashMap<>();
        for (Map<String, Object> entityInfo : entitiesWithChunks) {
            List<String> chunks = (List<String>)entityInfo.get("chunks");
            List<String> deduplicatedChunks = new ArrayList<>();

            for (String chunkId : chunks) {
                int count = chunkOccurrenceCount.getOrDefault(chunkId, 0) + 1;
                chunkOccurrenceCount.put(chunkId, count);
                if (count == 1) {
                    deduplicatedChunks.add(chunkId);
                }
            }

            entityInfo.put("chunks", deduplicatedChunks);
        }

        // 步骤3：按出现次数对块进行排序
        for (Map<String, Object> entityInfo : entitiesWithChunks) {
            List<String> chunks = (List<String>)entityInfo.get("chunks");
            List<String> sortedChunks =
                chunks.stream().sorted((a, b) -> Integer.compare(chunkOccurrenceCount.getOrDefault(b, 0),
                    chunkOccurrenceCount.getOrDefault(a, 0))).collect(Collectors.toList());
            entityInfo.put("sorted_chunks", sortedChunks);
        }

        // 步骤4：线性梯度加权轮询
        List<String> selectedChunkIds =
            LinearPollingUtils.linearGradientWeightedPolling(entitiesWithChunks, DEFAULT_RELATED_CHUNK_NUMBER, 1);

        if (selectedChunkIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 步骤5：在保持秩序的同时进行重复数据删除
        return selectedChunkIds.stream().distinct().collect(Collectors.toList());
    }

    public Map<String, Integer> edgeDegreesBatch(List<Long> bookIds, List<String> excludedChunkIds,
        List<GraphRelationship> edgePairs) {
        // Step 1: 收集所有唯一节点 ID
        Set<String> uniqueNodeIds = edgePairs.stream()
            .flatMap(edge -> Stream.of(edge.getSourceEntity(), edge.getTargetEntity())).collect(Collectors.toSet());

        // Step 2: 查询所有节点的度数
        Map<String, Integer> nodeDegrees =
            neo4jService.nodeDegreesBatch(bookIds, excludedChunkIds, new ArrayList<>(uniqueNodeIds));

        // Step 3: 为每条边计算度数之和
        Map<String, Integer> edgeDegrees = new HashMap<>();
        for (GraphRelationship edge : edgePairs) {
            String src = edge.getSourceEntity();
            String tgt = edge.getTargetEntity();
            int srcDegree = nodeDegrees.getOrDefault(src, 0);
            int tgtDegree = nodeDegrees.getOrDefault(tgt, 0);
            String key = GraphUtil.generateEdgeKey(src, tgt);
            edgeDegrees.put(key, srcDegree + tgtDegree);
        }

        return edgeDegrees;
    }

}
