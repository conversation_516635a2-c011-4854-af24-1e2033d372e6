package com.qnvip.qwen.bizService;

import java.util.List;

import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentListDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberListDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatDepartmentMemberTreeDTO;
import com.qnvip.qwen.dal.dto.wechat.WechatUserDTO;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月27日 17:00:00
 */
public interface WechatBizService {
    WechatUserDTO getUserInfo(String code);

    /**
     * 获取企业微信部门列表
     * @return 部门列表信息
     */
    WechatDepartmentListDTO getDepartmentList();


    /**
     * 获取部门成员列表
     * @param token
     * @param departmentId
     * @return
     */
    WechatDepartmentMemberListDTO getDepartmentMemberList(String token, Integer departmentId);


    /**
     * 获取企业微信部门和成员树形关系
     * @return 部门和成员的二级关系列表
     */
    List<WechatDepartmentMemberTreeDTO> getDepartmentMemberTree();

    /**
     * 格式化企业和部门关系
     * 
     * @param
     * @return
     */
    String formatAsProperties(List<WechatDepartmentMemberTreeDTO> departmentMembers);
}
