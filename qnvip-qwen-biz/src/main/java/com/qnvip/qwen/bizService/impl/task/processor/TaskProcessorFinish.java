package com.qnvip.qwen.bizService.impl.task.processor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskProcessorFinish extends BaseTaskProcessor implements TaskProcessor {
    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_FINISH;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        recordData(task, "finish");
        return TaskOperateEnum.FINISH;
    }

}