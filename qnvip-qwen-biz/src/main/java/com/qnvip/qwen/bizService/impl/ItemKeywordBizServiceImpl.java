package com.qnvip.qwen.bizService.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.bizService.ItemKeywordBizService;
import com.qnvip.qwen.dal.dto.FileChunkRerankDTO;
import com.qnvip.qwen.dal.dto.ItemNameRerankDTO;
import com.qnvip.qwen.dal.dto.open.ItemKeywordDTO;
import com.qnvip.qwen.dal.entity.open.ItemKeywordDO;
import com.qnvip.qwen.dal.es.EsItemKeywordService;
import com.qnvip.qwen.dal.es.esdo.ItemKeywordESDO;
import com.qnvip.qwen.dal.milvus.MilvusItemKeywordService;
import com.qnvip.qwen.enums.OpenPlatformEnum;
import com.qnvip.qwen.service.open.ItemKeywordService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年05月22日 17:05:00
 */
@Slf4j
@Service
public class ItemKeywordBizServiceImpl implements ItemKeywordBizService {

    @Resource
    private MilvusItemKeywordService milvusItemKeywordService;
    @Resource
    private EsItemKeywordService esItemKeywordService;
    @Resource
    private ItemKeywordService itemKeywordService;

    @Value("${rerank.host:}")
    private String rerankHost;

    @Value("${open.host.rent:}")
    private String openRentHost;

    @Value("${open.host.merchant:}")
    private String openMerchantHost;

    /**
     * 同步业务关键词
     */
    @Override
    public void syncKeyword() {
        // 租赁
        processBizKeywords(OpenPlatformEnum.RENT.getCode());
        // 分期
        processBizKeywords(OpenPlatformEnum.MERCHANT.getCode());
    }

    /**
     * 处理租赁业务关键词
     */
    private void processBizKeywords(Long platformId) {
        String url = null;
        if (platformId.equals(OpenPlatformEnum.RENT.getCode())) {
            url = openRentHost + "itemKeyword/selectNameList";
        } else if (platformId.equals(OpenPlatformEnum.MERCHANT.getCode())) {
            url = openMerchantHost + "open/ai/itemKeyword/selectNameList";
        }
        String resp = null;
        try {
            resp = HttpUtil.post(url, new HashMap<>());
            log.info("=====同步业务关键词返回=====resp:{}, platformId:{}", resp, platformId);
            if (StringUtils.isEmpty(resp)) {
                log.error("=====同步业务关键词返回为空=====platformId:{}", platformId);
                return;
            }
        } catch (Exception e) {
            log.error("=====同步业务关键词异常======platformId:{}", platformId);
        }

        JSONObject jo = JSONUtil.parseObj(resp);
        int code = jo.getInt("code");
        String message = jo.getStr("message");
        if (code != 0) {
            log.error("=====同步业务关键词错误=====platformId:{}, message:{}", platformId, message);
        }
        List<String> keywords = jo.getJSONArray("data").toList(String.class);
        if (CollUtil.isNotEmpty(keywords)) {
            processBatchKeywords(keywords, platformId);
            log.info("同步业务关键词完成，共处理{}个关键词", keywords.size());
        }
    }

    /**
     * 处理批量关键词
     * 
     * @param batchKeywords 批量关键词
     * @param platformId 平台ID
     */
    private void processBatchKeywords(List<String> batchKeywords, Long platformId) {
        if (CollUtil.isEmpty(batchKeywords) || platformId == null) {
            log.warn("批量关键词或平台ID为空，跳过处理");
            return;
        }

        try {
            // 1. 获取数据库中现有关键词
            List<ItemKeywordDO> existingKeywords = itemKeywordService.getByPlatformId(platformId);
            Set<String> existingKeywordSet =
                existingKeywords.stream().map(ItemKeywordDO::getKeyword).collect(Collectors.toSet());

            // 2. 计算新增和删除的关键词
            Set<String> incomingKeywordSet = new HashSet<>(batchKeywords);
            Set<String> newKeywords = new HashSet<>(incomingKeywordSet);
            newKeywords.removeAll(existingKeywordSet);

            Set<String> deletedKeywords = new HashSet<>(existingKeywordSet);
            deletedKeywords.removeAll(incomingKeywordSet);

            // 3. 处理数据库变更
            processDbChanges(platformId, newKeywords, deletedKeywords, existingKeywords);

        } catch (Exception e) {
            log.error("处理批量关键词失败，平台ID：{}，批次大小：{}，错误：{}", platformId, batchKeywords.size(), e.getMessage(), e);
            throw FrameworkException.instance("处理批量关键词失败: " + e.getMessage());
        }
    }

    /**
     * 处理数据库、ES和Milvus的变更
     */
    private void processDbChanges(Long platformId, Set<String> newKeywords, Set<String> deletedKeywords,
        List<ItemKeywordDO> existingKeywords) {
        // 1. 处理新增关键词
        List<ItemKeywordDO> newKeywordDOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(newKeywords)) {
            List<ItemKeywordDO> newKeywordList = newKeywords.stream().map(keyword -> {
                ItemKeywordDO keywordDO = new ItemKeywordDO();
                keywordDO.setKeyword(keyword);
                keywordDO.setPlatformId(platformId);
                return keywordDO;
            }).collect(Collectors.toList());

            // 批量保存到数据库
            itemKeywordService.saveBatch(newKeywordList);

            // 获取包含ID的完整对象列表
            newKeywordDOList = itemKeywordService.getByPlatformId(platformId).stream()
                .filter(item -> newKeywords.contains(item.getKeyword())).collect(Collectors.toList());

            log.info("新增关键词数量: {}, platformId: {}", newKeywords.size(), platformId);

            // 2. 同步新增关键词到ES和Milvus
            syncNewKeywordsToEsAndMilvus(newKeywordDOList);
        }

        // 3. 处理删除关键词
        List<Long> deleteIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(deletedKeywords)) {
            deleteIds = existingKeywords.stream().filter(item -> deletedKeywords.contains(item.getKeyword()))
                .map(ItemKeywordDO::getId).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(deleteIds)) {
                // 从数据库中删除
                itemKeywordService.removeByIds(deleteIds);

                // 4. 从ES和Milvus中删除
                syncDeleteKeywordsFromEsAndMilvus(deleteIds);

                log.info("删除关键词数量: {}, platformId: {}", deleteIds.size(), platformId);
            }
        }

        // 5. 记录总体同步情况
        if (CollUtil.isNotEmpty(newKeywords) || CollUtil.isNotEmpty(deletedKeywords)) {
            log.info("同步ES和Milvus完成，新增: {}, 删除: {}, platformId: {}", newKeywordDOList.size(), deleteIds.size(),
                platformId);
        } else {
            log.info("无需同步数据，platformId: {}", platformId);
        }
    }

    /**
     * 同步新增关键词到ES和Milvus
     */
    private void syncNewKeywordsToEsAndMilvus(List<ItemKeywordDO> newKeywordDOList) {
        if (CollUtil.isEmpty(newKeywordDOList)) {
            return;
        }
        List<ItemKeywordDTO> milvusList = new ArrayList<>();
        List<ItemKeywordESDO> esList = new ArrayList<>();
        for (ItemKeywordDO newKeyword : newKeywordDOList) {
            // 同步到ES
            ItemKeywordESDO esdo = new ItemKeywordESDO();
            esdo.setId(newKeyword.getId());
            esdo.setPlatformId(newKeyword.getPlatformId());
            esdo.setName(newKeyword.getKeyword());
            esList.add(esdo);

            // 同步到Milvus
            ItemKeywordDTO dto = new ItemKeywordDTO();
            dto.setId(newKeyword.getId());
            dto.setPlatformId(newKeyword.getPlatformId());
            dto.setKeyword(newKeyword.getKeyword());
            milvusList.add(dto);

        }
        // 同步到ES
        if (CollUtil.isNotEmpty(esList)) {
            esItemKeywordService.batchSaveOrUpdate(esList);
        }

        // 同步到Milvus
        if (CollUtil.isNotEmpty(milvusList)) {
            milvusItemKeywordService.insert(milvusList);
        }

    }

    /**
     * 从ES和Milvus中删除关键词
     */
    private void syncDeleteKeywordsFromEsAndMilvus(List<Long> deleteIds) {
        if (CollUtil.isEmpty(deleteIds)) {
            return;
        }

        try {
            // 删除ES中的关键词
            boolean esResult = esItemKeywordService.deleteByIds(deleteIds);
            if (!esResult) {
                log.warn("从ES中删除关键词失败，IDs: {}", deleteIds);
            }
        } catch (Exception e) {
            log.error("从ES中删除关键词异常: {}", e.getMessage(), e);
        }

        try {
            // 删除Milvus中的关键词
            milvusItemKeywordService.deleteByIds(deleteIds);
        } catch (Exception e) {
            log.error("从Milvus中删除关键词异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 业务查询关键词
     * 
     * @param query
     * @return
     */
    @Override
    public ResultVO search(String query, Long platformId) {
        Set<String> names = new HashSet<>();

        List<ItemKeywordESDO> list = esItemKeywordService.search(platformId, query, 20);
        if (CollUtil.isNotEmpty(list)) {
            names.addAll(list.stream().map(ItemKeywordESDO::getName).collect(Collectors.toList()));
        }
        List<Long> ids = milvusItemKeywordService.search(platformId, query, 20);
        if (CollUtil.isNotEmpty(ids)) {
            List<ItemKeywordDO> keywordList = itemKeywordService.getListByIds(ids);
            if (CollUtil.isNotEmpty(keywordList)) {
                names.addAll(keywordList.stream().map(ItemKeywordDO::getKeyword).collect(Collectors.toList()));
            }
        }

        List<String> nameList = new ArrayList<>(names);
        List<BigDecimal> scores = executeRerankApi(nameList, query);
        if (names.size() != scores.size()) {
            throw FrameworkException.instance("两个列表长度不一致");
        }

        List<ItemNameRerankDTO> result = new ArrayList<>();
        // 塞入rerank评分
        for (int i = 0; i < nameList.size(); i++) {
            String name = nameList.get(i);
            BigDecimal score = scores.get(i);

            ItemNameRerankDTO dto = new ItemNameRerankDTO();
            dto.setName(name);
            dto.setScore(score);
            result.add(dto);
        }

        result = result.stream().sorted(Comparator.comparing(ItemNameRerankDTO::getScore).reversed()).limit(1)
            .collect(Collectors.toList());
        return ResultVO.of(result);
    }

    /**
     * 调用rerank模型
     *
     * @param chunkList
     * @return
     */
    private List<BigDecimal> executeRerankApi(List<String> chunkList, String query) {
        if (CollUtil.isEmpty(chunkList)) {
            return new ArrayList<>();
        }

        List<FileChunkRerankDTO> list = new ArrayList<>();
        for (String str : chunkList) {
            FileChunkRerankDTO dto = new FileChunkRerankDTO();
            dto.setQuery(query);
            dto.setFileOriginId(1L);
            dto.setChunkUid("123");
            dto.setContent(str);
            list.add(dto);
        }

        try {
            log.info("=====重排序请求=====req:{}", JSONUtil.toJsonStr(list));
            String resp = HttpUtil.post(rerankHost + "/rerank", JSONUtil.toJsonStr(list));
            log.info("=====重排序返回=====resp:{}", resp);
            if (StringUtils.isEmpty(resp)) {
                log.error("=====重排序范围内容为空=====processContent:{}", list);
            }
            return JSONUtil.toList(resp, BigDecimal.class);
        } catch (Exception e) {
            throw FrameworkException.instance("重排序失败");
        }

    }
}
