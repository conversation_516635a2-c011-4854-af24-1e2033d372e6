package com.qnvip.qwen.bizService.impl.task.processor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.bizService.AIChatBizService;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dao.FileQaDaoService;
import com.qnvip.qwen.dal.dao.FileQaDataDaoService;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.entity.FileQaDataDO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.dal.milvus.MilvusQaLinkService;
import com.qnvip.qwen.enums.PromptEnum;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.util.SequenceUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档结构化
 */
@Service
@Slf4j
// @Transactional(rollbackFor = Exception.class)
public class TaskProcessorQA extends BaseTaskProcessor implements TaskProcessor {
    protected static final ExecutorService executor =
        new ThreadPoolExecutor(10, 15, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("TaskProcessorQA-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    @Resource
    private FileQaDaoService fileQaDaoService;
    @Resource
    private FileQaDataDaoService fileQaDataDaoService;
    @Resource
    private FileChunkDataDaoService fileChunkDataDaoService;
    @Resource
    private AIChatBizService aiChatBizService;
    @Resource
    private MilvusQaLinkService milvusQaLinkService;

    @Resource
    private TeamDaoService teamDaoService;

    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_QA;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        FileOriginDO fileOriginDO = task.getFileOriginDO();
        Long fileOriginId = fileOriginDO.getId();
        Long teamId = fileOriginDO.getTeamId();
        Long bookId = fileOriginDO.getBookId();
        String tocs = fileOriginDO.getTocs();

        TeamDO teamDO = teamDaoService.getById(teamId);
        if (teamDO == null) {
            log.warn("=====查不到团队信息===task:{}", JSONUtil.toJsonStr(task));
            return TaskOperateEnum.CONTINUE;
        }

        // 删除qa信息
        fileQaDaoService.deleteByFileOriginId(fileOriginId);
        fileQaDataDaoService.deleteByFileOriginId(fileOriginId);
        // 删除milvus数据
        milvusQaLinkService.deleteByFileOriginId(fileOriginId);


        List<FileChunkDataDO> fileChunkDOList = fileChunkDataDaoService.getListByFileOriginId(fileOriginId);
        if (CollUtil.isNotEmpty(fileChunkDOList)) {
            List<CompletableFuture> futures = new ArrayList<>();
            for (FileChunkDataDO d : fileChunkDOList) {
                String finalTocs = tocs;
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    String prompt = PromptEnum.CHUNK_QA.replace("{article}", d.getData());
                    int retryCount = 0;
                    boolean success = false;

                    // 重试逻辑，最多尝试3次
                    while (retryCount < 3 && !success) {
                        try {
                            String resp = aiChatBizService.chatBlockingJsonByThird(prompt);
                            List<FileQaDataDO> dataList = JSONUtil.toList(resp, FileQaDataDO.class);
                            if (CollUtil.isNotEmpty(dataList)) {
                                saveQaData(dataList, fileOriginId, d.getChunkUid(), teamId, bookId, finalTocs);
                            }
                            success = true;
                        } catch (Exception e) {
                            retryCount++;
                            if (retryCount < 3) {
                                try {
                                    // 等待一段时间后再重试，例如1秒
                                    Thread.sleep(1000);
                                } catch (InterruptedException interruptedException) {
                                    Thread.currentThread().interrupt();
                                    log.error("线程被中断", interruptedException);
                                }
                            }
                            log.error("qa数据解析异常,chunkUid{}, 重试次数:{}", d.getChunkUid(), retryCount, e);
                        }
                    }
                }, executor);
                futures.add(completableFuture);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }

        return TaskOperateEnum.CONTINUE;
    }

    /**
     * 保存问答数据
     *
     * @param dataList 问答数据列表
     * @param fileOriginId 文件源ID
     * @param fileChunkUid 文件块UID
     * @param teamId 团队ID
     * @param bookId 书籍ID
     */
    private void saveQaData(List<FileQaDataDO> dataList, Long fileOriginId, String fileChunkUid, Long teamId,
        Long bookId, String tocs) {
        List<FileQaDO> qaList = new ArrayList<>();
        List<FileQaDataDO> qaDataList = new ArrayList<>();
        List<RagChunkDTO> ragChunkDTOS = new ArrayList<>();

        for (FileQaDataDO data : dataList) {
            if (StringUtils.isEmpty(data.getQuestion()) || StringUtils.isEmpty(data.getAnswer())) {
                continue;
            }

            String uid = SequenceUtil.getSerialNumberPlus();

            FileQaDO qa = new FileQaDO();
            qa.setFileOriginId(fileOriginId);
            qa.setUid(uid);
            qa.setFileChunkUid(fileChunkUid);
            qaList.add(qa);

            data.setFileOriginId(fileOriginId);
            data.setQaUid(uid);
            qaDataList.add(data);

            RagChunkDTO ragChunkDTO = new RagChunkDTO();
            ragChunkDTO.setBookId(bookId);
            ragChunkDTO.setFileOriginId(fileOriginId);
            ragChunkDTO.setChunkUid(fileChunkUid);
            ragChunkDTO.setQaLink(data.getQuestion() + " " + data.getAnswer());
            ragChunkDTO.setTocs(tocs);
            ragChunkDTO.setQaUid(uid);
            ragChunkDTOS.add(ragChunkDTO);
        }

        if (CollUtil.isNotEmpty(qaList)) {
            fileQaDaoService.saveBatch(qaList);
        }
        if (CollUtil.isNotEmpty(qaDataList)) {
            fileQaDataDaoService.saveBatch(qaDataList);
        }
        if (CollUtil.isNotEmpty(ragChunkDTOS)) {
            milvusQaLinkService.insert(ragChunkDTOS);
        }
    }

}
