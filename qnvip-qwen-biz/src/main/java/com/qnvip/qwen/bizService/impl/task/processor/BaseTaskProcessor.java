package com.qnvip.qwen.bizService.impl.task.processor;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;

import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileTaskProgressDatalogService;

public abstract class BaseTaskProcessor implements TaskProcessor {

    @Resource
    private FileTaskProgressDatalogService fileTaskProgressDatalogService;


    /**
     * 记录步骤产出数据
     *
     * @param task 任务
     * @param data
     */
    protected void recordData(TaskProgressDomainModelDTO task, String data) {
        fileTaskProgressDatalogService.recordData(task.getFileOriginId(), task.getId(), supportStep(),
            data);
    }

    /**
     * 加载步骤产出的数据
     * 
     * @param model 原始文件id
     * @param nowStep 指定步骤
     * @return
     */
    protected FileTaskProgressDatalogDO loadData(TaskProgressDomainModelDTO model, TaskStepsEnum nowStep) {
        FileTaskProgressDatalogDO datalogDO =
            fileTaskProgressDatalogService.loadLatestData(model.getFileOriginId(), nowStep);
        if (datalogDO == null || ObjectUtils.isEmpty(datalogDO.getData())) {
            String msg = String.format("历史步骤数据为空 当前步骤-%s 历史步骤%s taskId%s", supportStep().getDesc(),
                supportStep().getDesc(), model.getId());
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(), msg);
        }
        return datalogDO;

    }

}
