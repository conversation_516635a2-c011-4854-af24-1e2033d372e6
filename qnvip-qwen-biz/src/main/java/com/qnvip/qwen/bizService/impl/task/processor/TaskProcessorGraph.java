package com.qnvip.qwen.bizService.impl.task.processor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.bizService.AIChatBizService;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dto.FileGraphExtractDataDTO;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.enums.CategoryEnum;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.enums.PromptEnum;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileGraphService;
import com.qnvip.qwen.service.GraphMilvusService;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.util.rag.ChunkUtil;
import com.qnvip.qwen.util.rag.GraphUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档结构化
 */
@Service
@Slf4j
public class TaskProcessorGraph extends BaseTaskProcessor implements TaskProcessor {
    protected static final ExecutorService executor =
        new ThreadPoolExecutor(10, 10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("TaskProcessorGraph-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    private static final int ENTITY_MAX_LENGTH = 25;
    @Resource
    private FileChunkDataDaoService fileChunkDataDaoService;
    @Resource
    private AIChatBizService aiChatBizService;
    @Resource
    private FileGraphService fileGraphService;
    @Resource
    private GraphMilvusService graphMilvusService;

    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_GRAPH;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        FileOriginDO fileOriginDO = task.getFileOriginDO();
        Long fileOriginId = fileOriginDO.getId();
        Long bookId = fileOriginDO.getBookId();

        fileGraphService.deleteAndRefreshByFileId(fileOriginDO.getId());
        graphMilvusService.deleteByFileOriginIds(Lists.newArrayList(fileOriginId));

        List<FileChunkDataDO> fileChunks = fileChunkDataDaoService.getListByFileOriginId(fileOriginId);
        if (CollUtil.isEmpty(fileChunks)) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "TASK_STEP_GRAPH fileOriginId=" + fileOriginId + "的chunk data不存在");
        }

        List<CompletableFuture> futures = new ArrayList<>();
        for (FileChunkDataDO chunkData : fileChunks) {
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                int retryCount = 0;
                boolean success = false;

                // 重试逻辑，最多尝试3次
                while (retryCount < 3 && !success) {
                    try {
                        doExecute(chunkData, fileOriginDO, bookId, fileOriginId);
                        success = true;
                    } catch (Exception e) {
                        retryCount++;
                        if (retryCount < 3) {
                            try {
                                // 等待一段时间后再重试，例如1秒
                                Thread.sleep(1000);
                            } catch (InterruptedException interruptedException) {
                                Thread.currentThread().interrupt();
                                log.error("线程被中断", interruptedException);
                            }
                        }
                        log.error("graph数据解析异常,chunkUid{}, 重试次数:{}", chunkData.getChunkUid(), retryCount, e);
                    }
                }
            }, executor);
            futures.add(completableFuture);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return TaskOperateEnum.CONTINUE;
    }

    private void doExecute(FileChunkDataDO chunkData, FileOriginDO fileOriginDO, Long bookId, Long fileOriginId) {
        String prompt = PromptEnum.ENTITY_EXTRACT.replace("{{examples}}", PromptEnum.GRAPH_EXAMPLE)
            .replace("{{entityTypes}}", PromptEnum.GRAPH_DEFAULT_ENTITY)
            .replace("{{inputText}}", ChunkUtil.toScoreChunk(fileOriginDO.getTocs(), chunkData.getData()));

        String resp = aiChatBizService.chatBlockingJsonByThird(prompt);
        List<FileGraphExtractDataDTO> dataList = JSONUtil.toList(resp, FileGraphExtractDataDTO.class);
        if (CollUtil.isNotEmpty(dataList)) {
            saveGraphData(bookId, fileOriginId, chunkData.getChunkUid(), dataList);
        }
    }

    /**
     * 保存问答数据
     *
     * @param dataList 问答数据列表
     * @param fileOriginId 文件源ID
     * @param chunkUid 文件块UID
     * @param bookId 书籍ID
     */
    private void saveGraphData(Long bookId, Long fileOriginId, String chunkUid,
        List<FileGraphExtractDataDTO> dataList) {

        /// 过滤空数据和异常数据
        List<FileGraphExtractDataDTO> graphs = dataList.stream().filter(this::needCollect).collect(Collectors.toList());
        graphs.forEach(e -> {
            e.setBookId(bookId);
            e.setFileOriginId(fileOriginId);
            e.setChunkUid(chunkUid);
        });

        /// dataformat
        for (FileGraphExtractDataDTO graph : graphs) {
            if (CategoryEnum.ENTITY.getDesc().contains(graph.getType())) {
                graph.setEntName(GraphUtil.removeNameSpace(
                    GraphUtil.removeBracketsContent(GraphUtil.normalizeExtractedInfo(graph.getEntName()))));
                graph.setEntType(GraphUtil.removeBracketsContent(graph.getEntType()));
                graph.setEntDesc(GraphUtil.normalizeExtractedInfo(graph.getEntDesc()));
            } else if (CategoryEnum.RELATIONSHIP.getDesc().contains(graph.getType())) {
                graph.setSrc(GraphUtil.removeNameSpace(
                    GraphUtil.removeBracketsContent(GraphUtil.normalizeExtractedInfo(graph.getSrc()))));
                graph.setTgt(GraphUtil.removeNameSpace(
                    GraphUtil.removeBracketsContent(GraphUtil.normalizeExtractedInfo(graph.getTgt()))));
                graph.setRelDesc(GraphUtil.normalizeExtractedInfo(graph.getRelDesc()));
                graph.setRelKeys(GraphUtil.removeBracketsContent(GraphUtil.normalizeExtractedInfo(graph.getRelDesc())));
            }
        }

        /// 数据入db
        if (CollUtil.isNotEmpty(graphs)) {
            fileGraphService.saveBatchToDb(graphs);
        }

        /// 数据入图
        if (CollUtil.isNotEmpty(graphs)) {
            fileGraphService.saveBatchToGraphByRelation(graphs);
        }

        /// 数据入向量
        if (CollUtil.isNotEmpty(graphs)) {
            graphMilvusService.saveBatchToMilvus(graphs);
        }

    }

    private boolean needCollect(FileGraphExtractDataDTO e) {
        if (ObjectUtils.isEmpty(e.getType())) {
            return false;
        }

        if (CategoryEnum.ENTITY.getDesc().contains(e.getType())) {
            if (e.getEntName() != null && e.getEntName().length() > ENTITY_MAX_LENGTH) {
                return false;
            }
            /// 实体属性-名字 类型 描述 都不能为空
            return isCollect(e.getEntName()) || isCollect(e.getEntType()) || isCollect(e.getEntDesc());
        }

        if (CategoryEnum.RELATIONSHIP.getDesc().contains(e.getType())) {
            if (e.getSrc() != null && e.getSrc().length() > ENTITY_MAX_LENGTH) {
                return false;
            }

            if (e.getTgt() != null && e.getTgt().length() > ENTITY_MAX_LENGTH) {
                return false;
            }
            /// 关系属性-源实体 目标实体 关系描述 关键词 关系强度
            return isCollect(e.getSrc()) || isCollect(e.getTgt()) || isCollect(e.getRelDesc())
                || isCollect(e.getRelKeys()) || isCollect(e.getRelStrength()) || e.getSrc().length() > ENTITY_MAX_LENGTH
                || e.getTgt().length() > ENTITY_MAX_LENGTH;
        }

        if (CategoryEnum.CONTENT_KEYWORDS.getDesc().contains(e.getType())) {
            /// 关系属性-源实体 目标实体 关系描述 关键词 关系强度
            return isCollect(e.getHighLevelKeywords());
        }
        return false;
    }

    private boolean isCollect(Object data) {
        if (ObjectUtils.isEmpty(data)) {
            return false;
        }
        return !"无".equals(data) && !"空".equals(data);

    }
}
