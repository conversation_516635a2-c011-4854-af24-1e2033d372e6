package com.qnvip.qwen.bizService.impl.task.processor;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.qnvip.qwen.bizService.AIChatBizService;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.dal.es.esdo.TagNavigationESDO;
import com.qnvip.qwen.dal.es.esdo.TagNavigationLlmDO;
import com.qnvip.qwen.enums.PromptEnum;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileOriginService;
import com.qnvip.qwen.service.FileSummaryService;
import com.qnvip.qwen.service.FileTagService;
import com.qnvip.qwen.service.TeamBookDocService;
import com.qnvip.qwen.service.TeamBookService;
import com.qnvip.qwen.util.rag.LanguageProcessUtil;
import com.qnvip.qwen.vo.response.TeamBookResp;

import lombok.extern.slf4j.Slf4j;

/**
 * 把多种格式文件转成md格式
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskProcessorNavigation extends BaseTaskProcessor implements TaskProcessor {
    private static final int MAX_PROMPT_LENGTH = 80_000;
    @Resource
    private AIChatBizService aiChatBizService;
    @Resource
    private FileTagService fileTagService;
    @Resource
    private FileSummaryService fileSummaryService;


    @Resource
    private TeamBookDocService teamBookDocService;
    @Resource
    private TeamBookService teamBookService;
    @Resource
    private FileOriginService fileOriginService;

    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_NAVIGATION;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {

        FileTaskProgressDatalogDO datalogDO = loadData(task, TaskStepsEnum.TASK_STEP_DESENSITIZATION);

        String data = datalogDO.getData();
        if (data.length() > MAX_PROMPT_LENGTH) {
            data = data.substring(0, MAX_PROMPT_LENGTH);
        }

        String prompt = PromptEnum.TAG_AND_SUMMARY.replace("{article}", data);

        String chatModelResponse = aiChatBizService.chatBlockingJsonByThird(prompt);

        List<String> tocs =
            teamBookDocService.loadTocNames(task.getFileOriginDO().getBookId(), task.getFileOriginDO().getDocUid());

        TeamBookResp book = teamBookService.detail(task.getFileOriginDO().getBookId());
        tocs.add(0, book.getName());

        TagNavigationLlmDO llm = JSON.parseObject(chatModelResponse, TagNavigationLlmDO.class);

        TagNavigationESDO tagAndSummary = new TagNavigationESDO();
        tagAndSummary.setTeamId(task.getFileOriginDO().getTeamId());
        tagAndSummary.setBookId(task.getFileOriginDO().getBookId());
        tagAndSummary.setFileOriginId(task.getFileOriginDO().getId());
        tagAndSummary.setTags(LanguageProcessUtil.keywordSearchProcess(String.join(" ", llm.getTags())));
        tagAndSummary.setSummary(LanguageProcessUtil.keywordSearchProcess(String.join(" ", tagAndSummary.getTags())));
        tagAndSummary.setTocs(LanguageProcessUtil.keywordSearchProcess(String.join(" ", tocs)));

        // 保存tocs
        fileOriginService.updateToc(task.getFileOriginId(), tocs);

        // 保存文章摘要
        fileSummaryService.saveDeleteBefore(task.getFileOriginId(), tagAndSummary.getSummary());

        // 保存文章tags
        fileTagService.saveDeleteBefore(task.getFileOriginId(), llm.getTags());

        // 保存数据
        recordData(task, chatModelResponse);

        return TaskOperateEnum.CONTINUE;

    }



}
