// package com.qnvip.qwen.bizService.impl;
//
// import java.util.Arrays;
// import java.util.List;
//
// import com.alibaba.dashscope.audio.asr.transcription.Transcription;
// import com.alibaba.dashscope.audio.asr.transcription.TranscriptionParam;
// import com.alibaba.dashscope.audio.asr.transcription.TranscriptionQueryParam;
// import com.alibaba.dashscope.audio.asr.transcription.TranscriptionResult;
// import com.alibaba.dashscope.audio.asr.transcription.TranscriptionTaskResult;
//
/// **
// * <AUTHOR>
// * @desc 描述
// * @createTime 2025年04月29日 09:59:00
// */
//
// public class TestService {
// public static void main(String[] args) {
// // 创建转写请求参数
// TranscriptionParam param = TranscriptionParam.builder()
// // 若没有将API Key配置到环境变量中，需将apiKey替换为自己的API Key
// .apiKey("sk-3e9a2167d94f4dde8e5fb664ad52921f").model("paraformer-v2")
// // “language_hints”只支持paraformer-v2模型
// .parameter("language_hints", new String[] {"zh", "en"})
// .fileUrls(Arrays.asList("https://ai-api-dev.qnvip.com/test.mp3")).build();
// try {
// Transcription transcription = new Transcription();
// // 提交转写请求
// TranscriptionResult result = transcription.asyncCall(param);
// System.out.println("RequestId: " + result.getRequestId());
// // 阻塞等待任务完成并获取结果
// result = transcription.wait(TranscriptionQueryParam.FromTranscriptionParam(param, result.getTaskId()));
// // 解析转写结果
// List<TranscriptionTaskResult> taskResultList = result.getResults();
// if (taskResultList != null && taskResultList.size() > 0) {
// for (TranscriptionTaskResult taskResult : taskResultList) {
// String transcriptionUrl = taskResult.getTranscriptionUrl();
// System.out.println("transcriptionUrl:" + transcriptionUrl);
// // HttpURLConnection connection =
// // (HttpURLConnection) new URL(transcriptionUrl).openConnection();
// // connection.setRequestMethod("GET");
// // connection.connect();
// // BufferedReader reader =
// // new BufferedReader(new InputStreamReader(connection.getInputStream()));
// // Gson gson = new GsonBuilder().setPrettyPrinting().create();
// // System.out.println(gson.toJson(gson.fromJson(reader, JsonObject.class)));
// }
// }
// } catch (Exception e) {
// System.out.println("error: " + e);
// }
// System.exit(0);
// }
// }
