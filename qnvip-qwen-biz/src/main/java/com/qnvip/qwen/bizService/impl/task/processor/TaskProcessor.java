package com.qnvip.qwen.bizService.impl.task.processor;

import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;

public interface TaskProcessor {

    /**
     * 支持的任务步骤
     * 
     * @return
     */
    TaskStepsEnum supportStep();

    /**
     * 执行任务
     *
     * @param task
     * @return
     */
    TaskOperateEnum execute(TaskProgressDomainModelDTO task);
}
