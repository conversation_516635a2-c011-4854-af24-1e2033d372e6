package com.qnvip.qwen.bizService.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.bizService.GraphBizService;
import com.qnvip.qwen.bizService.ReCallBizService;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.FileQaDaoService;
import com.qnvip.qwen.dal.dto.FileChunkRerankDTO;
import com.qnvip.qwen.dal.dto.FileOriginScoreDTO;
import com.qnvip.qwen.dal.dto.HybirdRecallDTO;
import com.qnvip.qwen.dal.dto.IdScoreFileDTO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.FileQaDO;
import com.qnvip.qwen.dal.es.EsChunkSearchService;
import com.qnvip.qwen.dal.es.dto.EsChunkSearchDTO;
import com.qnvip.qwen.dal.milvus.MilvusChunkDocService;
import com.qnvip.qwen.dal.milvus.MilvusQaLinkService;
import com.qnvip.qwen.dal.redis.ChunkCacheRedisService;
import com.qnvip.qwen.enums.RecallFromEnum;
import com.qnvip.qwen.enums.RecallTypeEnum;
import com.qnvip.qwen.service.FileRecallLogService;
import com.qnvip.qwen.service.TeamBookService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.util.rag.ChunkUtil;
import com.qnvip.qwen.util.rag.LanguageProcessUtil;
import com.qnvip.qwen.vo.request.DifyUserRecallReq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月01日 09:54:00
 */
@Slf4j
@Service
public class ReCallBizServiceImpl implements ReCallBizService {

    // 在类中定义线程池（确保重用）
    protected static final ExecutorService executorMutilQuestion =
        new ThreadPoolExecutor(9, 27, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("ReCallBizServiceImpl-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    protected static final ExecutorService executorRecall =
        new ThreadPoolExecutor(8, 32, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("ReCallBizServiceImpl-executorRecall-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Resource
    private EsChunkSearchService esChunkSearchService;
    @Resource
    private MilvusChunkDocService milvusChunkDocService;
    @Resource
    private MilvusQaLinkService milvusQaLinkService;
    @Resource
    private FileQaDaoService fileQaDaoService;
    @Resource
    private FileChunkDataDaoService fileChunkDataDaoService;

    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private FileRecallLogService fileRecallLogService;
    @Resource
    private ChunkCacheRedisService chunkCacheRedisService;
    @Resource
    private TeamBookService teamBookService;

    @Resource
    private GraphBizService graphBizService;

    @Value("${rerank.host}")
    private String rerankHost;

    private static @NotNull List<FileChunkRerankDTO> putScoreAndSortGetReranks(List<FileChunkDataDO> chunkDataList,
        List<BigDecimal> rerankScoreList) {
        List<FileChunkRerankDTO> rerankList = new ArrayList<>();
        // 塞入rerank评分
        for (int i = 0; i < chunkDataList.size(); i++) {
            FileChunkDataDO chunk = chunkDataList.get(i);
            BigDecimal score = rerankScoreList.get(i);

            FileChunkRerankDTO rerankDTO = new FileChunkRerankDTO();
            CopierUtil.copy(chunk, rerankDTO);
            rerankDTO.setContent(chunk.getData());
            rerankDTO.setScore(score);
            rerankDTO.setSort(chunk.getSort());
            rerankList.add(rerankDTO);
        }
        return rerankList;
    }

    @Override
    public String recall(List<DifyUserRecallReq> reqs) {
        // 批量召回问题，线程并发处理
        List<Future<List<FileChunkRerankDTO>>> futures = reqs.stream().map(e -> {
            final DifyUserRecallReq tmpReq = CopierUtil.copy(e, DifyUserRecallReq.class);
            return executorMutilQuestion.submit(() -> getFileChunkRerankDTOS(tmpReq));
        }).collect(Collectors.toList());

        List<FileChunkRerankDTO> futuresResults = futures.stream().map(e -> {
            try {
                return e.get(10, TimeUnit.SECONDS);
            } catch (InterruptedException | TimeoutException | ExecutionException ex) {
                throw new RuntimeException(ex);
            }
        }).flatMap(List::stream).collect(Collectors.toList());

        // 切块组文章
        DifyUserRecallReq req = reqs.get(0);
        List<FileOriginScoreDTO> fileScores = chunkMerge(req, futuresResults);

        // 文章取topN
        fileScores = fileTopN(req, fileScores);

        // 构建响应结果并返回
        if (req.getRecallType().equals(RecallTypeEnum.HAS_PERMIT.getDesc())) {
            return buildRecallResp(req, fileScores);
        } else {
            return buildFileNameResp(req, fileScores);
        }
    }

    @NotNull
    private List<FileChunkRerankDTO> getFileChunkRerankDTOS(DifyUserRecallReq e) {
        try {
            return hybridAndRerank(e);
        } catch (Throwable ex) {
            ex.printStackTrace();
            return Lists.newArrayList();
        }
    }

    /**
     * 混合检索和重排序
     *
     * @param req
     * @return
     */
    private List<FileChunkRerankDTO> hybridAndRerank(DifyUserRecallReq req) {

        beforeRecall(req);

        HybirdRecallDTO hybirdRecallDTO = new HybirdRecallDTO();

        // 切块多路召回
        getMultiRouteRecall(req, hybirdRecallDTO);

        List<String> totalChunkUids = hybirdRecallDTO.toTotalChunkUids();

        afterRecall(req, totalChunkUids);

        // 重排序
        List<FileChunkRerankDTO> reranks = getRerankList(totalChunkUids, req, hybirdRecallDTO);

        // 保存命中率分析
        fileRecallLogService.saveStatic(req, hybirdRecallDTO, RecallFromEnum.STATIC);

        return reranks;
    }

    private void beforeRecall(DifyUserRecallReq req) {
        req.setExcludedChunkIds(chunkCacheRedisService.getExcludedChunkIds(req.getWorkflowRunId()));
    }

    private void afterRecall(DifyUserRecallReq req, List<String> chunks) {
        chunkCacheRedisService.addExcludedChunkIds(req.getWorkflowRunId(), chunks);
    }

    /**
     * 多路召回
     *
     * @param req
     * @param recall
     * @return
     */
    private void getMultiRouteRecall(DifyUserRecallReq req, HybirdRecallDTO recall) {
        long totalStartTime = System.currentTimeMillis();

        // 使用CompletableFuture并行执行所有召回任务
        CompletableFuture<Void> esRecallFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            recall.setEsRecallChunkUids(esRecall(req));
            log.info("es召回耗时{}", System.currentTimeMillis() - startTime);
        }, executorRecall);

        CompletableFuture<Void> milvusQaRecallFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            recall.setMilvusQaRecallChunkUids(milvusRecallQa(req));
            log.info("milvus qa召回耗时{}", System.currentTimeMillis() - startTime);
        }, executorRecall);

        CompletableFuture<Void> milvusChunkRecallFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            recall.setMilvusChunkRecallChunkUids(milvusChunkRecall(req));
            log.info("milvus chunk召回耗时{}", System.currentTimeMillis() - startTime);
        }, executorRecall);

        CompletableFuture<Void> graphRecallFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            recall.setGraphRecallChunkUids(graphBizService.graphRecall(req));
            log.info("graph召回耗时{}", System.currentTimeMillis() - startTime);
        }, executorRecall);

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(esRecallFuture, milvusQaRecallFuture, milvusChunkRecallFuture, graphRecallFuture)
                .join();
            log.info("全部召回完成，总耗时{}", System.currentTimeMillis() - totalStartTime);
        } catch (Exception e) {
            log.error("并发召回异常", e);
        }
    }

    @Override
    public String recall(DifyUserRecallReq req) {

        log.debug("用户问题{} {}多路召回入参 single {}", req.getQuestionId(), req.getRecallType(), JSONUtil.toJsonStr(req));
        List<FileChunkRerankDTO> resultList = hybridAndRerank(req);

        // 切块组文章
        List<FileOriginScoreDTO> fileScores = chunkMerge(req, resultList);

        // 构建响应结果并返回
        if (req.getRecallType().equals(RecallTypeEnum.HAS_PERMIT.getDesc())) {
            return buildRecallResp(req, fileScores);
        } else {
            return buildFileNameResp(req, fileScores);
        }
    }

    private @NotNull List<FileOriginScoreDTO> fileTopN(DifyUserRecallReq req, List<FileOriginScoreDTO> fileScores) {
        fileScores = fileScores.stream().limit(req.getRecallConfig().getDocTopN()).collect(Collectors.toList());
        // fileRecallLogService.saveFileTopNRecall(req, req.getQuestionId(), fileScores, RecallFromEnum.FILE_TOP_N);
        log.debug("用户问题{} {} docTopN过滤 fileOriginIds{}", req.getQuestionId(), req.getRecallType(),
            fileScores.stream().map(FileOriginScoreDTO::getFileOriginId).distinct().collect(Collectors.toList()));
        return fileScores;
    }

    private List<FileChunkRerankDTO> getRerankList(List<String> chunkUIds, DifyUserRecallReq req,
        HybirdRecallDTO hybirdRecallDTO) {
        if (CollUtil.isEmpty(chunkUIds)) {
            return new ArrayList<>();
        }

        // 根据分块ID查找切块内容
        List<FileChunkDataDO> chunkDataList = fileChunkDataDaoService.getListByChunkUIds(chunkUIds);
        if (chunkUIds.size() != chunkDataList.size()) {
            log.warn("用户问题{} 周围切块召回  存在脏数据", req.getQuestionId());
        }

        // 对切块进行重排序
        return processRerank(chunkDataList, req.getUserInput(), req, hybirdRecallDTO);
    }

    private List<String> milvusChunkRecall(DifyUserRecallReq req) {
        // chunk
        List<String> chunkChunkIds = milvusChunkDocService.search(req.getBookIds(), req.getFileOriginIds(),
            req.getUserInput(), req.getRecallConfig().getQaVector(), req.getExcludedChunkIds());
        List<FileChunkDataDO> chunkDataList = fileChunkDataDaoService.getListByChunkUIds(chunkChunkIds);
        if (chunkChunkIds.size() != chunkDataList.size()) {
            log.warn("milvusRecall chunk 数量不相等");
        }

        fileRecallLogService.saveMultRecallMilvusRecall(req, req.getQuestionId(), chunkDataList,
            RecallFromEnum.MULT_MILVUS_CHUNK);

        return chunkChunkIds;
    }

    private List<String> milvusRecallQa(DifyUserRecallReq req) {
        List<String> qaUids = milvusQaLinkService.search(req.getBookIds(), req.getFileOriginIds(), req.getUserInput(),
            req.getRecallConfig().getQaVector(), req.getExcludedChunkIds());

        if (CollUtil.isEmpty(qaUids)) {
            log.warn("用户问题{} {}多路召回milvus qa未命中", req.getQuestionId(), req.getRecallType());
            // 召回qa相关的切块
            return new ArrayList<>();
        }

        List<FileQaDO> fileQas = fileQaDaoService.getSortedQaByUIds(qaUids);
        if (CollUtil.isEmpty(fileQas)) {
            log.warn("用户问题{} {}多路召回milvus qa没有对应的切块", req.getQuestionId(), req.getRecallType());
            return new ArrayList<>();
        }

        if (fileQas.size() != qaUids.size()) {
            log.warn("milvusRecall qa的chunk 数量不相等");
        }

        // qa
        List<String> qaChunkUids =
            fileQas.stream().map(FileQaDO::getFileChunkUid).distinct().collect(Collectors.toList());
        fileRecallLogService.saveMultRecallChunkMilvusRecall(req, req.getQuestionId(), fileQas,
            RecallFromEnum.MULT_MILVUS_QA);

        return qaChunkUids;
    }

    /**
     * es召回
     *
     * @param req
     */
    private List<String> esRecall(DifyUserRecallReq req) {
        EsChunkSearchDTO searchDTO = new EsChunkSearchDTO();
        searchDTO.setUserInput(req.getUserInput());
        searchDTO.setBookIds(req.getBookIds());
        searchDTO.setChunkFullText(req.getRecallConfig().getChunkFullText());
        searchDTO.setExcludedChunkIds(req.getExcludedChunkIds());
        searchDTO.setFileOriginIds(req.getFileOriginIds());

        List<IdScoreFileDTO<String>> esList = esChunkSearchService.searchRelevantDocuments(searchDTO);
        if (CollUtil.isEmpty(esList)) {
            return new ArrayList<>();
        }

        // 保存多路召回的日志
        fileRecallLogService.saveMultChunkEsRecall(req, req.getQuestionId(), esList, RecallFromEnum.MULT_ES);
        List<Long> fileOriginIds =
            esList.stream().map(IdScoreFileDTO::getFileOriginId).distinct().collect(Collectors.toList());
        List<String> chunkUids =
            esList.stream().map(IdScoreFileDTO<String>::getId).distinct().collect(Collectors.toList());
        log.debug("用户问题{} {}多路召回es originFileIds {} chunkIds {}", req.getQuestionId(), req.getRecallType(),
            fileOriginIds, chunkUids);

        return chunkUids;

    }

    /**
     * 重排序处理
     *
     * @param chunks
     * @param userInput
     * @param hybirdRecallDTO
     * @return
     */
    private List<FileChunkRerankDTO> processRerank(List<FileChunkDataDO> chunks, String userInput,
        DifyUserRecallReq req, HybirdRecallDTO hybirdRecallDTO) {
        Long questionId = req.getQuestionId();
        if (CollUtil.isEmpty(chunks) || StrUtil.isEmpty(userInput)) {
            return new ArrayList<>();
        }

        // 对切块使用重排序模型打分rerank模型
        List<BigDecimal> vtSimilarity = getVtSimilarity(chunks, userInput, req);
        if (CollUtil.isEmpty(vtSimilarity)) {
            return new ArrayList<>();
        }

        List<BigDecimal> tkSimilarity = getTkSimilarity(userInput, chunks);

        // 权重混合公式计算最终得分
        List<BigDecimal> rerankSimilarity = mergeScore(vtSimilarity, tkSimilarity, req.getRecallConfig().getTkweight(),
            req.getRecallConfig().getVtweight());

        // 塞入评分获得rerank
        List<FileChunkRerankDTO> vtSimilarityModel = putScoreAndSortGetReranks(chunks, rerankSimilarity);
        if (ObjectUtils.isEmpty(vtSimilarityModel)) {
            return new ArrayList<>();
        }

        // 对rerank进行重排序 得分倒序
        vtSimilarityModel =
            vtSimilarityModel.stream().sorted(Comparator.comparing(FileChunkRerankDTO::getScore).reversed())
                .limit(req.getRecallConfig().getRerankChunk().getTopN()).collect(Collectors.toList());

        // 对rerank进行重排序 抛弃过低评分
        vtSimilarityModel = vtSimilarityModel.stream().filter(
            e -> e.getScore().compareTo(BigDecimal.valueOf(req.getRecallConfig().getRerankChunk().getMinScore())) > 0)
            .collect(Collectors.toList());

        // 对rerank进行重排序 取前N个
        vtSimilarityModel = vtSimilarityModel.stream().limit(req.getRecallConfig().getRerankChunk().getTopN())
            .collect(Collectors.toList());

        // 保存重排序召回日志
        List<String> chunkUIds =
            vtSimilarityModel.stream().map(FileChunkRerankDTO::getChunkUid).distinct().collect(Collectors.toList());
        fileRecallLogService.saveRerankChunkRecall(req, questionId, vtSimilarityModel, RecallFromEnum.MULT_RERANK);

        hybirdRecallDTO.setRerankRecallChunkUids(chunkUIds);
        return vtSimilarityModel;
    }

    private List<BigDecimal> getTkSimilarity(String userInput, List<FileChunkDataDO> chunks) {
        List<FileChunkRerankDTO> fileChunkRerankDTOS = formRerankChunks(chunks, userInput);
        List<List<String>> tks = fileChunkRerankDTOS.stream().map(FileChunkRerankDTO::getContent)
            .map(LanguageProcessUtil::segment).collect(Collectors.toList());

        // 问题分词
        List<String> result = LanguageProcessUtil.getEsQuestion(userInput);
        // 相似度计算
        List<Double> doubles = LanguageProcessUtil.computeSimilarityBatch(result, tks);
        return doubles.stream().map(BigDecimal::valueOf).collect(Collectors.toList());
    }

    /**
     * 权重混合公式 最终分数 = tkweight * (关键词相似度 + 排序特征分数) + vtweight * 向量相似度
     *
     * @param vtSim vtweight
     * @param tkSim tkweight
     * @param tkweight 关键字权重
     * @param vtweight 向量权重
     * @return
     */
    private List<BigDecimal> mergeScore(List<BigDecimal> vtSim, List<BigDecimal> tkSim, BigDecimal tkweight,
        BigDecimal vtweight) {
        List<BigDecimal> finalScores = new ArrayList<>();
        for (int i = 0; i < vtSim.size(); i++) {
            // tkweight * (关键词相似度 + 排序特征分数)
            BigDecimal part1 = tkweight.multiply(tkSim.get(i));
            // vtweight * 向量相似度
            BigDecimal part2 = vtweight.multiply(vtSim.get(i));
            // 最终分数
            finalScores.add(part1.add(part2));
        }
        return finalScores;
    }

    /**
     * 组装成重排序的切块
     *
     * @param chunkDataList
     * @param userInput
     * @return
     */
    private @NotNull List<FileChunkRerankDTO> formRerankChunks(List<FileChunkDataDO> chunkDataList, String userInput) {
        List<Long> fileOriginIds =
            chunkDataList.stream().map(FileChunkDataDO::getFileOriginId).collect(Collectors.toList());
        Map<Long, FileOriginDO> fileOriginDOMap = fileOriginDaoService.getMapByIds(fileOriginIds);

        // 组装请求rerank的参数
        return chunkDataList.stream().map(c -> {
            FileOriginDO origin = fileOriginDOMap.get(c.getFileOriginId());
            FileChunkRerankDTO dto = new FileChunkRerankDTO();
            dto.setChunkUid(c.getChunkUid());
            dto.setFileOriginId(c.getFileOriginId());
            dto.setQuery(userInput);
            if (origin != null) {
                dto.setContent(ChunkUtil.toScoreChunk(origin.getTocs(), c.getData()));
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 调用rerank模型
     *
     * @param
     * @return
     */
    private List<BigDecimal> getVtSimilarity(List<FileChunkDataDO> chunks, String userInput, DifyUserRecallReq req) {
        if (CollUtil.isEmpty(chunks)) {
            return new ArrayList<>();
        }
        List<FileChunkRerankDTO> rerankParams = formRerankChunks(chunks, userInput);

        try {
            String resp = HttpUtil.post(rerankHost + "/rerank", JSONUtil.toJsonStr(rerankParams));
            if (StringUtils.isEmpty(resp)) {
                log.error("=====重排序范围内容为空=====questionId:{},processContent:{}", req.getQuestionId(), rerankParams);
            }

            List<BigDecimal> list = JSONUtil.toList(resp, BigDecimal.class);

            if (rerankParams.size() != list.size()) {
                throw FrameworkException.instance("重排序两个列表长度不一致");
            }

            return list;
        } catch (Exception e) {
            throw FrameworkException.instance("重排序失败questionId=" + req.getQuestionId());
        }

    }

    public String buildRecallResp(DifyUserRecallReq req, List<FileOriginScoreDTO> fileScores) {
        if (CollUtil.isEmpty(fileScores)) {
            return "";
        }

        Map<Long, String> teamNameMap = teamBookService.loadNameMap();

        Map<Long, FileOriginDO> fileOriginMap = fileOriginDaoService.getMapByIds(
            fileScores.stream().map(FileOriginScoreDTO::getFileOriginId).distinct().collect(Collectors.toList()));

        StringBuilder sb = new StringBuilder();
        fileScores.forEach(dto -> {
            FileOriginDO fileOrigin = fileOriginMap.get(dto.getFileOriginId());
            if (fileOrigin == null || CollUtil.isEmpty(dto.getChunks())) {
                log.warn("用户问题{} FileOriginDO丢失id={} ", req.getQuestionId(), dto.getFileOriginId());
                return;
            }

            String bookName = teamNameMap.get(fileOrigin.getBookId());

            // 合并召回chunk 并且消除相邻chunk的重叠内容
            String mergedContent = mergeAdjacentChunks(dto.getChunks().stream()
                .sorted(Comparator.comparing(FileChunkRerankDTO::getSort)).collect(Collectors.toList()));
            // 构建输出 引用块开始
            sb
                // 目录行
                .append(ChunkUtil.toScoreChunk(fileOrigin.getTocs(), mergedContent))
                // 链接行
                .append("\n").append(generateMarkdownLink(fileOrigin, bookName)).append("\n\n\n\n");
        });

        return sb.toString();
    }

    /**
     * 切块分组
     *
     * 把ChunkRerank 根据fileOriginId分组，然后对每个分组进行排序，排序规则为：按照score降序排序
     *
     * @param req
     * @param
     * @return
     */
    private List<FileOriginScoreDTO> chunkMerge(DifyUserRecallReq req, List<FileChunkRerankDTO> rerankList) {

        // 根据chunuid去重
        List<
            FileChunkRerankDTO> distinctList =
                rerankList.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toMap(FileChunkRerankDTO::getChunkUid,
                        Function.identity(), (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())));

        List<FileOriginScoreDTO> fileOriginReranks = distinctList.stream()
            .collect(Collectors.groupingBy(FileChunkRerankDTO::getFileOriginId)).entrySet().stream().map(entry -> {
                FileOriginScoreDTO dto = new FileOriginScoreDTO();
                dto.setFileOriginId(entry.getKey());
                dto.setChunks(entry.getValue());

                // 计算maxScore
                dto.setMaxScore(entry.getValue().stream().map(FileChunkRerankDTO::getScore).filter(Objects::nonNull)
                    .max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO));

                return dto;
            }).sorted(Comparator.comparing(FileOriginScoreDTO::getMaxScore).reversed()).collect(Collectors.toList());

        fileRecallLogService.saveChunkMergeRecall(req, req.getQuestionId(), fileOriginReranks,
            RecallFromEnum.CHUNK_MERGE);
        log.debug("用户问题{} {}文章分组排序 结果fileOriginIds{}", req.getQuestionId(), req.getRecallType(),
            fileOriginReranks.stream().map(FileOriginScoreDTO::getFileOriginId).collect(Collectors.toList()));

        // 文章取topN
        fileOriginReranks = fileTopN(req, fileOriginReranks);

        return fileOriginReranks;
    }

    public String buildFileNameResp(DifyUserRecallReq req, List<FileOriginScoreDTO> rerankList) {
        if (CollUtil.isEmpty(rerankList)) {
            return "";
        }

        Map<Long, String> teamNameMap = teamBookService.loadNameMap();

        Map<Long, FileOriginDO> fileOriginDOMap = fileOriginDaoService.getMapByIds(
            rerankList.stream().map(FileOriginScoreDTO::getFileOriginId).distinct().collect(Collectors.toList()));

        StringBuilder sb = new StringBuilder();
        sb.append("### 可能相关的文档\n\n");

        rerankList.forEach(dto -> {
            FileOriginDO fileOrigin = fileOriginDOMap.get(dto.getFileOriginId());
            if (fileOrigin == null || CollUtil.isEmpty(dto.getChunks())) {
                log.warn("用户问题{} FileOriginDO丢失id={} ", req.getQuestionId(), dto.getFileOriginId());
                return;
            }
            String bookName = teamNameMap.get(fileOrigin.getBookId());

            // 目录行
            sb.append("目录：").append(fileOrigin.getTocs()).append("\n")
                // 链接行
                .append(generateMarkdownLink(fileOrigin, bookName)).append("\n\n\n\n");
        });

        return sb.toString();
    }

    /**
     * 合并分块内容并去除重叠部分
     */
    private String mergeAdjacentChunks(List<FileChunkRerankDTO> chunks) {
        if (CollUtil.isEmpty(chunks)) {
            return "";
        }

        StringBuilder merged = new StringBuilder(chunks.get(0).getContent());
        FileChunkRerankDTO previousChunk = chunks.get(0);

        for (int i = 1; i < chunks.size(); i++) {
            FileChunkRerankDTO currentChunk = chunks.get(i);
            // 只有当sort值连续时才处理重叠
            if (currentChunk.getSort() == previousChunk.getSort() + 1) {
                int overlap = findOverlap(previousChunk.getContent(), currentChunk.getContent());
                if (overlap > 0) {
                    merged.append(currentChunk.getContent().substring(overlap));
                } else {
                    merged.append("\n").append(currentChunk.getContent());
                }
            } else {
                // 非相邻chunk直接换行添加
                merged.append("\n").append(currentChunk.getContent());
            }
            previousChunk = currentChunk;
        }

        return merged.toString();
    }

    /**
     * 查找两个字符串间的重叠长度
     */
    private int findOverlap(String a, String b) {
        int maxPossible = Math.min(a.length(), b.length());
        for (int i = maxPossible; i > 0; i--) {
            if (a.endsWith(b.substring(0, i))) {
                return i;
            }
        }
        return 0;
    }

    private String generateMarkdownLink(FileOriginDO file, String teamName) {
        if (file == null || file.getFileName() == null || file.getTargetOriginUrl() == null) {
            return "";
        }
        return "原文地址：" + teamName + " - [" + file.getFileName() + "](" + file.getTargetOriginUrl().trim() + ")";
    }

}
