package com.qnvip.qwen.bizService.impl;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.bizService.AIChatBizService;
import com.qnvip.qwen.config.AiLlmProperties;
import com.qnvip.qwen.dal.dto.ai.AiMessageDTO;
import com.qnvip.qwen.dal.dto.ai.CompletionModelRequestDTO;
import com.qnvip.qwen.dal.dto.ai.CompletionModelResponseDTO;
import com.qnvip.qwen.dal.dto.ai.GenerateModelRequestDTO;
import com.qnvip.qwen.dal.dto.ai.GenerateModelResponseDTO;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.enums.InternalBusinessException;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.util.RemoveMarkdownJsonFormatUtil;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Data
public class AiChatBizServiceImpl implements AIChatBizService {
    private static final String LOCAL_OLLAMA_PROPS = "ollamaQwen72b";
    private static final String THIRD_BYTE_DOUBAO_PROPS = "bytepro256";
    @Resource
    private AiLlmProperties aiLlmProperties;

    @Override
    public String chatBlockingJsonByThird(String content) {
        return chatBlockingJson(THIRD_BYTE_DOUBAO_PROPS, content, null, null);

    }

    @Override
    public String chatBlockingJsonByLocal(String content, String jsonFormat, String options) {
        return chatBlockingJson(LOCAL_OLLAMA_PROPS, content, jsonFormat, options);
    }

    private String chatBlockingJson(String props, String content, String jsonFormat, String options) {
        AiLlmProperties.Api api = aiLlmProperties.getApis().get(props);

        if (api.getUrl().contains("completions")) {
            CompletionModelRequestDTO completionModelRequestDTO = new CompletionModelRequestDTO();
            completionModelRequestDTO.setModel(api.getModelName());
            completionModelRequestDTO.setStream(false);

            completionModelRequestDTO.setMessages(Lists.newArrayList(AiMessageDTO.system().content(content).build()));
            CompletionModelResponseDTO completionModelResponseDTO = completionsBlocking(api, completionModelRequestDTO);
            String chatModelResponse =
                String.valueOf(completionModelResponseDTO.getChoices().get(0).getMessage().getContent());

            chatModelResponse = RemoveMarkdownJsonFormatUtil.removeMarkdownJson(chatModelResponse);
            return chatModelResponse;

        } else if (api.getUrl().contains("generate")) {
            GenerateModelRequestDTO request = new GenerateModelRequestDTO();
            request.setModel(api.getModelName());
            request.setPrompt(content);
            request.setStream(false);
            request.setFormat(ObjectUtils.isEmpty(jsonFormat) ? null : JSON.parseObject(jsonFormat));
            request.setOptions(ObjectUtils.isEmpty(options) ? null : JSON.parseObject(options));

            GenerateModelResponseDTO completionModelResponse = generateBlocking(api, request);
            return completionModelResponse.getResponse();
        }

        throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
            "chatBlockingJson 未配置的url类型" + api.getUrl());
    }

    private CompletionModelResponseDTO completionsBlocking(AiLlmProperties.Api api,
        CompletionModelRequestDTO completionModelRequestDTO) {
        log.info("ai chatBlocking req {},url:{}", JSON.toJSONString(completionModelRequestDTO), api.getUrl());
        HttpResponse response = HttpUtil.createPost(api.getUrl()).header("Authorization", "Bearer " + api.getApiKey())
            .body(JSON.toJSONString(completionModelRequestDTO), ContentType.JSON.getValue()).execute();
        String body = response.body();
        log.info("ai chatBlocking resp {} ", body);
        CompletionModelResponseDTO completionModelResponseDTO =
            JSON.parseObject(body, CompletionModelResponseDTO.class);
        if (completionModelResponseDTO.getError() != null) {
            throw InternalBusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                JSON.toJSONString(completionModelResponseDTO.getError()));
        }
        return completionModelResponseDTO;
    }

    private GenerateModelResponseDTO generateBlocking(AiLlmProperties.Api api,
        GenerateModelRequestDTO chatModelRequest) {
        log.info("ai generateBlocking req {},url:{}", JSON.toJSONString(chatModelRequest), api.getUrl());
        HttpResponse response = HttpUtil.createPost(api.getUrl()).header("Authorization", "Bearer " + api.getApiKey())
            .body(JSON.toJSONString(chatModelRequest), ContentType.JSON.getValue()).execute();
        String body = response.body();
        log.info("ai generateBlocking resp {} ", body);
        return JSON.parseObject(body, GenerateModelResponseDTO.class);
    }

}
