package com.qnvip.qwen.bizService.impl.task.processor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.util.IDCardMaskingUtil;
import com.qnvip.qwen.util.PhoneNumberMaskingUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskProcessorDesensitization extends BaseTaskProcessor implements TaskProcessor {
    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_DESENSITIZATION;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        log.info("执行数据脱敏任务{}", task);

        FileTaskProgressDatalogDO datalogDO = loadData(task, TaskStepsEnum.TASK_STEP_CLEAN);
        String data = datalogDO.getData();

        // 身份证隐藏中间部分
        data = IDCardMaskingUtil.maskIDCardNumbers(data);
        // 手机号隐藏中间4位
        data = PhoneNumberMaskingUtil.maskPhoneNumbers(data);

        recordData(task, data);
        return TaskOperateEnum.CONTINUE;
    }


}
