package com.qnvip.qwen.bizService.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.qnvip.qwen.bizService.EsDataInitBizService;
import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dao.FileTagDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.entity.FileTagDO;
import com.qnvip.qwen.dal.es.EsChunkService;
import com.qnvip.qwen.dal.es.dto.EsChunkInitDTO;
import com.qnvip.qwen.dal.es.esdo.DocChunkESDO;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.util.rag.LanguageProcessUtil;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc milvus数据初始化
 * @createTime 2025年04月15日 10:46:00
 */
@Service
@Slf4j
public class EsDataInitBizServiceImpl implements EsDataInitBizService {
    protected static final ExecutorService executor =
        new ThreadPoolExecutor(10, 30, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNameFormat("EsDataInitService-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    @Resource
    public FileChunkDaoService fileChunkDaoService;
    @Resource
    private FileTagDaoService fileTagDaoService;
    @Resource
    private EsChunkService esChunkService;

    /**
     * 初始化文档切块数据
     *
     * @param param 参数
     */
    @Override
    public void initBookChunk(DataInitSearchDTO param) {
        List<CompletableFuture> futures = new ArrayList<>();
        int pageNo = 0;
        int pageSize = 1000;

        while (true) {
            param.setLimitSql(buildLimitSql(pageNo, pageSize));
            List<EsChunkInitDTO> list = fileChunkDaoService.getEsInitList(param);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            futures.add(processChunkInitList(list));
            pageNo++;
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        log.info("====EsDataInitServiceImpl.initBookDoc=====结束");
    }

    /**
     * 设置查询限制SQL
     *
     * @param pageNo 页码
     * @param pageSize 每页大小
     */
    private String buildLimitSql(int pageNo, int pageSize) {
        return " limit " + pageNo * pageSize + "," + pageSize;
    }

    /**
     * 处理ES初始化列表
     *
     * @param list ES初始化DTO列表
     * @return CompletableFuture
     */
    private CompletableFuture<Void> processChunkInitList(List<EsChunkInitDTO> list) {
        return CompletableFuture.runAsync(() -> {
            List<Long> fileOriginIds =
                list.stream().map(EsChunkInitDTO::getFileOriginId).distinct().collect(Collectors.toList());

            List<FileTagDO> tagDOS = fileTagDaoService.getListByFileOriginIds(fileOriginIds);
            Map<Long, List<FileTagDO>> tagMap = tagDOS.stream().collect(Collectors.groupingBy(FileTagDO::getOriginId));

            Map<Long, Map<Long, List<EsChunkInitDTO>>> teamBookMap = list.stream().collect(
                Collectors.groupingBy(EsChunkInitDTO::getTeamId, Collectors.groupingBy(EsChunkInitDTO::getBookId)));

            try {
                processTeamBookMap(teamBookMap, tagMap);
            } catch (Exception e) {
                log.error("处理文档切块保存ES异常", e);
            }
        }, executor);
    }

    /**
     * 处理团队书籍映射
     *
     * @param teamBookMap 团队书籍映射
     * @param tagMap 标签映射
     */
    private void processTeamBookMap(Map<Long, Map<Long, List<EsChunkInitDTO>>> teamBookMap,
        Map<Long, List<FileTagDO>> tagMap) {
        for (Map.Entry<Long, Map<Long, List<EsChunkInitDTO>>> teamBookEntry : teamBookMap.entrySet()) {
            for (Map.Entry<Long, List<EsChunkInitDTO>> bookEntry : teamBookEntry.getValue().entrySet()) {
                Long bookId = bookEntry.getKey();
                List<EsChunkInitDTO> chunkList = bookEntry.getValue();
                if (CollUtil.isEmpty(chunkList)) {
                    continue;
                }

                List<DocChunkESDO> saves = new ArrayList<>();
                for (EsChunkInitDTO c : chunkList) {
                    DocChunkESDO save = CopierUtil.copy(c, DocChunkESDO.class);
                    save.setTocs(LanguageProcessUtil.keywordSearchProcess(c.getTocs()));

                    List<FileTagDO> tagDOList = tagMap.get(c.getFileOriginId());
                    if (CollUtil.isNotEmpty(tagDOList)) {
                        save.setTags(LanguageProcessUtil.keywordSearchProcess(
                            tagDOList.stream().map(FileTagDO::getName).distinct().collect(Collectors.toList())));
                    }

                    save.setContent(LanguageProcessUtil.keywordSearchProcess(c.getContent()));
                    save.setBookId(bookId);

                    saves.add(save);
                }

                if (CollUtil.isNotEmpty(saves)) {
                    List<Long> fileOriginIds =
                        saves.stream().map(DocChunkESDO::getFileOriginId).distinct().collect(Collectors.toList());
                    // 先删除
                    esChunkService.batchDeleteByFileOriginIds(fileOriginIds);
                    // 再保存
                    esChunkService.batchSaveOrUpdate(saves, bookId);
                }
            }
        }
    }

}
