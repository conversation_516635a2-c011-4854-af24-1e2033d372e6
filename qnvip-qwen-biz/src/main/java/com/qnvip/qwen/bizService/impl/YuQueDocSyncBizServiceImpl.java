package com.qnvip.qwen.bizService.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.bizService.YuQueDocSyncBizService;
import com.qnvip.qwen.component.yuque.YuQueApiComponent;
import com.qnvip.qwen.component.yuque.enums.TocTypeEnum;
import com.qnvip.qwen.component.yuque.response.YuQueBookListResp;
import com.qnvip.qwen.component.yuque.response.YuQueBookTocResp;
import com.qnvip.qwen.component.yuque.response.YuQueDockDetailResp;
import com.qnvip.qwen.component.yuque.response.YuQueDockListResp;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.enums.FileExtEnum;
import com.qnvip.qwen.enums.FileOriginTypeEnum;
import com.qnvip.qwen.enums.TeamTypeEnum;
import com.qnvip.qwen.service.FileTaskProgressService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class YuQueDocSyncBizServiceImpl implements YuQueDocSyncBizService {

    @Resource
    private YuQueApiComponent yuQueApiComponent;
    @Resource
    private TeamDaoService teamDaoService;
    @Resource
    private TeamBookDaoService teamBookDaoService;
    @Resource
    private TeamBookDocDaoService teamBookDocDaoService;
    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;
    @Resource
    private FileTaskProgressService fileTaskProgressService;

    private static final int MAX_RETRY_TIMES = 3;
    private static final int RETRY_INTERVAL_MS = 3000;
    private static final int TIMEZONE_OFFSET_HOURS = 8;

    protected static final ExecutorService executor =
            new ThreadPoolExecutor(3, 5, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
                    new ThreadFactoryBuilder().setNamePrefix("syncFullCapitalApiData-executor-%d").build(),
                    new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void syncBook() {
        List<TeamDO> teams = teamDaoService.getAll(TeamTypeEnum.YUQUE);
        if (CollUtil.isEmpty(teams)) {
            return;
        }
        for (TeamDO t : teams) {
            Long teamId = t.getId();
            String teamMark = t.getMark();
            int pageNo = 0;
            int limit = 100;
            List<TeamBookDO> teamBookDOList;
            List<YuQueBookListResp> list = new ArrayList<>();
            List<String> slugList = null;
            Map<String, TeamBookDO> dbBookMap = null;
            while (true) {
                int offset = pageNo * limit;
                list = getWithRetry(() -> yuQueApiComponent.getBookList(teamMark, offset, limit), "获取语雀知识库列表", teamMark,
                    offset, limit);
                if (CollUtil.isEmpty(list)) {
                    break;
                }
                teamBookDOList = new ArrayList<>();
                // 语雀知识库唯一标识
                slugList = list.stream().map(YuQueBookListResp::getSlug).distinct().collect(Collectors.toList());
                dbBookMap = teamBookDaoService.getMapByTeamIdAndMark(teamId, slugList);

                for (YuQueBookListResp r : list) {
                    TeamBookDO dbData = dbBookMap.get(r.getSlug());
                    TeamBookDO teamBookDO = new TeamBookDO();
                    if (dbData != null) {
                        teamBookDO.setId(dbData.getId());
                    }
                    teamBookDO.setMark(r.getSlug());
                    teamBookDO.setTeamMark(t.getMark());
                    teamBookDO.setTeamId(teamId);
                    teamBookDO.setName(r.getName());
                    teamBookDO.setType(TeamTypeEnum.YUQUE.getCode());
                    teamBookDO.setDescription(r.getDescription());
                    teamBookDO.setContentUpdateTime(r.getContentUpdatedAt().plusHours(8));
                    teamBookDO.setCreateTime(r.getCreatedAt());
                    teamBookDOList.add(teamBookDO);
                }
                if (CollUtil.isNotEmpty(teamBookDOList)) {
                    teamBookDaoService.saveOrUpdateBatch(teamBookDOList);
                }
                pageNo++;
            }
        }
    }

    /**
     * 同步知识库目录
     */
    @Override
    public void syncBookToc() {
        List<TeamBookDO> teamBooks = teamBookDaoService.getAll(TeamTypeEnum.YUQUE);
        if (CollUtil.isEmpty(teamBooks)) {
            return;
        }
        for (TeamBookDO t : teamBooks) {
            CompletableFuture.runAsync(() -> {
                Long teamId = t.getTeamId();
                Long bookId = t.getId();
                String teamMark = t.getTeamMark();
                String bookMark = t.getMark();
                try {
                    List<YuQueBookTocResp> list = getWithRetry(
                        () -> yuQueApiComponent.getBookTocList(teamMark, bookMark), "获取语雀文档目录", teamMark, bookMark);
                    if (CollUtil.isEmpty(list)) {
                        return;
                    }
                    // 语雀目录uuid
                    List<String> uuids =
                        list.stream().map(YuQueBookTocResp::getUuid).distinct().collect(Collectors.toList());
                    Map<String, Long> dbDocMap = teamBookDocDaoService.getMapByBookIdAndUid(bookId, uuids);
                    Map<String, Long> dbFileMap = fileOriginDaoService.getMapByBookIdAndUid(bookId, uuids);
                    List<TeamBookDocDO> processDocList = new ArrayList<>();
                    List<FileOriginDO> processFileList = new ArrayList<>();
                    for (YuQueBookTocResp r : list) {
                        this.processDocList(processDocList, dbDocMap, r, teamId, bookId);
                        this.processFileList(processFileList, dbFileMap, r, teamId, bookId, teamMark, bookMark);
                    }
                    if (CollUtil.isNotEmpty(processDocList)) {
                        teamBookDocDaoService.saveOrUpdateBatch(processDocList);
                    }
                    if (CollUtil.isNotEmpty(processFileList)) {
                        fileOriginDaoService.saveOrUpdateBatch(processFileList);
                    }
                    list = null;
                    processDocList = null;
                    processFileList = null;
                    dbDocMap = null;
                    dbFileMap = null;
                    uuids = null;
                } catch (Exception e) {
                    log.error("======同步语雀文档目录异常======，teamMark:{},bookMark:{}", teamMark, bookMark, e);
                }
            }, executor);
        }

        //同步更新子文档状态
        teamBookDocDaoService.renewExitsSubDoc();
    }


    @Override
    public void syncBookData() {
        List<TeamBookDO> teamBooks = teamBookDaoService.getAll(TeamTypeEnum.YUQUE);
        if (CollUtil.isEmpty(teamBooks)) {
            return;
        }

        for (TeamBookDO t : teamBooks) {
            Long bookId = t.getId();
            String teamMark = t.getTeamMark();
            String bookMark = t.getMark();
            CompletableFuture.runAsync(() -> {
                int pageNo = 0;
                while(true) {
                    try {
                        int offset = pageNo * 40;
                        List<YuQueDockListResp> list =
                            getWithRetry(() -> yuQueApiComponent.getDocList(teamMark, bookMark, offset, 40),
                                "获取语雀文档列表",
                                teamMark, bookMark);

                        if (CollUtil.isEmpty(list)) {
                            break;
                        }

                        List<String> slugList =
                            list.stream().map(YuQueDockListResp::getSlug).distinct().collect(Collectors.toList());
                        Map<String, TeamBookDocDataDO> dataMap =
                            teamBookDocDataDaoService.getMapByBookIdAndMarkList(slugList, bookId);
                        Map<String, TeamBookDocDO> docMap =
                            teamBookDocDaoService.getMapByBookIdAndMarkList(bookId, slugList);
                        List<TeamBookDocDataDO> dataList = new ArrayList<>();
                        List<String> docUidList = new ArrayList<>();
                        TeamBookDocDataDO newData = null;
                        for (YuQueDockListResp l : list) {
                            String docSlug = l.getSlug();
                            TeamBookDocDO dbDoc = docMap.get(l.getSlug());
                            if (dbDoc == null) {
                                continue;
                            }

                            newData =
                                processDocData(l, dataMap.get(docSlug), teamMark, bookMark, dbDoc.getUid(), docSlug,
                                    bookId);
                            if (newData != null) {
                                dataList.add(newData);
                                docUidList.add(dbDoc.getUid());
                            }
                        }

                        if (CollUtil.isNotEmpty(dataList)) {
                            teamBookDocDataDaoService.saveOrUpdateBatch(dataList);
                            Map<String, Long> dbFileMap = fileOriginDaoService.getMapByBookIdAndUid(bookId, docUidList);
                            List<Long> fileIds = dbFileMap.values().stream().distinct().collect(Collectors.toList());
                            List<FileOriginDO> processFileList = new ArrayList<>();
                            dataList.stream().filter(d -> !d.getType().equals(FileExtEnum.MD.getDesc())).forEach(d -> {
                                Long fileId = dbFileMap.get(d.getDocUid());
                                if (fileId != null) {
                                    FileOriginDO file = new FileOriginDO();
                                    file.setId(fileId);
                                    file.setFileExtension(d.getType());
                                    processFileList.add(file);
                                }
                            });
                            // 为了更新fileOrigin的fileExtension字段，需要更新操作
                            if (CollUtil.isNotEmpty(processFileList)) {
                                fileOriginDaoService.updateBatchById(processFileList);
                            }
                            if (CollUtil.isNotEmpty(fileIds)) {
                                // 创建文档处理任务
                                fileTaskProgressService.saveTaskStartWithFormat(bookId, fileIds);
                            }
                            fileIds = null;
                        }
                        list = null;
                        slugList = null;
                        docMap = null;
                        dataMap = null;
                        dataList = null;
                        docUidList = null;
                    } catch (Exception e) {
                        log.error("======同步语雀文档数据异常======，teamMark:{},bookMark:{}", teamMark, bookMark, e);
                    }

                    pageNo++;
                }
            }, executor);

        }
    }

    /**
     * 处理删除的文档和知识库
     */
    @Override
    public void processDelete(LocalDateTime time) {
        List<TeamBookDocDO> todoList = new ArrayList<>();

        // 删除知识库
        List<TeamBookDO> deleteBooks =
            teamBookDaoService.getDeleteListByUpdateTime(time);
        if (CollUtil.isNotEmpty(deleteBooks)) {
            List<Long> bookIds = deleteBooks.stream().map(TeamBookDO::getId).collect(Collectors.toList());
            teamBookDaoService.deleteByIds(bookIds);

            // 删除知识库关联的文档
            List<TeamBookDocDO> docList = teamBookDocDaoService.getListByBookIds(bookIds);
            if (CollUtil.isNotEmpty(docList)) {
                todoList.addAll(docList);
            }
        }

        // 删除天数内未获取到的文档数据
        List<TeamBookDocDO> deleteList =
            teamBookDocDaoService.getDeleteListByUpdateTime(time);
        if (CollUtil.isNotEmpty(deleteList)) {
            todoList.addAll(deleteList);
        }

        if (CollUtil.isEmpty(todoList)) {
            return;
        }
        List<Long> docIds = todoList.stream().map(TeamBookDocDO::getId).distinct().collect(Collectors.toList());
        List<String> docUids = todoList.stream().map(TeamBookDocDO::getUid).distinct().collect(Collectors.toList());
        List<FileOriginDO> fileOriginDOS = fileOriginDaoService.getListByDocUids(docUids);
        teamBookDocDaoService.deleteByIds(docIds);
        teamBookDocDataDaoService.deleteByDocUIds(docUids);

        if (CollUtil.isNotEmpty(fileOriginDOS)) {
            List<Long> fileIds = fileOriginDOS.stream().map(FileOriginDO::getId).collect(Collectors.toList());
            fileOriginDaoService.deleteByDocUids(docUids);
            // 删除任务
            fileTaskProgressService.deleteByFileOriginIds(fileIds);
        }
    }


    private TeamBookDocDataDO processDocData(YuQueDockListResp doc, TeamBookDocDataDO dbData,
        String teamMark, String bookMark, String docUid, String docSlug, Long bookId) {

        LocalDateTime time = doc.getContentUpdatedAt().plusHours(TIMEZONE_OFFSET_HOURS);
        if (dbData != null && !time.isAfter(dbData.getContentUpdateTime())) {
            return null;
        }

        YuQueDockDetailResp detail = getWithRetry(() ->
                        yuQueApiComponent.getDocDetail(teamMark, bookMark, doc.getSlug()),
                "获取语雀文档目录", teamMark, bookMark, doc.getSlug());

        if (detail == null) {
            return null;
        }

        TeamBookDocDataDO newData = new TeamBookDocDataDO();
        if (dbData != null) {
            newData.setId(dbData.getId());
        }
        newData.setDocUid(docUid);
        String type = detail.getType();
        if ("Sheet".equals(type)) {
            newData.setData(detail.getBodySheet());
            newData.setType(FileExtEnum.EXCEL_JSON.getDesc());
        } else if ("Doc".equals(type)) {
            newData.setData(detail.getBody());
            newData.setType(FileExtEnum.MD.getDesc());
        } else if ("Board".equals(type)) {
            newData.setData(detail.getBody());
            newData.setType(FileExtEnum.BOARD_JSON.getDesc());
        } else if ("Table".equals(type)) {
            newData.setData(detail.getBody());
            newData.setType(FileExtEnum.TABLE_JSON.getDesc());
        } else {
            newData.setData(detail.getBody());
            newData.setType(type);
        }
        newData.setMark(docSlug);
        newData.setContentUpdateTime(time);
        newData.setBookId(bookId);

        if (ObjectUtils.isEmpty(newData.getData()) || ObjectUtils.isEmpty(newData.getData().trim())) {
            return null;
        }
        return newData;
    }

    private <T> T getWithRetry(Supplier<T> supplier, String operation, Object... params) {
        for (int i = 0; i <= MAX_RETRY_TIMES; i++) {
            try {
                return supplier.get();
            } catch (Exception e) {
                if (i == MAX_RETRY_TIMES) {
                    log.error("======{}异常，重试{}次后仍失败======", operation, MAX_RETRY_TIMES, params, e);
                    return null;
                }
                log.warn("======{}异常，开始第{}次重试======", operation, i+1, params, e);
                try {
                    Thread.sleep(RETRY_INTERVAL_MS);
                } catch (InterruptedException interruptedException) {
                    Thread.currentThread().interrupt();
                    return null;
                }
            }
        }
        return null;
    }


    private void processDocList(List<TeamBookDocDO> processDocList, Map<String, Long> dbDocMap, YuQueBookTocResp r, Long teamId, Long bookId) {
        Long dbDataId = dbDocMap.get(r.getUuid());
        TeamBookDocDO docDO = new TeamBookDocDO();
        if (dbDataId != null) {
            docDO.setId(dbDataId);
        }
        docDO.setTeamId(teamId);
        docDO.setBookId(bookId);
        docDO.setUid(r.getUuid());
        if (r.getType().equals("TITLE")) {
            docDO.setType(TocTypeEnum.TITLE.getCode());
        } else if (r.getType().equals("DOC")) {
            docDO.setType(TocTypeEnum.DOC.getCode());
        }
        docDO.setName(r.getTitle());
        docDO.setMark(r.getSlug());
        docDO.setParentUid(r.getParentUuid());
        processDocList.add(docDO);
    }

    private void processFileList(List<FileOriginDO> processFileList, Map<String, Long> dbFileMap, YuQueBookTocResp r, Long teamId, Long bookId, String teamMark, String bookMark) {
        if (r.getType().equals("TITLE")) {
            return;
        }
        Long dbDataId = dbFileMap.get(r.getUuid());
        FileOriginDO fileDO = new FileOriginDO();
        String fileExtension = FileExtEnum.MD.getDesc();
        if (dbDataId != null) {
            fileDO.setId(dbDataId);
            fileExtension = null;
        }
        fileDO.setTeamId(teamId);
        fileDO.setBookId(bookId);
        fileDO.setDocUid(r.getUuid());
        fileDO.setDocTableUid(r.getParentUuid());
        fileDO.setFileName(r.getTitle());
        fileDO.setChecksum(r.getUuid());
        //语雀文档的扩展名是md
        fileDO.setFileExtension(fileExtension);
        fileDO.setType(FileOriginTypeEnum.YU_QUE.getCode());
        fileDO.setTargetOriginUrl(yuQueApiComponent.getDocUrl(teamMark, bookMark, r.getUrl()));

        processFileList.add(fileDO);
    }
}
