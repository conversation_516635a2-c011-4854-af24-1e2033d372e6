package com.qnvip.qwen.bizService.impl.task.processor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.util.HtmlTagRemoveUtil;
import com.qnvip.qwen.util.ReplaceSpecialCharactersRemoveUtil;
import com.qnvip.qwen.util.TextCleanUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskProcessorClean extends BaseTaskProcessor implements TaskProcessor {
    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_CLEAN;
    }


    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        log.info("执行数据清洗任务{}", task);

        FileTaskProgressDatalogDO datalogDO = loadData(task, TaskStepsEnum.TASK_STEP_FORMAT);

        String data = datalogDO.getData();

        // 剔除脚本、广告、特殊符号
        data = ReplaceSpecialCharactersRemoveUtil.removeUnicodeControlChars(data);

        // 多空白字符清理，多换行符替换成\n\n
        data = TextCleanUtil.cleanTextWithRegex(data);

        // 移除html标签
        data = HtmlTagRemoveUtil.removeHtmlTags(data);

        // 避免影响语义模型，将双引号替换成中文引号
        data = data.replace("\"", "“");

        recordData(task, data);
        return TaskOperateEnum.CONTINUE;
    }

}
