package com.qnvip.qwen.bizService.impl.task;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.bizService.ApiSyncRegistryBizService;
import com.qnvip.qwen.bizService.ApiSyncService;

@Service
public class ApiSyncRegistryBizServiceImpl implements ApiSyncRegistryBizService, InitializingBean {

    @Resource
    private List<ApiSyncService> apiSyncServices;

    private final Map<String, List<ApiSyncService>> registry = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, List<ApiSyncService>> collect =
            apiSyncServices.stream().collect(Collectors.groupingBy(ApiSyncService::bookName));
        collect.forEach((s, apiSyncServices) -> {
            registry.put(s, apiSyncServices);
        });

    }

    @Override
    public List<String> bookNames() {
        return apiSyncServices.stream().map(ApiSyncService::bookName).collect(Collectors.toList());
    }

    @Override
    public List<String> tocNames() {
        return apiSyncServices.stream().map(ApiSyncService::tocName).collect(Collectors.toList());
    }

    @Override
    public List<ApiSyncService> getByBookName(String name) {
        return registry.get(name);
    }

}
