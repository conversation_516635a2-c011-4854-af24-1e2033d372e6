package com.qnvip.qwen.bizService.impl;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.qnvip.common.exception.FrameworkException;
import com.qnvip.qwen.bizService.FeishuDocSyncBizService;
import com.qnvip.qwen.component.feishu.FeishuApiComponent;
import com.qnvip.qwen.component.feishu.enums.FeishuObjectTypeEnum;
import com.qnvip.qwen.component.feishu.response.ExportTaskDownloadResponse;
import com.qnvip.qwen.component.feishu.response.SpaceListResponse;
import com.qnvip.qwen.component.feishu.response.SpaceNodeListResponse;
import com.qnvip.qwen.component.yuque.enums.TocTypeEnum;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.enums.FileExtEnum;
import com.qnvip.qwen.enums.FileOriginTypeEnum;
import com.qnvip.qwen.enums.TeamTypeEnum;
import com.qnvip.qwen.service.FileTaskProgressService;
import com.qnvip.qwen.util.ExcelTableJson;
import com.qnvip.qwen.util.ExcelToExcelJsonUtil;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.util.docx2md.DocxToMarkdownUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FeishuDocSyncBizServiceImpl implements FeishuDocSyncBizService, InitializingBean {

    protected static final ExecutorService executor =
        new ThreadPoolExecutor(3, 5, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("syncFullFeishuApiData-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    private static final int MAX_RETRY_TIMES = 3;
    private static final int RETRY_INTERVAL_MS = 3000;
    private static final int TIMEZONE_OFFSET_HOURS = 8;
    private static final int PAGE_SIZE = 50;
    @Resource
    private FeishuApiComponent feishuApiComponent;
    @Resource
    private TeamDaoService teamDaoService;
    @Resource
    private TeamBookDaoService teamBookDaoService;
    @Resource
    private TeamBookDocDaoService teamBookDocDaoService;
    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;
    @Resource
    private FileTaskProgressService fileTaskProgressService;

    @Override
    public void afterPropertiesSet() {
        List<TeamDO> teams = teamDaoService.getAll(TeamTypeEnum.FEISHU);
        List<FeishuApiComponent.AppSecret> appSecrets = teams.stream().map(e -> {
            FeishuApiComponent.AppSecret appSecret =
                JSON.parseObject(e.getAppIdSecretJson(), FeishuApiComponent.AppSecret.class);
            appSecret.setTeamId(e.getId());
            return appSecret;
        }).collect(Collectors.toList());
        feishuApiComponent.init(appSecrets);
    }

    @Override
    public void syncBook() {
        List<TeamDO> teams = teamDaoService.getAll(TeamTypeEnum.FEISHU);
        if (CollUtil.isEmpty(teams)) {
            return;
        }
        for (TeamDO team : teams) {
            SpaceListResponse spaceListResponse = feishuApiComponent.listSpace(team.getId(), PAGE_SIZE);

            List<String> marks = spaceListResponse.getItems().stream().map(SpaceListResponse.SpaceItem::getSpaceId)
                .distinct().collect(Collectors.toList());

            Map<String, TeamBookDO> dbBookMap = teamBookDaoService.getMapByTeamIdAndMark(team.getId(), marks);

            List<TeamBookDO> books = new ArrayList<>();
            for (SpaceListResponse.SpaceItem item : spaceListResponse.getItems()) {
                TeamBookDO book = dbBookMap.get(item.getSpaceId());
                TeamBookDO teamBookDO = new TeamBookDO();
                if (book != null) {
                    teamBookDO.setId(book.getId());
                }
                teamBookDO.setType(TeamTypeEnum.FEISHU.getCode());
                teamBookDO.setMark(item.getSpaceId());
                teamBookDO.setTeamMark(team.getMark());
                teamBookDO.setTeamId(team.getId());
                teamBookDO.setName(item.getName());
                teamBookDO.setDescription(item.getDescription());
                teamBookDO.setDescription(item.getDescription());
                teamBookDO.setContentUpdateTime(LocalDateTime.now());
                books.add(teamBookDO);
            }

            if (CollUtil.isEmpty(books)) {
                continue;
            }

            teamBookDaoService.saveOrUpdateBatch(books);
        }

    }

    /**
     * 同步知识库目录
     */
    @Override
    public void syncBookToc() {
        List<TeamBookDO> teamBooks = teamBookDaoService.getAll(TeamTypeEnum.FEISHU);
        if (CollUtil.isEmpty(teamBooks)) {
            return;
        }

        for (TeamBookDO book : teamBooks) {
            syncBookToc(book, Lists.newArrayList(""));
        }

        // 同步更新子文档状态
        teamBookDocDaoService.renewExitsSubDoc();
    }

    private void syncBookToc(TeamBookDO book, List<String> parentTokens) {
        if (CollUtil.isEmpty(parentTokens)) {
            return;
        }

        List<String> hasChildNodeToken = new ArrayList<>();
        for (String parentToken : parentTokens) {
            boolean hasMore = true;
            while (hasMore) {
                SpaceNodeListResponse resp =
                    feishuApiComponent.listSpaceNode(book.getTeamId(), book.getMark(), parentToken, PAGE_SIZE);
                hasMore = resp.getHasMore();

                if (CollUtil.isEmpty(resp.getItems())) {
                    continue;
                }

                for (SpaceNodeListResponse.NodeItem item : resp.getItems()) {
                    if (item.getHasChild()) {
                        hasChildNodeToken.add(item.getNodeToken());
                    }
                }

                List<String> uuids = resp.getItems().stream().map(SpaceNodeListResponse.NodeItem::getNodeToken)
                    .distinct().collect(Collectors.toList());
                saveDoc(book, uuids, resp);
                saveFileOrigin(book, uuids, resp);
            }
        }

        log.info("syncBookToc子目录文档同步{}", hasChildNodeToken);
        syncBookToc(book, hasChildNodeToken);
    }

    private void saveFileOrigin(TeamBookDO book, List<String> uuids, SpaceNodeListResponse response) {
        Map<String, Long> dbFileMap = fileOriginDaoService.getMapByBookIdAndUid(book.getId(), uuids);
        List<FileOriginDO> processFileList = new ArrayList<>();
        for (SpaceNodeListResponse.NodeItem item : response.getItems()) {
            Long dbDataId = dbFileMap.get(item.getNodeToken());
            FileOriginDO fileDO = new FileOriginDO();

            if (dbDataId != null) {
                fileDO.setId(dbDataId);
            }
            fileDO.setTeamId(book.getTeamId());
            fileDO.setBookId(book.getId());
            fileDO.setDocUid(item.getNodeToken());
            fileDO.setDocTableUid(item.getParentNodeToken());
            fileDO.setFileName(item.getTitle());
            fileDO.setChecksum(item.getObjToken());
            fileDO.setFileExtension(getFileExtension(item));
            fileDO.setType(FileOriginTypeEnum.LOCAL_FILE.getCode());
            fileDO.setTargetOriginUrl(feishuApiComponent.getDocUrl(book.getTeamMark(), item.getNodeToken()));
            processFileList.add(fileDO);
        }

        if (CollUtil.isEmpty(processFileList)) {
            return;
        }
        fileOriginDaoService.saveOrUpdateBatch(processFileList);
    }

    public String getFileExtension(SpaceNodeListResponse.NodeItem item) {
        String type = item.getObjType();
        if (type.equals(FeishuObjectTypeEnum.DOC.getCode()) || type.equals(FeishuObjectTypeEnum.DOCX.getCode())) {
            return FileExtEnum.MD.getDesc();
        }

        if (type.equals(FeishuObjectTypeEnum.SHEET.getCode())) {
            return FileExtEnum.EXCEL_JSON.getDesc();
        }

        if (type.equals(FeishuObjectTypeEnum.MINDNOTE.getCode())) {
            return FileExtEnum.MIND_NOTE.getDesc();
        }

        if (type.equals(FeishuObjectTypeEnum.BITABLE.getCode())) {
            return FileExtEnum.EXCEL_JSON.getDesc();
        }

        throw FrameworkException.instance("getFileExtension 转换失败，不支持的type " + JSON.toJSONString(item));
    }

    private void saveDoc(TeamBookDO book, List<String> uuids, SpaceNodeListResponse response) {
        Map<String, Long> dbDocMap = teamBookDocDaoService.getMapByBookIdAndUid(book.getId(), uuids);
        List<TeamBookDocDO> processDocList = new ArrayList<>();
        for (SpaceNodeListResponse.NodeItem item : response.getItems()) {
            Long dbDataId = dbDocMap.get(item.getNodeToken());
            TeamBookDocDO docDO = new TeamBookDocDO();

            if (dbDataId != null) {
                docDO.setId(dbDataId);
            }
            docDO.setParentId(0L);
            docDO.setTeamId(book.getTeamId());
            docDO.setBookId(book.getId());
            docDO.setParentUid(item.getParentNodeToken());
            docDO.setUid(item.getNodeToken());
            docDO.setType(TocTypeEnum.DOC.getCode());
            docDO.setName(item.getTitle());
            docDO.setMark(item.getObjToken());
            processDocList.add(docDO);
        }
        if (CollUtil.isEmpty(processDocList)) {
            return;
        }
        teamBookDocDaoService.saveOrUpdateBatch(processDocList);
    }

    @Override
    public void syncBookData() {
        List<TeamBookDO> teamBooks = teamBookDaoService.getAll(TeamTypeEnum.FEISHU);
        if (CollUtil.isEmpty(teamBooks)) {
            return;
        }

        for (TeamBookDO book : teamBooks) {
            syncBookData(book, Lists.newArrayList(""));
        }

    }

    private <T> void syncBookData(TeamBookDO book, List<String> parentTokens) {
        if (CollUtil.isEmpty(parentTokens)) {
            return;
        }

        List<String> nodeTokens = new ArrayList<>();
        for (String parentToken : parentTokens) {
            boolean hasMore = true;
            while (hasMore) {
                SpaceNodeListResponse resp =
                    feishuApiComponent.listSpaceNode(book.getTeamId(), book.getMark(), parentToken, PAGE_SIZE);
                hasMore = resp.getHasMore();

                if (CollUtil.isEmpty(resp.getItems())) {
                    continue;
                }

                for (SpaceNodeListResponse.NodeItem item : resp.getItems()) {
                    if (item.getHasChild()) {
                        nodeTokens.add(item.getNodeToken());
                    }
                }

                List<TeamBookDocDataDO> teamBookDocDataDOS = saveDocDataAndReturnNeedUpdateTask(book, resp);

                submitFileWriteTask(book.getId(), teamBookDocDataDOS);
            }
        }
        syncBookData(book, nodeTokens);
    }

    /**
     * 提交文档处理任务
     * 
     * @param bookId
     * @param teamBookDocDataDOS
     */
    private void submitFileWriteTask(Long bookId, List<TeamBookDocDataDO> teamBookDocDataDOS) {
        if (CollUtil.isEmpty(teamBookDocDataDOS)) {
            return;
        }

        List<String> docUidList =
            teamBookDocDataDOS.stream().map(TeamBookDocDataDO::getDocUid).collect(Collectors.toList());
        Map<String, Long> dbFileMap = fileOriginDaoService.getMapByBookIdAndUid(bookId, docUidList);

        List<Long> fileIds = dbFileMap.values().stream().distinct().collect(Collectors.toList());

        // 创建文档处理任务
        fileTaskProgressService.saveTaskStartWithFormat(bookId, fileIds);
    }

    /**
     * 保存文档数据，并且返回需要更新的任务
     * 
     * @param book
     * @param resp
     */
    private List<TeamBookDocDataDO> saveDocDataAndReturnNeedUpdateTask(TeamBookDO book, SpaceNodeListResponse resp) {
        List<String> uids =
            resp.getItems().stream().map(SpaceNodeListResponse.NodeItem::getNodeToken).collect(Collectors.toList());

        Map<String, TeamBookDocDataDO> dataMap = teamBookDocDataDaoService.getMapByDocUIdList(uids, book.getId());

        List<SpaceNodeListResponse.NodeItem> needUpdateNode = resp.getItems().stream()
            .filter(item -> dataMap.get(item.getNodeToken()) == null
                || !item.getObjEditTimeFmt().equals(dataMap.get(item.getNodeToken()).getContentUpdateTime()))
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(needUpdateNode)) {
            return new ArrayList<>();
        }

        List<TeamBookDocDataDO> needUpdateDocData = new ArrayList<>();

        for (SpaceNodeListResponse.NodeItem objectToken : needUpdateNode) {
            TeamBookDocDataDO newData = null;
            if (objectToken.getObjType().equals(FeishuObjectTypeEnum.MINDNOTE.getCode())) {
                log.info("飞书暂不支持思维笔记导出{}", JSON.toJSONString(objectToken));
                continue;
            } else {
                try {
                    // 写入本地，解析成文档
                    newData = getDataByDownloadFile(book, objectToken);
                } catch (FrameworkException e) {
                    e.printStackTrace();
                    continue;
                }
            }
            TeamBookDocDataDO before = dataMap.get(objectToken.getNodeToken());

            newData.setParentId(0L);
            newData.setDocUid(objectToken.getNodeToken());
            newData.setBookId(book.getId());
            newData.setMark(objectToken.getObjToken());
            newData.setContentUpdateTime(objectToken.getObjEditTimeFmt());
            newData.setId(before == null ? null : before.getId());
            teamBookDocDataDaoService.saveOrUpdate(newData);
            needUpdateDocData.add(newData);
        }

        return needUpdateDocData;

    }

    private TeamBookDocDataDO getDataByDownloadFile(TeamBookDO book, SpaceNodeListResponse.NodeItem objectToken) {
        ExportTaskDownloadResponse exportTaskDownloadResponse = feishuApiComponent
            .waitForCreateExportTask(book.getTeamId(), objectToken.getObjToken(), objectToken.getObjType());
        return writeLocalAndConvertToMarkdown(exportTaskDownloadResponse, this::convertLocalFileToDoc);
    }

    private TeamBookDocDataDO convertLocalFileToDoc(Path localFile) {

        if (localFile.toFile().getName().endsWith(FileExtEnum.DOCX.getDesc())) {
            try {
                TeamBookDocDataDO teamBookDocDataDO = new TeamBookDocDataDO();
                teamBookDocDataDO.setData(DocxToMarkdownUtil.docxToMarkdown(localFile.toString()));
                teamBookDocDataDO.setType(FileExtEnum.MD.getDesc());
                return teamBookDocDataDO;
            } catch (Exception e) {
                e.printStackTrace();
                throw FrameworkException.instance("docx 转md 失败");
            }
        } else if (localFile.toFile().getName().endsWith(FileExtEnum.XLSX.getDesc())) {
            try {
                ExcelTableJson excelTableJson = ExcelToExcelJsonUtil.getExcelTableJson(localFile);
                TeamBookDocDataDO teamBookDocDataDO = new TeamBookDocDataDO();
                teamBookDocDataDO.setData(JSON.toJSONString(excelTableJson));
                teamBookDocDataDO.setType(FileExtEnum.EXCEL_JSON.getDesc());
                return teamBookDocDataDO;
            } catch (Exception e) {
                e.printStackTrace();
                throw FrameworkException.instance("excel 转exceljson 失败");
            }
        } else {
            throw FrameworkException.instance("飞书转markdown 不支持的格式:" + localFile.toFile().getName());
        }
    }


    /**
     * 写入文件到本地返回路径
     * 
     * @param response
     * @param function
     * @return
     */
    public TeamBookDocDataDO writeLocalAndConvertToMarkdown(ExportTaskDownloadResponse response,
        Function<Path, TeamBookDocDataDO> function) {
        // 创建临时目录
        Path tempDir;
        Path filePath = null; // 用于存储文件路径
        try {
            tempDir = Files.createTempDirectory("tempFiles");
            // 设置文件路径
            filePath = Paths.get(tempDir.toString(), response.getFileName() + "." + response.getFileExtension());

            // 将文件内容写入临时目录
            try (FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
                fos.write(response.getFileData());
            }

            log.info("文件已写入临时目录：{}", filePath);

            // 执行 doAction 方法
            return function.apply(filePath);

        } catch (IOException e) {
            e.printStackTrace();
            throw FrameworkException.instance("feishu 文件写入失败");
        } finally {
            // 如果文件路径不为空，删除文件
            if (filePath != null) {
                try {
                    Files.delete(filePath);
                } catch (IOException e) {
                    e.printStackTrace();
                    throw FrameworkException.instance("feishu 文件写入删除失败");
                }
            }
        }
    }

}
