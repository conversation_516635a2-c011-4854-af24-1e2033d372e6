package com.qnvip.qwen.bizService.impl.task.processor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dao.FileChunkDataDaoService;
import com.qnvip.qwen.dal.dao.FileTagDaoService;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.dto.SectionDTO;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileChunkDO;
import com.qnvip.qwen.dal.entity.FileChunkDataDO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.FileTagDO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDatalogDO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.dal.es.EsChunkService;
import com.qnvip.qwen.dal.es.esdo.DocChunkESDO;
import com.qnvip.qwen.dal.milvus.MilvusChunkDocService;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.util.Lists;
import com.qnvip.qwen.util.MarkdownChunkTableUtil;
import com.qnvip.qwen.util.SequenceUtil;
import com.qnvip.qwen.util.rag.LanguageProcessUtil;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Node;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档切块
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskProcessorChunk extends BaseTaskProcessor implements TaskProcessor {
    @Resource
    private FileChunkDaoService fileChunkDaoService;
    @Resource
    private FileChunkDataDaoService fileChunkDataDaoService;
    @Resource
    private EsChunkService esChunkService;
    @Resource
    private TeamDaoService teamDaoService;
    @Resource
    private FileTagDaoService fileTagDaoService;
    @Value("${chunk.length:512}")
    private Integer chunkLength;
    @Value("${chunk.overlap:100}")
    private Integer chunkOverlap;
    @Value("${chunk.host:}")
    private String chunkHost;

    @Resource
    private MilvusChunkDocService milvusChunkDocService;

    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_CHUNK;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        FileOriginDO fileOriginDO = task.getFileOriginDO();
        Long fileOriginId = fileOriginDO.getId();
        String docUId = fileOriginDO.getDocUid();
        String fileName = fileOriginDO.getFileName();
        Long teamId = fileOriginDO.getTeamId();
        Long bookId = fileOriginDO.getBookId();

        FileTaskProgressDatalogDO datalogDO = loadData(task, TaskStepsEnum.TASK_STEP_DESENSITIZATION);
        String data = datalogDO.getData();

        TeamDO teamDO = teamDaoService.getById(teamId);
        if (teamDO == null) {
            log.warn("=====查不到团队信息===task:{}", JSONUtil.toJsonStr(task));
            return TaskOperateEnum.CONTINUE;
        }

        LinkedList<String> contentList = getContentList(data);

        // 删除历史切块信息
        fileChunkDaoService.deleteByFileOriginId(fileOriginId);
        fileChunkDataDaoService.deleteByFileOriginId(fileOriginId);
        // 删除ES切块信息
        esChunkService.deleteChunksByFileOriginId(fileOriginId, bookId);
        milvusChunkDocService.deleteByFileOriginIds(Lists.newArrayList(fileOriginId));

        if (CollUtil.isEmpty(contentList)) {
            // 保存切块后的数据
            recordData(task, "");
            return TaskOperateEnum.CONTINUE;
        }

        String tagNames = fileTagDaoService.getListByFileOriginId(fileOriginId).stream().map(FileTagDO::getName)
            .collect(Collectors.joining(" "));

        // 合并contentList
        contentList = mergeContent(contentList);

        LinkedList<FileChunkDO> fileChunkDOList = new LinkedList<>();
        LinkedList<FileChunkDataDO> chunkDataDOList = new LinkedList<>();
        LinkedList<DocChunkESDO> esChunkList = new LinkedList<>();
        List<RagChunkDTO> chunks = new ArrayList<>();
        for (int i = 0; i < contentList.size(); i++) {
            String content = contentList.get(i);
            String chunkUid = SequenceUtil.getSerialNumberPlus();

            // 切块
            convertFileChunk(chunkUid, i, fileOriginId, fileChunkDOList, bookId);

            // 切块内容
            convertFileChunkData(chunkUid, i, fileOriginId, chunkDataDOList, content);

            // 切块ES
            // ES目录特殊处理
            convertEsDocChunk(chunkUid, fileOriginId, esChunkList, content, docUId, fileName, teamId, tagNames,
                fileOriginDO.getTocs(), bookId);

            // milvus切块
            convertFileMilvusChunkData(chunkUid, fileOriginDO, chunks, content);

        }
        // 批量插入切块信息
        if (CollUtil.isNotEmpty(fileChunkDOList)) {
            fileChunkDaoService.saveBatch(fileChunkDOList);
        }
        // 切块内容数据存入数据库
        if (CollUtil.isNotEmpty(chunkDataDOList)) {
            fileChunkDataDaoService.saveBatch(chunkDataDOList);
        }

        // 保存切块ES信息
        if (CollUtil.isNotEmpty(esChunkList)) {
            esChunkService.batchSaveOrUpdate(esChunkList, bookId);
        }

        // 保存切块milvus信息
        if (CollUtil.isNotEmpty(chunkDataDOList)) {
            milvusChunkDocService.insert(chunks);
        }

        return TaskOperateEnum.CONTINUE;
    }

    private void convertFileMilvusChunkData(String chunkUid, FileOriginDO fileOrigin, List<RagChunkDTO> chunks,
        String content) {
        RagChunkDTO reg = new RagChunkDTO();
        reg.setBookId(fileOrigin.getBookId());
        reg.setFileOriginId(fileOrigin.getId());
        reg.setChunkUid(chunkUid);
        reg.setTocs(fileOrigin.getTocs());
        reg.setChunk(content);

        chunks.add(reg);
    }

    private void convertEsDocChunk(String chunkUid, Long fileOriginId, LinkedList<DocChunkESDO> esChunkList,
        String content, String docUId, String fileName, Long teamId, String tagNameList, String tocs, Long bookId) {
        DocChunkESDO esChunk = new DocChunkESDO();
        esChunk.setChunkUId(chunkUid);
        esChunk.setContent(LanguageProcessUtil.keywordSearchProcess(content));
        esChunk.setFileOriginId(fileOriginId);
        esChunk.setDocId(docUId);
        esChunk.setFileName(fileName);
        esChunk.setTeamId(teamId);
        esChunk.setTags(LanguageProcessUtil.keywordSearchProcess(tagNameList));
        esChunk.setBookId(bookId);
        esChunk.setTocs(LanguageProcessUtil.keywordSearchProcess(tocs));
        esChunkList.add(esChunk);
    }

    private void convertFileChunkData(String chunkUid, Integer sort, Long fileOriginId,
        LinkedList<FileChunkDataDO> chunkDataDOList, String content) {
        FileChunkDataDO chunkDataDO = new FileChunkDataDO();
        chunkDataDO.setChunkUid(chunkUid);
        chunkDataDO.setFileOriginId(fileOriginId);
        chunkDataDO.setData(content);
        chunkDataDO.setSort(sort);
        chunkDataDOList.add(chunkDataDO);
    }

    private void convertFileChunk(String chunkUid, Integer sort, Long fileOriginId,
        LinkedList<FileChunkDO> fileChunkDOList, Long bookId) {
        // 切块
        FileChunkDO chunkDO = new FileChunkDO();
        chunkDO.setUid(chunkUid);
        chunkDO.setFileOriginId(fileOriginId);
        chunkDO.setSort(sort);
        chunkDO.setBookId(bookId);
        fileChunkDOList.add(chunkDO);
    }

    private LinkedList<String> getContentList(String data) {
        Parser parser = Parser.builder().build();
        Node document = parser.parse(data);

        // 构建章节树
        SectionDTO root = new SectionDTO("", "");
        buildSectionTree(document, root, 0);

        LinkedList<String> contentList = new LinkedList<>();
        reBuildContent(root, contentList, "");
        return contentList;
    }

    private List<String> apiChunk(String processContent) {
        Map<String, Object> params = new HashMap<>();
        params.put("txt", processContent);
        params.put("chunkSize", chunkLength);
        params.put("chunkOverlap", chunkOverlap);
        String resp = HttpUtil.post(chunkHost + "/split", JSONUtil.toJsonStr(params));
        // log.info("=====切块返回=====resp:{}", resp);
        if (StringUtils.isEmpty(resp)) {
            log.error("=====切块失败=====processContent:{}", processContent);
        }
        return JSONUtil.toList(resp, String.class);
    }

    private void reBuildContent(SectionDTO sections, LinkedList<String> contentList, String title) {
        if (sections.getProcessContent().length() > 512) {
            if (CollUtil.isNotEmpty(sections.getSubSectionDTOS())) {
                for (SectionDTO s : sections.getSubSectionDTOS()) {
                    StringBuilder titleSb = new StringBuilder();
                    if (StringUtils.isNotBlank(title)) {
                        titleSb.append(title).append("\n");
                    }
                    if (StringUtils.isNotBlank(sections.getTitle())) {
                        titleSb.append(sections.getTitle()).append("\n");
                    }
                    reBuildContent(s, contentList, titleSb.toString());
                }
            } else {
                // 超长文本处理
                List<String> list = chunkOverLimitContent(sections.getProcessContent());
                for (String s : list) {
                    contentList.add((title + sections.getTitle() + "\n" + s + "\n").trim());
                }
            }
        } else {
            contentList.add((title + sections.getTitle() + "\n" + sections.getProcessContent()).trim());
        }
    }

    // 根据标题构建章节树
    private void buildSectionTree(Node document, SectionDTO parentSectionDTO, int level) {
        SectionDTO currentSectionDTO = parentSectionDTO;
        NavigableMap<Integer, SectionDTO> levelMap = new TreeMap<>();
        levelMap.put(level, parentSectionDTO);
        for (Node node = document.getFirstChild(); node != null; node = node.getNext()) {
            // 检查是否为标题节点
            if (node instanceof com.vladsch.flexmark.ast.Heading) {
                String headingContent = node.getChars().toString();
                int headingLevel = ((com.vladsch.flexmark.ast.Heading)node).getLevel();

                // 如果当前标题级别大于父级，创建新的子章节
                if (headingLevel > level) {
                    parentSectionDTO = currentSectionDTO;
                    currentSectionDTO = new SectionDTO(headingContent, "");
                    parentSectionDTO.addSubSection(currentSectionDTO);
                    levelMap.put(headingLevel, currentSectionDTO);
                } else if (headingLevel <= level) {
                    // 从levelMap中找到最接近的父级章节
                    parentSectionDTO = levelMap.lowerEntry(headingLevel).getValue();
                    Integer parentLevel = levelMap.lowerEntry(headingLevel).getKey();
                    currentSectionDTO = new SectionDTO(headingContent, "");
                    parentSectionDTO.addSubSection(currentSectionDTO);
                    // 清除当前父等级之后的所有章节
                    levelMap.tailMap(parentLevel, false).clear();
                    levelMap.put(headingLevel, currentSectionDTO);
                }
                level = headingLevel;
            } else if (currentSectionDTO != null) {
                // 将内容添加到当前章节
                currentSectionDTO.setContent(currentSectionDTO.getContent() + node.getChars());
            }
        }
    }

    /**
     * 合并内容
     * 
     * @param contentList
     * @return
     */
    private LinkedList<String> mergeContent(LinkedList<String> contentList) {
        LinkedList<String> resultList = new LinkedList<>();
        StringBuilder currentString = new StringBuilder();

        for (String str : contentList) {
            // 检查当前字符串的拼接长度
            if (currentString.length() + str.length() <= chunkLength) {
                // 如果可以拼接，则直接拼接
                currentString.append("\n").append(str);
            } else {
                // 如果当前拼接的字符串长度 + 下一个字符串长度大于 512
                // 将当前拼接的字符串添加到结果列表
                if (currentString.length() > 0) {
                    resultList.add(currentString.toString().trim());
                    currentString.setLength(0); // 重置当前字符串
                }

                // 由于当前字符串无法与下一个拼接，直接判断下一个字符串
                if (str.length() <= chunkLength) {
                    currentString.append(str.trim()); // 仅添加当前字符串
                } else {
                    // 当前字符串长度大于512，直接添加到结果列表
                    resultList.add(str.trim());
                }
            }
        }

        // 添加最后一个拼接的字符串（如果有的话）
        if (currentString.length() > 0) {
            resultList.add(currentString.toString());
        }

        return resultList;
    }

    /**
     * 超长内容切块处理
     * 
     * @return
     */
    private List<String> chunkOverLimitContent(String content) {
        if (StringUtils.isEmpty(content)) {
            return new ArrayList<>();
        }
        List<String> segments = MarkdownChunkTableUtil.getSegments(content);
        if (CollUtil.isEmpty(segments)) {
            return new ArrayList<>();
        }

        List<String> resultList = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();

        for (String segment : segments) {
            if (StringUtils.isEmpty(segment)) {
                continue;
            }
            Matcher tableMatcher = MarkdownChunkTableUtil.TABLE_PATTERN.matcher(segment);

            if (!tableMatcher.find()) {
                // 如果没有表格，则直接拼接内容
                if (segment.length() > chunkLength) {
                    // 如果内容超长，则使用API进行切分
                    List<String> chunks = apiChunk(segment);
                    if (CollUtil.isNotEmpty(chunks)) {
                        resultList.addAll(chunks.subList(0, chunks.size() - 1));
                        // 分开的最后一个段落单独处理,去往后拼接
                        currentChunk.append(chunks.get(chunks.size() - 1));
                    }
                } else {
                    // 直接拼接内容
                    MarkdownChunkTableUtil.appendWithNewLine(currentChunk, segment, resultList, chunkLength);
                }
            } else {
                // 如果是表格，则按照表格处理
                MarkdownChunkTableUtil.processTableContent(segment, currentChunk, resultList, chunkLength);
                if (StringUtils.isNotEmpty(currentChunk.toString())) {
                    resultList.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }
            }
        }

        if (currentChunk.length() > 0) {
            resultList.add(currentChunk.toString());
        }

        return resultList;
    }

}
