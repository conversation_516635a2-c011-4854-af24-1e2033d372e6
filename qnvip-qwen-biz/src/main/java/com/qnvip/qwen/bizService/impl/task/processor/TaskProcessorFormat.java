package com.qnvip.qwen.bizService.impl.task.processor;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.enums.FileExtEnum;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileOriginService;
import com.qnvip.qwen.service.TeamBookDocDataService;
import com.qnvip.qwen.util.ExcelJsonToMarkdownTableUtil;
import com.qnvip.qwen.util.MarkdownTableUtil;
import com.qnvip.qwen.util.YuqueLakeBoardJSONToMarkdownUtil;

import cn.hutool.crypto.digest.MD5;
import lombok.extern.slf4j.Slf4j;

/**
 * 把多种格式文件转成md格式
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskProcessorFormat extends BaseTaskProcessor implements TaskProcessor {

    private static final int MAX_SHEET_LINE_COUNT = 1500;
    private static final long MAX_CONTENT_LENGTH = 1000_000L;
    @Resource
    private TeamBookDocDataService teamBookDocDataService;
    @Resource
    private FileOriginService fileOriginService;

    /**
     * 判断字符串中"|\n|"出现的次数是否超过n次
     *
     * @param str 要检查的字符串
     * @param n 次数阈值
     * @return 如果出现次数超过n则返回true，否则返回false
     */
    public static boolean overLength(String str, int n) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        int count = 0;
        int index = 0;
        String target = "|\n|";

        // 循环查找所有出现的位置
        while ((index = str.indexOf(target, index)) != -1) {
            count++;
            // 移动索引到当前找到位置的后面，避免重复计数
            index += target.length();

            // 一旦超过阈值就可以提前返回
            if (count > n) {
                return true;
            }
        }

        return count > n;
    }

    @Override
    public TaskStepsEnum supportStep() {
        return TaskStepsEnum.TASK_STEP_FORMAT;
    }

    @Override
    public TaskOperateEnum execute(TaskProgressDomainModelDTO task) {
        FileOriginDO origin = task.getFileOriginDO();
        TeamBookDocDataDO dataDo = teamBookDocDataService.getByDocUid(origin.getDocUid());
        if (dataDo == null || ObjectUtils.isEmpty(dataDo.getData())) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                supportStep().getDesc() + "data数据丢失doc_uid：" + origin.getDocUid());
        }

        if (dataDo.getData().length() > MAX_CONTENT_LENGTH) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "文件过大 fileId=" + origin.getId() + " 文件大小为" + dataDo.getData().length());
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.XLSX, FileExtEnum.XLS, FileExtEnum.DOC)) {
            // 分大excel 和中小处理方案
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "暂不支持处理的格式" + origin.getFileExtension());
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.MD)) {
            // md格式不转换，直接通过
            String format = MarkdownTableUtil.format(dataDo.getData());
            recordData(task, format);
            return TaskOperateEnum.CONTINUE;
        }
        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.TXT)) {
            // txt格式不转换，直接通过
            recordData(task, dataDo.getData());
            return TaskOperateEnum.CONTINUE;
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.EXCEL_JSON)) {
            List<ExcelJsonToMarkdownTableUtil.SheetData> mds =
                ExcelJsonToMarkdownTableUtil.convertJsonToMarkdownTable(dataDo.getData());
            if (ObjectUtils.isEmpty(mds)) {
                throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                    "数据为空docUid=" + origin.getDocUid());
            } else {

                for (int i = 0; i < mds.size(); i++) {
                    ExcelJsonToMarkdownTableUtil.SheetData e = mds.get(i);
                    if (overLength(e.getData(), MAX_SHEET_LINE_COUNT)) {
                        continue;
                    }
                    // docUid + sheetIndex + data 生成checksum 用来区分不同文档的同一sheet，防止产生相同checksum
                    e.setChecksum(MD5.create().digestHex(dataDo.getDocUid() + " " + i + " " + e.getData()));
                }
                fileOriginService.createMarkdownTableSheet(origin, mds);
                return TaskOperateEnum.FINISH;
            }
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.TABLE_JSON)) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "暂不支持处理的格式docUid=" + origin.getDocUid());
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.BOARD_JSON)) {

            String md = YuqueLakeBoardJSONToMarkdownUtil.lakeboardToMarkdown(dataDo.getData());
            if (ObjectUtils.isEmpty(md)) {
                throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                    "暂不支持处理的格式docUid=" + origin.getDocUid());
            } else {
                recordData(task, md);
                return TaskOperateEnum.CONTINUE;
            }
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.DOCX)) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "暂不支持处理的格式" + origin.getFileExtension());
        }
        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.PDF)) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "暂不支持处理的格式" + origin.getFileExtension());
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.PPT)) {
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "暂不支持处理的格式" + origin.getFileExtension());
        }

        if (FileExtEnum.isIn(origin.getFileExtension(), FileExtEnum.PNG, FileExtEnum.JPG, FileExtEnum.JPEG,
            FileExtEnum.BMP, FileExtEnum.RAW)) {
            // 分大excel 和中小处理方案
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(),
                "暂不支持处理的格式" + origin.getFileExtension());
        }

        throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(), "暂不支持处理的格式" + origin.getFileExtension());

    }

}
