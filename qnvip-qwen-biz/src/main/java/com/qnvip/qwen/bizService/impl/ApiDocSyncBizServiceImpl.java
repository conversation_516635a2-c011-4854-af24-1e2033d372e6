package com.qnvip.qwen.bizService.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.qnvip.qwen.bizService.ApiDocSyncBizService;
import com.qnvip.qwen.bizService.ApiSyncRegistryBizService;
import com.qnvip.qwen.bizService.ApiSyncService;
import com.qnvip.qwen.bizService.WechatBizService;
import com.qnvip.qwen.component.yuque.enums.TocTypeEnum;
import com.qnvip.qwen.dal.dao.FileOriginDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDaoService;
import com.qnvip.qwen.dal.dao.TeamBookDocDataDaoService;
import com.qnvip.qwen.dal.dao.TeamDaoService;
import com.qnvip.qwen.dal.dto.ApiFileDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.TeamBookDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDO;
import com.qnvip.qwen.dal.entity.TeamBookDocDataDO;
import com.qnvip.qwen.dal.entity.TeamDO;
import com.qnvip.qwen.enums.FileExtEnum;
import com.qnvip.qwen.enums.FileOriginTypeEnum;
import com.qnvip.qwen.enums.TeamTypeEnum;
import com.qnvip.qwen.service.FileTaskProgressService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ApiDocSyncBizServiceImpl implements ApiDocSyncBizService {

    protected static final ExecutorService executor =
        new ThreadPoolExecutor(3, 5, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNamePrefix("syncFullCapitalApiData-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    private static final int MAX_RETRY_TIMES = 3;
    private static final int RETRY_INTERVAL_MS = 3000;
    private static final int TIMEZONE_OFFSET_HOURS = 8;
    @Resource
    private WechatBizService wechatBizService;
    @Resource
    private TeamDaoService teamDaoService;
    @Resource
    private TeamBookDaoService teamBookDaoService;
    @Resource
    private TeamBookDocDaoService teamBookDocDaoService;
    @Resource
    private FileOriginDaoService fileOriginDaoService;
    @Resource
    private TeamBookDocDataDaoService teamBookDocDataDaoService;
    @Resource
    private FileTaskProgressService fileTaskProgressService;
    @Resource
    private ApiSyncRegistryBizService apiSyncRegistryBizService;

    @Override
    public void syncBook() {
        List<TeamDO> teams = teamDaoService.getAll(TeamTypeEnum.API);
        if (CollUtil.isEmpty(teams)) {
            return;
        }
        for (TeamDO t : teams) {
            Long teamId = t.getId();
            List<String> list = apiSyncRegistryBizService.bookNames();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            List<TeamBookDO> teamBookDOList = new ArrayList<>();
            Map<String, TeamBookDO> dbBookMap = teamBookDaoService.getMapByTeamIdAndMark(teamId, list);

            for (String book : list) {
                TeamBookDO dbData = dbBookMap.get(book);
                TeamBookDO teamBookDO = new TeamBookDO();
                if (dbData != null) {
                    teamBookDO.setId(dbData.getId());
                }
                teamBookDO.setMark(book);
                teamBookDO.setTeamMark(t.getMark());
                teamBookDO.setTeamId(teamId);
                teamBookDO.setName(book);
                teamBookDO.setType(TeamTypeEnum.API.getCode());
                teamBookDO.setDescription(book);
                teamBookDO.setContentUpdateTime(LocalDateTime.now().plusHours(8));
                teamBookDO.setCreateTime(LocalDateTime.now());
                teamBookDOList.add(teamBookDO);
            }
            if (CollUtil.isNotEmpty(teamBookDOList)) {
                teamBookDaoService.saveOrUpdateBatch(teamBookDOList);
            }
        }
    }

    /**
     * 同步知识库目录
     */
    @Override
    public void syncBookToc() {
        List<TeamBookDO> teamBooks = teamBookDaoService.getAll(TeamTypeEnum.API);
        if (CollUtil.isEmpty(teamBooks)) {
            return;
        }
        for (TeamBookDO t : teamBooks) {
            List<String> list = new ArrayList<>();
            try {
                list = apiSyncRegistryBizService.tocNames();
                if (CollUtil.isEmpty(list)) {
                    return;
                }
                // 语雀目录uuid
                Map<String, Long> dbDocMap = teamBookDocDaoService.getMapByBookIdAndUid(t.getId(), list);
                Map<String, Long> dbFileMap = fileOriginDaoService.getMapByBookIdAndUid(t.getId(), list);
                List<TeamBookDocDO> processDocList = new ArrayList<>();
                List<FileOriginDO> processFileList = new ArrayList<>();
                for (String r : list) {
                    this.processDocList(processDocList, dbDocMap, r, t.getTeamId(), t.getId());
                    this.processFileList(processFileList, dbFileMap, r, t.getTeamId(), t.getId());
                }
                if (CollUtil.isNotEmpty(processDocList)) {
                    teamBookDocDaoService.saveOrUpdateBatch(processDocList);
                }
                if (CollUtil.isNotEmpty(processFileList)) {
                    fileOriginDaoService.saveOrUpdateBatch(processFileList);
                }

            } catch (Exception e) {
                log.error("======同步api目录录异常======tocNames:{}", list, e);
            }
        }

    }

    @Override
    public void syncBookData() {
        List<TeamBookDO> teamBooks = teamBookDaoService.getAll(TeamTypeEnum.API);
        if (CollUtil.isEmpty(teamBooks)) {
            return;
        }

        List<String> slugList = apiSyncRegistryBizService.tocNames();

        for (TeamBookDO book : teamBooks) {
            Long bookId = book.getId();

            List<ApiSyncService> syncServices = apiSyncRegistryBizService.getByBookName(book.getName());
            if (CollUtil.isEmpty(syncServices)) {
                continue;
            }

            for (ApiSyncService syncService : syncServices) {
                Map<String, TeamBookDocDataDO> dataMap =
                    teamBookDocDataDaoService.getMapByBookIdAndMarkList(slugList, bookId);
                Map<String, TeamBookDocDO> docMap = teamBookDocDaoService.getMapByBookIdAndMarkList(bookId, slugList);

                List<TeamBookDocDataDO> dataList = new ArrayList<>();
                List<String> docUidList = new ArrayList<>();

                ApiFileDTO data = syncService.getData();

                String docSlug = data.getFileName();
                TeamBookDocDO dbDoc = docMap.get(data.getFileName());
                if (dbDoc == null) {
                    continue;
                }

                TeamBookDocDataDO newData = processDocData(data, dataMap.get(docSlug), dbDoc.getUid(), docSlug, bookId);
                if (newData != null) {
                    dataList.add(newData);
                    docUidList.add(dbDoc.getUid());
                }

                if (CollUtil.isNotEmpty(dataList)) {
                    teamBookDocDataDaoService.saveOrUpdateBatch(dataList);
                    Map<String, Long> dbFileMap = fileOriginDaoService.getMapByBookIdAndUid(bookId, docUidList);
                    List<Long> fileIds = dbFileMap.values().stream().distinct().collect(Collectors.toList());
                    List<FileOriginDO> processFileList = new ArrayList<>();
                    dataList.stream().filter(d -> !d.getType().equals(FileExtEnum.MD.getDesc())).forEach(d -> {
                        Long fileId = dbFileMap.get(d.getDocUid());
                        if (fileId != null) {
                            FileOriginDO file = new FileOriginDO();
                            file.setId(fileId);
                            file.setFileExtension(d.getType());
                            processFileList.add(file);
                        }
                    });
                    // 为了更新fileOrigin的fileExtension字段，需要更新操作
                    if (CollUtil.isNotEmpty(processFileList)) {
                        fileOriginDaoService.updateBatchById(processFileList);
                    }
                    if (CollUtil.isNotEmpty(fileIds)) {
                        // 创建文档处理任务
                        fileTaskProgressService.saveTaskStartWithFormat(bookId, fileIds);
                    }

                }
            }

        }
    }

    private TeamBookDocDataDO processDocData(ApiFileDTO doc, TeamBookDocDataDO dbData, String docUid, String docSlug,
        Long bookId) {

        LocalDateTime time = LocalDateTime.now().plusHours(TIMEZONE_OFFSET_HOURS);
        if (dbData != null && !time.isAfter(dbData.getContentUpdateTime())) {
            return null;
        }

        TeamBookDocDataDO newData = new TeamBookDocDataDO();
        if (dbData != null) {
            newData.setId(dbData.getId());
        }
        newData.setDocUid(docUid);
        newData.setType(FileExtEnum.MD.getDesc());
        newData.setData(doc.getData());
        newData.setMark(docSlug);
        newData.setContentUpdateTime(time);
        newData.setBookId(bookId);

        if (ObjectUtils.isEmpty(newData.getData()) || ObjectUtils.isEmpty(newData.getData().trim())) {
            return null;
        }
        return newData;
    }

    private void processDocList(List<TeamBookDocDO> processDocList, Map<String, Long> dbDocMap, String r, Long teamId,
        Long bookId) {
        Long dbDataId = dbDocMap.get(r);
        TeamBookDocDO docDO = new TeamBookDocDO();
        if (dbDataId != null) {
            docDO.setId(dbDataId);
        }
        docDO.setTeamId(teamId);
        docDO.setBookId(bookId);
        docDO.setUid(r);
        docDO.setParentId(0L);
        docDO.setType(TocTypeEnum.DOC.getCode());
        docDO.setName(r);
        docDO.setMark(r);
        docDO.setParentUid("");
        processDocList.add(docDO);
    }

    private void processFileList(List<FileOriginDO> processFileList, Map<String, Long> dbFileMap, String r, Long teamId,
        Long bookId) {

        Long dbDataId = dbFileMap.get(r);
        FileOriginDO fileDO = new FileOriginDO();
        String fileExtension = FileExtEnum.MD.getDesc();
        if (dbDataId != null) {
            fileDO.setId(dbDataId);
            fileExtension = null;
        }
        fileDO.setTeamId(teamId);
        fileDO.setParentId(0L);
        fileDO.setBookId(bookId);
        fileDO.setDocUid(r);
        fileDO.setDocTableUid("");
        fileDO.setFileName(r);
        fileDO.setChecksum(r);
        // 语雀文档的扩展名是md
        fileDO.setFileExtension(fileExtension);
        fileDO.setType(FileOriginTypeEnum.API.getCode());
        fileDO.setTargetOriginUrl(r);

        processFileList.add(fileDO);
    }
}
