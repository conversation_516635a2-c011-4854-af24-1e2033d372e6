package com.qnvip.qwen.bizService.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.qnvip.common.exception.BusinessException;
import com.qnvip.qwen.bizService.MilvusDataInitBizService;
import com.qnvip.qwen.dal.dao.FileChunkDaoService;
import com.qnvip.qwen.dal.dao.FileQaDaoService;
import com.qnvip.qwen.dal.dto.DataInitSearchDTO;
import com.qnvip.qwen.dal.dto.FileGraphExtractDataDTO;
import com.qnvip.qwen.dal.dto.RagChunkDTO;
import com.qnvip.qwen.dal.milvus.MilvusChunkDocService;
import com.qnvip.qwen.dal.milvus.MilvusGraphEntityService;
import com.qnvip.qwen.dal.milvus.MilvusGraphRelationService;
import com.qnvip.qwen.dal.milvus.MilvusQaLinkService;
import com.qnvip.qwen.enums.ErrorCodeEnum;
import com.qnvip.qwen.service.FileGraphService;
import com.qnvip.qwen.service.GraphMilvusService;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月15日 15:16:00
 */
@Slf4j
@Service
public class MilvusDataInitBizServiceImpl implements MilvusDataInitBizService {


    @Resource
    private FileQaDaoService fileQaDaoService;
    @Resource
    private FileChunkDaoService fileChunkDaoService;
    @Resource
    private MilvusQaLinkService milvusQaLinkService;
    @Resource
    private MilvusChunkDocService milvusChunkDocService;
    @Resource
    private FileGraphService fileGraphService;
    @Resource
    private GraphMilvusService graphMilvusService;

    @Resource
    private MilvusGraphEntityService milvusGraphEntityService;

    @Resource
    private MilvusGraphRelationService milvusGraphRelationService;

    protected static final ExecutorService executor =
        new ThreadPoolExecutor(10, 15, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNameFormat("MilvusDataInitService-executor-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());



    @Override
    public void initBookChunk(DataInitSearchDTO param) {
        int pageNo = 0;
        int pageSize = 50;

        while (true) {

            param.setLimitSql(" limit " + pageNo * pageSize + "," + pageSize);
            List<Long> fileOriginIds = fileChunkDaoService.getDistinctOriginId(param);
            if (CollUtil.isEmpty(fileOriginIds)) {
                break;
            }

            List<RagChunkDTO> list = fileChunkDaoService.getMilvusInitList(fileOriginIds);
            if (CollUtil.isEmpty(list)) {
                continue;
            }

            list = list.stream()
                .collect(Collectors.groupingBy(RagChunkDTO::getFileOriginId, LinkedHashMap::new, Collectors.toList()))
                .values().stream().flatMap(group -> group.stream().limit(300)).collect(Collectors.toList());

            log.info("====MilvusDataInitServiceImpl.initBookChunk=====当前页{}", pageNo);
            pageNo++;
            milvusChunkDocService.deleteByFileOriginIds(fileOriginIds);

            List<List<RagChunkDTO>> partitions = ListUtils.partition(list, 500);
            for (List<RagChunkDTO> partition : partitions) {
                milvusChunkDocService.insert(partition);
            }

            log.info("====MilvusDataInitServiceImpl.initBookChunk=====当前页完成{}", pageNo);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        log.info("====MilvusDataInitServiceImpl.initBookChunk=====结束");
    }


    @Override
    public void initBookQaLink(DataInitSearchDTO param) {

        int pageNo = 0;
        int pageSize = 50;

        while (true) {

            param.setLimitSql(" limit " + pageNo * pageSize + "," + pageSize);
            List<Long> fileOriginIds = fileChunkDaoService.getDistinctOriginId(param);
            if (CollUtil.isEmpty(fileOriginIds)) {
                break;
            }

            List<RagChunkDTO> list = fileQaDaoService.getMilvusInitList(fileOriginIds);
            if (CollUtil.isEmpty(list)) {
                continue;

            }

            list = list.stream()
                .collect(Collectors.groupingBy(RagChunkDTO::getFileOriginId, LinkedHashMap::new, Collectors.toList()))
                .values().stream().flatMap(group -> group.stream().limit(3000)).collect(Collectors.toList());

            log.info("====MilvusDataInitServiceImpl.initBookQaLink=====当前页{}", pageNo);
            pageNo++;
            milvusQaLinkService.deleteByFileOriginIds(fileOriginIds);

            List<List<RagChunkDTO>> partitions = ListUtils.partition(list, 500);
            for (List<RagChunkDTO> partition : partitions) {
                milvusQaLinkService.insert(partition);
            }

            log.info("====MilvusDataInitServiceImpl.initBookQaLink=====当前页完成{}", pageNo);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }


        log.info("====MilvusDataInitServiceImpl.initBookQaLink=====结束");
    }

    @Override
    public void initGraphAndRelation(DataInitSearchDTO param) {
        try {
            log.info("====MilvusDataInitServiceImpl.initGraphAndRelation 开始");
            // processMilvusEntity(param);
            //
            // processMilvusRelation(param);

            processGraphRelation(param);
            log.info("====MilvusDataInitServiceImpl.initGraphAndRelation 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw BusinessException.instance(ErrorCodeEnum.SYSTEM_ERR.getCode(), e.getMessage());
        }

    }

    private void processMilvusRelation(DataInitSearchDTO param) {
        int pageNo = 0;
        int pageSize = 500;

        while (true) {

            param.setLimitSql(" limit " + pageNo * pageSize + "," + pageSize);

            List<Long> fileOriginIds = fileGraphService.getDistinctOriginId(param);
            if (CollUtil.isEmpty(fileOriginIds)) {
                break;
            }

            List<FileGraphExtractDataDTO> list = fileGraphService.pageLoadRelation(fileOriginIds);
            if (CollUtil.isEmpty(list)) {
                continue;

            }

            list = list.stream()
                .collect(Collectors.groupingBy(FileGraphExtractDataDTO::getFileOriginId, LinkedHashMap::new,
                    Collectors.toList()))
                .values().stream().flatMap(group -> group.stream().limit(3000)).collect(Collectors.toList());

            log.info("====MilvusDataInitServiceImpl.processMilvusRelation.processMilvusRelation=====当前页{}", pageNo);
            pageNo++;

            milvusGraphRelationService.deleteByFileOriginIds(fileOriginIds);

            List<List<FileGraphExtractDataDTO>> partitions = ListUtils.partition(list, 500);
            for (List<FileGraphExtractDataDTO> partition : partitions) {
                graphMilvusService.saveBatchToMilvus(partition);
            }

            log.info("====MilvusDataInitServiceImpl.processMilvusRelation.processMilvusRelation=====当前页完成{}", pageNo);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void processMilvusEntity(DataInitSearchDTO param) {
        int pageNo = 0;
        int pageSize = 500;

        while (true) {
            param.setLimitSql(" limit " + pageNo * pageSize + "," + pageSize);
            List<Long> fileOriginIds = fileGraphService.getDistinctOriginId(param);
            if (CollUtil.isEmpty(fileOriginIds)) {
                break;
            }

            List<FileGraphExtractDataDTO> list = fileGraphService.pageLoadEntity(fileOriginIds);
            if (CollUtil.isEmpty(list)) {
                continue;
            }

            list = list.stream()
                .collect(Collectors.groupingBy(FileGraphExtractDataDTO::getFileOriginId, LinkedHashMap::new,
                    Collectors.toList()))
                .values().stream().flatMap(group -> group.stream().limit(3000)).collect(Collectors.toList());

            log.info("====MilvusDataInitServiceImpl.processMilvusEntity.processEntity=====当前页{}", pageNo);
            pageNo++;
            milvusGraphEntityService.deleteByFileOriginIds(fileOriginIds);

            List<List<FileGraphExtractDataDTO>> partitions = ListUtils.partition(list, 500);
            for (List<FileGraphExtractDataDTO> partition : partitions) {
                graphMilvusService.saveBatchToMilvus(partition);
            }

            log.info("====MilvusDataInitServiceImpl.processMilvusEntity.processEntity=====当前页完成{}", pageNo);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void processGraphRelation(DataInitSearchDTO param) {
        int pageNo = 0;
        int pageSize = 50;
        fileGraphService.deleteGraphAll();
        while (true) {

            param.setLimitSql(" limit " + pageNo * pageSize + "," + pageSize);

            List<Long> fileOriginIds = fileGraphService.getDistinctOriginId(param);
            if (CollUtil.isEmpty(fileOriginIds)) {
                break;
            }

            List<FileGraphExtractDataDTO> list = fileGraphService.pageLoadRelation(fileOriginIds);
            if (CollUtil.isEmpty(list)) {
                continue;
            }

            list = list.stream()
                .collect(Collectors.groupingBy(FileGraphExtractDataDTO::getFileOriginId, LinkedHashMap::new,
                    Collectors.toList()))
                .values().stream().flatMap(group -> group.stream().limit(3000)).collect(Collectors.toList());

            log.info("====MilvusDataInitServiceImpl.initGraphAndRelation.processRelation=====当前页{}", pageNo);
            pageNo++;

            List<List<FileGraphExtractDataDTO>> partitions = ListUtils.partition(list, 1000);
            for (List<FileGraphExtractDataDTO> partition : partitions) {
                fileGraphService.saveBatchToGraphByRelation(partition);
            }

            log.info("====MilvusDataInitServiceImpl.initGraphAndRelation.processRelation=====当前页完成{}", pageNo);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
