package com.qnvip.qwen.bizService.impl.task;

import java.util.Arrays;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.qnvip.qwen.bizService.TaskDomainBizService;
import com.qnvip.qwen.bizService.impl.task.processor.TaskProcessorRegistry;
import com.qnvip.qwen.dal.dao.FileTaskProgressDaoService;
import com.qnvip.qwen.dal.dto.ai.TaskProgressDomainModelDTO;
import com.qnvip.qwen.dal.entity.FileOriginDO;
import com.qnvip.qwen.dal.entity.FileTaskProgressDO;
import com.qnvip.qwen.enums.TaskOperateEnum;
import com.qnvip.qwen.enums.TaskStepsEnum;
import com.qnvip.qwen.service.FileOriginService;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TaskDomainBizServiceImpl implements TaskDomainBizService {
    @Resource
    private FileTaskProgressDaoService taskProgressRepository;
    @Resource
    private TaskProcessorRegistry taskProcessorRegistry;
    @Resource
    private FileOriginService fileOriginService;

    @Override
    public void processTask(Long taskId, Boolean nextTaskFlag) {
        FileTaskProgressDO taskDo = taskProgressRepository.getById(taskId);
        if (taskDo == null) {
            return;
        }

        nextTaskFlag = Convert.toBool(nextTaskFlag, true);

        TaskProgressDomainModelDTO task = TaskProgressDomainModelDTO.toDomainModel(taskDo);
        FileOriginDO origin = fileOriginService.getById(task.getFileOriginId());
        if (origin == null) {
            return;
        }

        task.setFileOriginDO(origin);

        try {
            // 任务开始，修改状态
            task.start();
            taskProgressRepository.saveOrUpdate(task.toDo());
            log.info("任务开始-taskId={} step={}", taskId, TaskStepsEnum.getByCode(task.getNowStep().getCode()));

            // 执行实际任务处理
            log.info("执行步骤{} {}", task.getId(), task.getNowStep().getDesc());

            TaskOperateEnum operate = taskProcessorRegistry.execute(task);

            log.info("执行完成{} {}", task.getId(), task.getNowStep().getDesc());
            if (operate == TaskOperateEnum.FINISH) {
                nextTaskFlag = false;
            }

            // 任务完成，如果有下一任务，则开启下一任务步骤
            Boolean finalNextTaskFlag = nextTaskFlag;
            task.complete().ifPresent(e -> {
                if (finalNextTaskFlag) {
                    FileTaskProgressDO aDo = e.toDo();
                    aDo.setBeforeProgressId(task.getId());
                    taskProgressRepository.saveOrUpdate(aDo);
                    log.info("任务开启下一任务-beforeTaskId={} step={}", aDo.getBeforeProgressId(), e.getNowStep().getDesc());
                }
            });

            // 任务完成，修改状态
            taskProgressRepository.saveOrUpdate(task.toDo());
            log.info("任务结束-taskId={} file={} step={}", taskId, task.getNowStep().getDesc(),
                TaskStepsEnum.getByCode(task.getNowStep().getCode()));

        } catch (Exception e) {
            handleFailure(task,
                e.getMessage() == null
                    ? e.getClass().getName()
                        + JSON.toJSONString(Arrays.stream(e.getStackTrace()).limit(3).collect(Collectors.toList()))
                    : e.getMessage());
            log.error("任务失败-taskId={} step={} errorMsg={}", taskId, task.getNowStep().getDesc(), e.getMessage());
        }
    }

    private void handleFailure(TaskProgressDomainModelDTO task, String errorMessage) {
        if (task.isRetryAllowed()) {
            task.retry(errorMessage);
        } else {
            task.markAsFailed(errorMessage);
        }
        taskProgressRepository.saveOrUpdate(task.toDo());
    }

}
