package com.qnvip.qwen.manage;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@MapperScan("com.qnvip.qwen.dal.mapper")
@ComponentScan({"com.qnvip.oauth2.security", "com.qnvip.qwen"})
@SpringBootApplication
public class ManageApplication {
    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        SpringApplication.run(ManageApplication.class, args);
        log.info("================ AppApplication start success 耗时: {}ms================",
            System.currentTimeMillis() - start);
    }
}
