package com.qnvip.qwen.manage.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2ClientProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.oauth2.security.OAuth2ApiProvider;
import com.qnvip.oauth2.security.config.SecurityContext;
import com.qnvip.oauth2.security.config.exception.Oauth2Exception;
import com.qnvip.oauth2.security.constants.OAuth2Constants;
import com.qnvip.oauth2.security.vo.AdminVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class BaseController {

    protected static final String REQUEST_HEADER_TOKEN_FLAG = "Authorization";
    protected static final String OAUTH2_TOKEN_FLAG = "access_token";
    protected static final String TOKEN_PREFIX = "Bearer ";
    protected static final String LOCAL_IP = "X-Real-IP";

    @Resource
    protected HttpServletRequest request;
    @Resource
    protected OAuth2ApiProvider oAuth2ApiProvider;
    @Resource
    protected StringRedisTemplate stringRedisTemplate;
    @Resource
    protected OAuth2ClientProperties oAuth2ClientProperties;

    protected String getAccessToken() {
        String token = this.request.getHeader(REQUEST_HEADER_TOKEN_FLAG);
        if (StringUtils.isBlank(token)) {
            String[] accessToken = this.request.getParameterMap().get(OAUTH2_TOKEN_FLAG);
            if (accessToken != null && accessToken.length > 0) {
                token = accessToken[0];
            }
        }
        if (token != null && !Strings.isEmpty(token)) {
            return token.replaceAll(TOKEN_PREFIX, "");
        } else {
            throw Oauth2Exception.instance("用户未登录，请刷新页面登录后使用");
        }
    }

    protected AdminVO getLoginUserInfo() {
        String userNamespace =
            OAuth2Constants.userNamespace(SecurityContext.getLoginUserId(), this.oAuth2ClientProperties.getClientId());
        String admin = this.stringRedisTemplate.opsForValue().get(userNamespace);
        if (admin == null) {
            throw Oauth2Exception.instance("用户未登录，请刷新页面登录后使用");
        } else {
            AdminVO adminDto = JSONUtil.toBean(admin, AdminVO.class);
            if (adminDto == null) {
                throw Oauth2Exception.instance("用户未登录，请刷新页面登录后使用");
            } else {
                return adminDto;
            }
        }
    }

    protected Long getLoginUserId() {
        return SecurityContext.getLoginUserId().longValue();
    }

    protected String getLoginUserName() {
        return SecurityContext.getLoginUserName();
    }

    protected String getLoginName() {
        return SecurityContext.getLoginName();
    }

    protected Map<Integer, AdminVO> getAllAdminInfo() {
        Map<Integer, AdminVO> map = new HashMap<>();
        String token = this.getAccessToken();
        List<AdminVO> list = this.oAuth2ApiProvider.getAllAdminInfo(token);
        if (!CollUtil.isEmpty(list)) {
            list.forEach((adminInfo) -> {
                map.put(adminInfo.getId(), adminInfo);
            });
        }

        return map;
    }

    protected List<AdminVO> getAllAdminList() {
        String token = this.getAccessToken();
        return this.oAuth2ApiProvider.getAllAdminInfo(token);
    }

    protected Map<Integer, AdminVO> getAllAdminInfoByTag(String tag) {
        Map<Integer, AdminVO> map = new HashMap<>();
        String token = this.getAccessToken();
        List<AdminVO> list = this.oAuth2ApiProvider.getAllAdminInfoByTag(token, tag);
        if (!CollUtil.isEmpty(list)) {
            list.forEach((adminInfo) -> {
                map.put(adminInfo.getId(), adminInfo);
            });
        }

        return map;
    }

    protected AdminVO getAdminInfoById(Integer id) {
        return this.oAuth2ApiProvider.getAdminInfoById(id);
    }

}
