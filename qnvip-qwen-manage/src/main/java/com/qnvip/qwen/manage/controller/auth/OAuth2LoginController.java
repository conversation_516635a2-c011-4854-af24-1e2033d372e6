package com.qnvip.qwen.manage.controller.auth;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.util.IPUtils;
import com.qnvip.oauth2.security.config.exception.Oauth2Exception;
import com.qnvip.oauth2.security.enums.AdminLogTypeEnum;
import com.qnvip.oauth2.security.vo.AdminVO;
import com.qnvip.qwen.manage.controller.BaseController;
import com.qnvip.qwen.vo.request.AuthBodyReq;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * create by gw on 2022/5/19
 */
@Api(tags = "后台登录")
@Slf4j
@RestController
@RequestMapping("/auth/")
public class OAuth2LoginController extends BaseController {

    @GetMapping("/app")
    @ApiOperation("返回跳转信息")
    public ResultVO<Object> app() {
        Map<String, Object> oAuth2Info = oAuth2ApiProvider.getOAuth2Info();
        return ResultVO.of(oAuth2Info);
    }

    @PostMapping("login")
    @ApiOperation("登录")
    public ResultVO<Object> login(@RequestBody AuthBodyReq authBodyReq) {
        String code = authBodyReq.getCode();
        String redirectUri = authBodyReq.getRedirectUri();
        try {
            log.info(" login code:{}, redirectUri: {}", code, redirectUri);
            String token = oAuth2ApiProvider.getOAuth2Token(code, redirectUri);
            // String token = resultMap.get(OAUTH2_TOKEN_FLAG) == null ? null :
            // String.valueOf(resultMap.get(OAUTH2_TOKEN_FLAG));
            if (StringUtils.isEmpty(token)) {
                throw Oauth2Exception.instance("登录失败，请检查code是否正确");
            }
            AdminVO adminInfoVo = oAuth2ApiProvider.getAdminInfo(token);
            if (adminInfoVo == null) {
                throw Oauth2Exception.instance("获取登录用户信息失败，请联系管理员");
            }
            // 把菜单资源，权限放入redis
            oAuth2ApiProvider.initResources(token);
            // 登录日志
            log.info("======" + JSONUtil.toJsonStr(this.request.getHeaderNames()));
            oAuth2ApiProvider.saveAminLog(AdminLogTypeEnum.LOGIN, IPUtils.getIpAddressWithDot(request),
                this.request.getHeader(LOCAL_IP), token);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("Authorization", "Bearer " + token);
            resultMap.put("staff", adminInfoVo);

            return ResultVO.of(resultMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultVO.error(104, e.getMessage());
        }

    }

    @PostMapping("refreshToken")
    @ApiOperation("刷新token")
    public ResultVO<Object> refreshToken(String refreshToken) {
        try {
            Map<String, Object> resultMap = oAuth2ApiProvider.refreshToken(refreshToken);
            String token =
                resultMap.get(OAUTH2_TOKEN_FLAG) == null ? null : String.valueOf(resultMap.get(OAUTH2_TOKEN_FLAG));
            if (StringUtils.isEmpty(token)) {
                throw Oauth2Exception.instance("登录失败，请检查code是否正确");
            }
            return ResultVO.of(resultMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultVO.error(104, e.getMessage());
        }
    }

    @PostMapping("logout")
    @ApiOperation("注销")
    public ResultVO<Object> logout() {
        String token = super.getAccessToken();
        oAuth2ApiProvider.logout(token);
        oAuth2ApiProvider.cleanResources();
        return ResultVO.of(null);
    }

}
