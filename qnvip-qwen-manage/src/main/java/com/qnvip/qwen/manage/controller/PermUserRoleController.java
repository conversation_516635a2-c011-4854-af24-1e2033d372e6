package com.qnvip.qwen.manage.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.util.AssertUtils;
import com.qnvip.oauth2.security.vo.AdminVO;
import com.qnvip.qwen.service.PermUserRoleService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.RoleAssignUsersReq;
import com.qnvip.qwen.vo.request.UserIdReq;
import com.qnvip.qwen.vo.request.UserRoleTokenReq;
import com.qnvip.qwen.vo.response.AdminAndTokenInfoResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户角色表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Api(tags = "用户角色表")
@RestController
@RequestMapping(value = "/api/permUserRole")
public class PermUserRoleController extends BaseController {

    @Resource
    private PermUserRoleService permUserRoleService;

    @ApiOperation("批量分配角色给用户")
    @PostMapping("assignUsers")
    public ResultVO<Boolean> assignUsers(@RequestBody RoleAssignUsersReq req) {
        AssertUtils.checkNotNull(req.getRoleId(), "角色ID不能为空");
        AssertUtils.checkNotNull(req.getUserIds(), "用户ID列表不能为空");
        return ResultVO.of(permUserRoleService.assignUsers(req.getRoleId(), req.getUserIds()));
    }

    @ApiOperation("获取用户的角色和token")
    @PostMapping("detail")
    public ResultVO<AdminAndTokenInfoResp> detail(@RequestBody UserIdReq userIdReq) {
        AdminVO admin = super.getAdminInfoById(Math.toIntExact(userIdReq.getUserId()));
        AdminAndTokenInfoResp adminResp = CopierUtil.copy(admin, AdminAndTokenInfoResp.class);
        permUserRoleService.putTokenAndRoles(adminResp);
        return ResultVO.of(adminResp);
    }

    @ApiOperation("更新用户角色和Token")
    @PostMapping("updateTokenAndRoles")
    public ResultVO<Boolean> update(@RequestBody UserRoleTokenReq req) {
        AssertUtils.checkNotNull(req.getUserId(), "用户ID不能为空");
        AssertUtils.checkNotNull(req.getRoleIds(), "角色ID列表不能为空");
        permUserRoleService.updateTokenAndRoles(req);
        return ResultVO.of(true);
    }

}