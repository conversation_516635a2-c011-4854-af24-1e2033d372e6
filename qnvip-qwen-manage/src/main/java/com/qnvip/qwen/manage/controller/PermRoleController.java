package com.qnvip.qwen.manage.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.util.AssertUtils;
import com.qnvip.qwen.service.PermRoleResourceService;
import com.qnvip.qwen.service.PermRoleService;
import com.qnvip.qwen.vo.request.IdReq;
import com.qnvip.qwen.vo.request.PermRoleReq;
import com.qnvip.qwen.vo.request.RoleAssignBooksReq;
import com.qnvip.qwen.vo.response.PermRoleDetailResp;
import com.qnvip.qwen.vo.response.PermRoleResp;
import com.qnvip.qwen.vo.response.TeamResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/05/06 11:41
 */
@Api(tags = "角色表")
@RestController
@RequestMapping(value = "/api/permRole")
public class PermRoleController extends BaseController {

    @Resource
    private PermRoleService permRoleService;

    @Resource
    private PermRoleResourceService permRoleResourceService;

    @ApiOperation(value = "角色列表")
    @PostMapping("list")
    public ResultVO<List<PermRoleResp>> list(@RequestBody PermRoleReq req) {
        return ResultVO.of(permRoleService.list(req));
    }

    @ApiOperation("角色表删除")
    @PostMapping("delete")
    public ResultVO<Boolean> delete(@RequestBody PermRoleReq record) {
        AssertUtils.checkNotBlank(String.valueOf(record.getId()), "id不能为空");
        return ResultVO.of(permRoleService.delete(record.getId()));
    }


    @ApiOperation("新建或修改角色同时分配知识库给角色")
    @PostMapping("assignBooks")
    public ResultVO<Boolean> assignBooks(@RequestBody RoleAssignBooksReq req) {
        AssertUtils.checkNotBlank(req.getRoleName(), "角色名称不能为空");
        AssertUtils.checkNotNull(req.getBookIds(), "知识库ID列表不能为空");
        return ResultVO.of(permRoleResourceService.assignBooks(req));
    }


    @ApiOperation("获取角色详情")
    @PostMapping("getDetail")
    public ResultVO<PermRoleDetailResp> getDetail(@RequestBody IdReq idReq) {
        AssertUtils.checkNotNull(idReq.getId(), "角色ID不能为空");
        return ResultVO.of(permRoleService.getDetail(idReq.getId()));
    }

    @ApiOperation("获取所有的团队和知识库")
    @PostMapping("getTeamBooks")
    public ResultVO<List<TeamResp>> getTeamBooks() {
        return ResultVO.of(permRoleService.getTeamBooks());
    }
}