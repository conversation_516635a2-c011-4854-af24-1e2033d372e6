package com.qnvip.qwen.manage.controller;

import java.io.IOException;
import java.util.UUID;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.qnvip.common.base.ResultVO;
import com.qnvip.qwen.util.FileSystemUtil;
import com.qnvip.qwen.util.FileUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "文件")
@RestController
@RequestMapping("/api/file")
public class FileController extends BaseController {

    @ApiOperation("上传")
    @PostMapping("/upload")
    public ResultVO<String> uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            return ResultVO.of("文件不能为空");
        }

        String fileName =
            UUID.randomUUID().toString().replace("-", "") + "." + FileUtil.getFileExtension(file.getOriginalFilename());
        // 获取文件内容
        byte[] fileData = file.getBytes();

        // 调用工具类上传文件
        String fileUrl = FileSystemUtil.upload(fileData, fileName);

        // 返回文件上传后的访问地址
        return ResultVO.of(fileUrl);
    }
}