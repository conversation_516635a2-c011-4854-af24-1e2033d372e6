package com.qnvip.qwen.manage.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.util.AssertUtils;
import com.qnvip.oauth2.security.vo.AdminVO;
import com.qnvip.qwen.service.PermRoleService;
import com.qnvip.qwen.service.PermUserRoleService;
import com.qnvip.qwen.service.UserTokenConfigService;
import com.qnvip.qwen.util.CopierUtil;
import com.qnvip.qwen.vo.request.AdminInfoGetReq;
import com.qnvip.qwen.vo.request.IdReq;
import com.qnvip.qwen.vo.response.AdminAndRoleAssignResp;
import com.qnvip.qwen.vo.response.AdminAndTokenInfoResp;
import com.qnvip.qwen.vo.response.PermRoleResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @desc 描述
 * @createTime 2025年04月14日 17:17:00
 */
@Api(tags = "用户")
@RestController
@RequestMapping(value = "/manage/user")
public class UserController extends BaseController {

    @Resource
    private UserTokenConfigService userTokenConfigService;

    @Resource
    private PermRoleService permRoleService;

    @Resource
    private PermUserRoleService permUserRoleService;

    @ApiOperation("搜索用户列表")
    @PostMapping("search")
    public ResultVO<List<AdminAndTokenInfoResp>> search(@RequestBody AdminInfoGetReq req) {
        return ResultVO.of(getAdminAndTokenInfoResps(req));
    }

    @ApiOperation("根据角色id获取已分配的用户的列表")
    @PostMapping("assignRoleUsers")
    public ResultVO<List<AdminAndRoleAssignResp>> assignRoleUsers(@RequestBody AdminInfoGetReq record) {
        AssertUtils.checkNotBlank(String.valueOf(record.getId()), "id不能为空");
        List<AdminVO> allAdminList = getAdminVOS(record);
        List<AdminAndRoleAssignResp> adminAndRoleAssignResps =
            CopierUtil.copyList(allAdminList, AdminAndRoleAssignResp.class);
        return ResultVO.of(permRoleService.putIsPuckedRole(record.getId(), adminAndRoleAssignResps));
    }

    @ApiOperation("根据角色id获取已分配的用户Ids")
    @PostMapping("getUserIdsByRoleId")
    public ResultVO<List<Long>> getUserIdsByRoleId(@RequestBody IdReq record) {
        AssertUtils.checkNotBlank(String.valueOf(record.getId()), "id不能为空");
        return ResultVO.of(permRoleService.getUserIdsByRoleId(record.getId()));
    }

    private @NotNull List<AdminAndTokenInfoResp> getAdminAndTokenInfoResps(AdminInfoGetReq req) {
        List<AdminVO> values = getAdminVOS(req);

        List<AdminAndTokenInfoResp> admins = CopierUtil.copyList(values, AdminAndTokenInfoResp.class);

        userTokenConfigService.putTokenInfo(admins);

        permUserRoleService.putRoles(admins, true);

        admins.forEach(e -> e
            .setRolesNameStr(e.getRoles().stream().map(PermRoleResp::getRoleName).collect(Collectors.joining(","))));
        admins.forEach(e -> e.setRoles(null));

        return admins;
    }

    private @Nullable List<AdminVO> getAdminVOS(AdminInfoGetReq req) {
        List<AdminVO> values = super.getAllAdminList();
        if (ObjectUtils.isEmpty(values)) {
            return values;
        }

        values = values.stream()
            .filter(adminVO -> adminVO.getName().contains(req.getName() == null ? "" : req.getName().trim()))
            .filter(adminVO -> {
                if (adminVO.getMobile() == null) {
                    adminVO.setMobile("");
                }

                return adminVO.getMobile().contains(req.getMobile() == null ? "" : req.getMobile().trim());
            }).collect(Collectors.toList());
        return values;
    }




}
