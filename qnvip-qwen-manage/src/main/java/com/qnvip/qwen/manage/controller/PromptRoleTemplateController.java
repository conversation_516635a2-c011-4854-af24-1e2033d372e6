package com.qnvip.qwen.manage.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qnvip.common.base.ResultVO;
import com.qnvip.common.util.AssertUtils;
import com.qnvip.qwen.dal.dto.PromptRoleTemplateQueryDTO;
import com.qnvip.qwen.service.PromptRoleTemplateService;
import com.qnvip.qwen.vo.request.PromptRoleTemplateReq;
import com.qnvip.qwen.vo.response.PromptRoleTemplateResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色提示词模板表控制器
 *
 * <AUTHOR>
 * @description powered by qnvip
 * @date 2025/06/12 13:24
 */
@Api(tags = "角色提示词模板表")
@RestController
@RequestMapping(value = "/api/promptRoleTemplate")
public class PromptRoleTemplateController {

    @Resource
    private PromptRoleTemplateService promptRoleTemplateService;

    @ApiOperation(value = "角色提示词模板表分页搜索")
    @PostMapping("page")
    public ResultVO<List<PromptRoleTemplateResp>> page(@RequestBody PromptRoleTemplateQueryDTO record) {
        return ResultVO.pageOf(promptRoleTemplateService.page(record));
    }

    @ApiOperation("角色提示词模板表详情")
    @PostMapping("detail")
    public ResultVO<PromptRoleTemplateResp> detail(@RequestBody PromptRoleTemplateReq record) {
        AssertUtils.checkNotBlank(String.valueOf(record.getId()), "id不能为空");
        return ResultVO.of(promptRoleTemplateService.detail(record.getId()));
    }

    @ApiOperation("角色提示词模板表新增或修改 无id为新增")
    @PostMapping("save")
    public ResultVO<Boolean> save(@RequestBody PromptRoleTemplateReq record) {
        return ResultVO.of(promptRoleTemplateService.save(record));
    }

    @ApiOperation("角色提示词模板表删除")
    @PostMapping("delete")
    public ResultVO<Boolean> delete(@RequestBody PromptRoleTemplateReq record) {
        AssertUtils.checkNotBlank(String.valueOf(record.getId()), "id不能为空");
        return ResultVO.of(promptRoleTemplateService.delete(record.getId()));
    }

}