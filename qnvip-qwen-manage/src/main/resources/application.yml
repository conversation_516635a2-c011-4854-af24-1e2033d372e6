server:
  port: 6014           # 应用端口
  shutdown: graceful

spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: '@spring.profiles.active@'
  application:
    name: qnvip-qwen-app
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  data:
    neo4j:
      uri: bolt://***********:7687
      username: neo4j
      password: password

management:
  server:
    port: 16022
  endpoints:
    web:
      exposure:
        include: [ 'health', 'info', 'loggers', 'prometheus','shutdown' ]
  endpoint:
    health:
      show-details: ALWAYS  #显示详细信息
    shutdown:
      enabled: true
  health:
    elasticsearch:
      enabled: false


logging.file.name: ./logs/${spring.application.name}.log



