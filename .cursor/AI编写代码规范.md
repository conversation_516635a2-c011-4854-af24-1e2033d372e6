# AI代码规范

## 本项目代码规范

1. XXXReq,XXXResp 的vo模型存到service中

2. 生成的所有的接口使用Post请求，并且使用XXXReq作为入参模型

3. LambdaQueryWrapper<T>相关的代码 只能存在对应的 extends BaseServiceImpl<PermRoleResourceMapper, T>的TService的实现类中
   例子1. LambdaQueryWrapper<User> 要写到 UserServiceImpl
   例子2. LambdaQueryWrapper<Role> 要写到 RoleServiceImpl
   例子3. LambdaQueryWrapper<UserRole> 要写到 UserRoleServiceImpl

4. 所有接口使用Post json格式

5. 解析json使用fastjson2的类，如果json字段是下划线命名，则转成java的驼峰命名后用JSONField注解原字段

6. 不适用com.google.gson，使用com.alibaba.fastjson2.JSON

## 项目通用代码规范

1. 项目架构规范：
   - 采用分层架构：controller -> service -> dao
   - 使用 MyBatis-Plus 作为 ORM 框架
   - 使用 Spring Boot 作为基础框架

2. 命名规范：
   - 请求对象以 `Req` 结尾（如 `RoleBookReq`）
   - 响应对象以 `Resp` 结尾（如 `PermRoleResourceResp`）
   - 数据对象以 `DO` 结尾（如 `PermRoleResourceDO`）
   - 查询对象以 `QueryDTO` 结尾（如 `PermRoleResourceQueryDTO`）
   - 数据传输对象以 `DTO` 结尾（如 `PermRoleResourceDTO`）

3. 接口规范：
   - 所有接口统一使用 POST 请求
   - 请求参数必须使用 `@RequestBody` 注解
   - 返回结果统一使用 `ResultVO` 包装
   - 接口必须添加 Swagger 注解（`@Api`、`@ApiOperation`）

4. 代码组织规范：
   - VO 对象存放在 service 模块中
   - 数据库操作相关的代码只能在对应的 Service 实现类中
   - 使用 `LambdaQueryWrapper` 进行数据库查询
   - 使用 `CopierUtil` 进行对象复制

5. 异常处理规范：
   - 使用 `AssertUtils` 进行参数校验
   - 业务异常需要明确的错误提示
3. LambdaQueryWrapper<T>相关的代码 只能存在对应的 extends BaseServiceImpl<PermRoleResourceMapper, T>的TService的实现类中
   例子1. LambdaQueryWrapper<User> 要写到 UserServiceImpl
   例子2. LambdaQueryWrapper<Role> 要写到 RoleServiceImpl
   例子3. LambdaQueryWrapper<UserRole> 要写到 UserRoleServiceImpl

6. 分页查询规范：
   - 使用 `Pageable` 作为分页结果包装类
   - 使用 `MybatisPlusUtils` 进行分页参数转换

7. 资源类型规范：
   - 资源类型使用 Integer 类型（而不是 String）
   - 需要定义资源类型的常量

8. 依赖管理规范：
   - 使用 Maven 进行依赖管理
   - 项目分为多个模块：acl、manage、service 等

9. 注释规范：
   - 类必须包含作者、描述、日期信息
   - 方法必须包含功能说明
   - 参数必须包含说明注释

10. 代码格式规范：
- 使用 Lombok 简化代码
- 使用 `@Resource` 进行依赖注入
- 使用 `@Service` 标注服务类
 