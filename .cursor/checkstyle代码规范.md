# 代码规范 (基于checkStyle.xml)

## 文件规范

1. 文件编码使用UTF-8
2. 禁止使用制表符(\t)，必须使用空格缩进
3. Java文件长度不超过1500行
4. 文件名必须与外部类型名称匹配(如类Foo必须在Foo.java中)

## 命名规范

1. 类/接口名：大驼峰式，符合`^[A-Z][a-zA-Z0-9]*$`模式
2. 方法参数名、局部变量名：小驼峰式
3. 常量名：全大写，单词间用下划线分隔
4. 类型参数名(泛型)：单一大写字母
5. 缩写词限制：连续大写字母不超过6个
6. catch参数名：简短描述性名称(如e, ex, t等)

## 代码结构

1. 必须包含包声明
2. 嵌套if深度不超过5层
3. 避免嵌套代码块
4. 代码块必须使用大括号
5. 每个变量声明独占一行
6. 每行只允许一条语句

## 编码实践

1. 避免使用星号导入(`.*`)
2. 避免静态导入(除测试框架外)
3. 禁止非法导入(如sun.*包)
4. 删除冗余导入
5. 修饰符顺序符合Java规范
6. 数组初始化推荐使用末尾逗号
7. 重写equals时必须同时重写hashCode
8. switch语句中default放在最后
9. 字符串比较使用equals()而非==
10. 避免不必要的对象实例化
11. 禁止修改循环控制变量
12. 避免复杂的布尔表达式
13. 简化不必要的布尔返回
14. 禁止使用finalize()方法
15. 禁止重写clone()方法

## 异常处理

1. 禁止空的catch块(除非有注释或变量名为expected/ignore)
2. 禁止空的代码块

## 注释规范

1. 支持使用特殊注释暂时禁用检查规则

## 代码风格

1. long型常量使用大写的L后缀(如100L)

## 特别说明

1. 测试代码允许特定的静态导入(JUnit, Mockito等)
2. 部分规则可根据项目实际情况调整(如方法长度、行长度等)

该规范旨在提高代码一致性、可读性和可维护性，建议结合团队实际情况适当调整后使用。