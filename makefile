

# 测试环境项目部署打包
package-qa-${rootArtifactId}:
	mvn -B clean package -Dautoconfig.skip -pl ${rootArtifactId}-app -am
# 生产环境项目部署打包
package-prod-${rootArtifactId}:
	mvn -B clean package -DskipTests -Dautoconfig.skip -Pprod -pl ${rootArtifactId}-app -am

# 测试环境jar包上传maven仓库
deploy-qa-${rootArtifactId}-client:
	mvn -B clean deploy -Dautoconfig.skip -pl ${rootArtifactId}-client -am

# 生产环境jar包上传maven仓库
deploy-prod-${rootArtifactId}-client:
	mvn -B clean deploy -DskipTests -Dautoconfig.skip -Pprod -pl ${rootArtifactId}-client -am