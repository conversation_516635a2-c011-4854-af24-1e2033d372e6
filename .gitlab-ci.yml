stages:
  - build

variables:
  MAVEN_IMAGE: maven:3.6.3-openjdk-8
  SONAR_IMAGE: sonarsource/sonar-scanner-cli
  SONAR_HOST_URL: http://sonar-test.qnvip.io/
  SONAR_LOGIN: ****************************************
  SONAR_PROJECT_KEY: ${rootArtifactId}

build:
  stage: build
  image: $MAVEN_IMAGE
  before_script:
    - mkdir -p /root/.m2/repository
  script:
    - mvn clean compile sonar:sonar -Dmaven.test.skip=true -Dsonar.login=${SONAR_LOGIN} -Dsonar.host.url=${SONAR_HOST_URL} -Dsonar.projectKey=${SONAR_PROJECT_KEY} -Prdc -U
  interruptible: true
  tags:
    - sonar
  only:
    - develop
