<?xml version="1.0"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://checkstyle.sourceforge.net/dtds/configuration_1_3.dtd">
<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="error"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- 每行不超过200个字符 -->
<!--    <module name="LineLength">-->
<!--        <property name="max" value="150"/>-->
<!--    </module>-->
    <module name="Header">
        <!-- 指定要处理的文件的文件类型扩展名为java。 -->
        <property name="fileExtensions" value="java"/>
    </module>
    <!-- 检查文件中是否含有'\t' -->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>
    <!-- 文件长度不超过1500行 -->
    <module name="FileLength">
        <property name="max" value="1500"/>
    </module>
    <!-- 检查property文件中是否有相同的key -->
    <module name="Translation"/>
    <module name="UniqueProperties"/>

    <!-- 每个java文件一个语法树 -->
    <module name="TreeWalker">
        <!-- Class或Interface名检查，默认^[A-Z][a-zA-Z0-9]*$-->
        <module name="TypeName">
            <property name="severity" value="warning"/>
            <message key="name.invalidPattern" value="名称 ''{0}'' 要符合 ''{1}''格式."/>
        </module>
        <!-- 检查方法名称是否符合指定的模式，默认"^[a-z][a-zA-Z0-9]*$" -->
        <!--<module name="MethodName"/>-->
        <!-- 检查接口类型参数名是否符合指定的模式，默认"^[A-Z]$" -->
        <module name="InterfaceTypeParameterName"/>
        <!-- 检查类类型参数名是否符合指定的模式，默认"^[A-Z]$" -->
        <module name="ClassTypeParameterName"/>
        <!-- 检查方法类型参数名称是否符合指定的模式，默认"^[A-Z]$"-->
        <module name="MethodTypeParameterName"/>
        <!-- 检查常量名称是否符合指定的模式，默认"^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$" -->
<!--        <module name="ConstantName"/>-->
        <!-- 检查静态的非final变量名是否符合指定的模式，默认"^[a-z][a-zA-Z0-9]*$" -->
<!--        <module name="StaticVariableName"/>-->
        <!-- 检查实例变量名称是否符合指定的模式，默认"^[a-z][a-zA-Z0-9]*$" -->
        <module name="MemberName"/>
        <!-- 检查局部的非final变量名是否符合指定的模式。catch参数被认为是一个局部变量，默认"^[a-z][a-zA-Z0-9]*$" -->
        <module name="LocalVariableName"/>
        <!-- 检查局部final变量名称是否符合指定的模式。try语句中的catch参数和资源被认为是局部的、最终的变量，默认"^[a-z][a-zA-Z0-9]*$"-->
        <module name="LocalFinalVariableName"/>
        <!-- 检查方法参数名称是否符合指定的模式，默认"^[a-z][a-zA-Z0-9]*$" -->
        <module name="ParameterName"/>
        <!-- 检查catch参数名是否符合指定的模式，默认"^(e|t|ex|[a-z][a-z][a-zA-Z]+)$" -->
        <module name="CatchParameterName"/>
        <!-- 验证标识符名称中的缩写(连续大写字母)长度。-->
        <module name="AbbreviationAsWordInName">
            <!-- 指出目标标识符(类、接口、变量和方法名称中的缩写，等等)中允许连续大写字母的数量。 -->
            <property name="allowedAbbreviationLength" value="6"/>
        </module>

        <!-- 长度检查 -->
        <!-- 检查长方法和构造函数，默认最大150 -->
        <!--<module name="MethodLength"/>-->


        <!-- import检查-->
        <!-- 避免使用* -->
        <module name="AvoidStarImport"/>
        <!-- 避免使用静态导入 -->
        <module name="AvoidStaticImport">
            <property name="excludes"
                      value="org.junit.Assert.*,org.hamcrest.CoreMatchers.*,org.mockito.Mockito.*,org.mockito.ArgumentMatchers.*"/>
        </module>
        <!-- 检查是否从非法的包中导入了类 -->
        <module name="IllegalImport"/>
        <!-- 检查冗余的导入语句。-->
        <module name="RedundantImport"/>

        <!-- 修饰符检查 -->
        <!-- 检查修饰符的顺序是否遵照java语言规范，默认public、protected、private、abstract、default、static、final、transient、volatile、synchronized、native、strictfp -->
        <module name="ModifierOrder"/>
        <!-- 检查接口和annotation中是否有多余修饰符，如接口方法不必使用public -->
<!--        <module name="RedundantModifier"/>-->

        <!-- Coding -->
        <!-- 检查数组初始化是否包含末尾逗号。 -->
        <module name="ArrayTrailingComma"/>
        <!-- 检查类是否覆盖了equals(java.lang.Object)。 -->
        <module name="CovariantEquals"/>
        <!-- 检查default是否在switch语句中的所有case之后。 -->
        <module name="DefaultComesLast"/>
        <!-- 检查字符串字面值的任何组合是否位于equals()比较的左侧。还检查分配给某些字段的字符串字面量(例如someString。= (anotherString =“文本”))。 -->
        <module name="EqualsAvoidNull"/>
        <!-- 检查优先使用工厂方法的非法实例化。 -->
        <module name="IllegalInstantiation"/>
        <!-- 检查特定的类或接口是否从未被使用。-->
        <module name="IllegalType">
            <!-- 方法、参数、变量-->
            <property name="tokens" value="METHOD_DEF,PARAMETER_DEF,VARIABLE_DEF"/>
        </module>
        <!-- 检查for循环控制变量是否在for块中被修改。-->
        <module name="ModifiedControlVariable"/>
        <!-- 检查每个变量声明是否在自己的语句中并在自己的行中。-->
        <module name="MultipleVariableDeclarations"/>
        <!-- 将嵌套的if-else块限制到指定的深度。 -->
        <module name="NestedIfDepth">
            <property name="max" value="5"/>
        </module>
        <!-- 检查是否从Object 类中重写克隆方法。 -->
        <module name="NoClone"/>
        <!-- 检查没有带零个参数的方法finalize。 -->
        <module name="NoFinalizer"/>
        <!-- 检查每行是否只有一条语句。-->
        <module name="OneStatementPerLine"/>
        <!-- 检查是否将重载方法分组在一起。重载的方法具有相同的名称但不同的签名，其中签名可以因输入参数的数量或输入参数的类型或两者的数量而不同。-->
<!--        <module name="OverloadMethodsDeclarationOrder"/>-->
        <!-- 确保类有包声明，以及(可选的)包名是否与源文件的目录名匹配。-->
        <module name="PackageDeclaration"/>
        <!-- 检查过于复杂的布尔表达式。目前发现代码，如if (b == true)， b || true， !false，等等。-->
        <module name="SimplifyBooleanExpression"/>
        <!-- 检查过于复杂的布尔返回语句。 -->
        <module name="SimplifyBooleanReturn"/>
        <!-- 检查字符串字面值是否与==或!=一起使用。因为==将比较对象引用，而不是字符串的实际值，所以应该使用String.equals()。-->
        <module name="StringLiteralEquality"/>
        <!-- 检查语句或表达式中是否使用了不必要的括号。-->
<!--        <module name="UnnecessaryParentheses"/>-->

        <!-- 代码块检查 -->
        <!-- 检查空块。此检查不验证顺序块。 -->
        <module name="EmptyBlock"/>
        <!-- 检查空的catch块。默认情况下，check允许包含任何注释的空catch块。 -->
        <module name="EmptyCatchBlock">
            <!-- 如果需要异常的变量名，或者忽略，或者有任何注释，则配置检查来抑制空catch块-->
            <property name="exceptionVariableName" value="expected|ignore"/>
        </module>
        <!-- 查找嵌套块(代码中自由使用的块)。 -->
        <module name="AvoidNestedBlocks"/>
        <!-- 检查代码块周围的括号。 -->
        <module name="NeedBraces"/>
        <!-- 检查代码块的左花括号('{')位置。 -->
<!--        <module name="RightCurly"/>-->

        <!-- 类设计检查 -->
        <!-- 检查只有private构造函数的类是否声明为final -->
        <!--<module name="FinalClass"/>-->
        <!-- cannot recognize for lombok @NoArgsConstructor(access = AccessLevel.PRIVATE), just ignore -->
        <!--<module name="HideUtilityClassConstructor"/>-->
        <!-- 嵌套的(内部的)类/接口在初始化和静态初始化块、方法、构造函数和字段声明之后，在主(顶级)类的底部声明。-->
        <!--<module name="InnerTypeLast"/>-->
        <!-- 检查类成员的可见性。只有static final、不可变或由指定注释成员注释的才可以是public;除非设置了protectedAllowed或packageAllowed属性，否则其他类成员必须是私有的。-->
        <!--<module name="VisibilityModifier"/>-->

        <!-- 定义检查 -->
        <!-- 检查数组类型定义的样式 -->
        <!--<module name="ArrayTypeStyle"/>-->
        <!-- 检查long型定义是否有大写的“L” -->
        <module name="UpperEll"/>
        <!-- 检查外部类型名称和文件名是否匹配。例如，类Foo必须在一个名为Foo.java的文件中。-->
        <module name="OuterTypeFilename"/>


        <!-- Filters -->
        <module name="SuppressionCommentFilter"/>
        <module name="SuppressWithNearbyCommentFilter"/>
    </module>
</module>